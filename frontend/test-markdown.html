<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown测试</title>
</head>
<body>
    <h1>Markdown渲染测试</h1>
    
    <h2>文本格式</h2>
    <p>这是一个包含 <code>行内代码</code> 的段落。</p>
    <p><strong>粗体文本</strong> 和 <em>斜体文本</em> 以及</p>
    
    <h2>列表</h2>
    
    <h3>无序列表</h3>
    <ul>
        <li>第一项</li>
        <li>第二项</li>
        <li>嵌套项目
            <ul>
                <li>另一个嵌套项目</li>
            </ul>
        </li>
        <li>第三项</li>
    </ul>
    
    <h3>有序列表</h3>
    <ol>
        <li>首先</li>
        <li>然后</li>
        <li>最后</li>
    </ol>
    
    <h2>代码块</h2>
    <pre><code>function test() {
    console.log("Hello World");
}</code></pre>
    
    <h2>引用</h2>
    <blockquote>
        <p>这是一个引用块。它可以包含多行内容。</p>
    </blockquote>
    
</body>
</html>
