// src/features/chat/index.tsx
import { useEffect, useRef, useCallback, useState } from 'react';
import { useChat } from './hooks/useChat';
import { useAutoScroll } from './hooks/useAutoScroll';
import { LogStream } from './components/LogStream';
import { WelcomeScreen } from './components/WelcomeScreen';
import { AnimatedFooter } from './components/AnimatedFooter';
import { ScrollToBottomButton } from './components/ScrollToBottomButton';

export const ChatView = () => {
  const { messages, sendMessage, isThinking, hasStarted } = useChat();
  const footerRef = useRef<HTMLDivElement>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // 使用自动滚动hook
  const {
    scrollContainerRef,
    showScrollButton,
    userHasScrolled,
    scrollToBottom,
    isAtBottom,
    updateScrollState
  } = useAutoScroll({
    threshold: 100,
    debug: false // 可以设置为true来启用调试日志
  });

  // 智能滚动逻辑：发送消息后立即滚动到底部
  const handleSendMessage = useCallback((message: string) => {
    // 如果是第一条消息，标记为过渡状态
    if (messages.length === 0) {
      setIsTransitioning(true);
      // 过渡动画完成后重置状态
      setTimeout(() => {
        setIsTransitioning(false);
      }, 1000);
    }

    sendMessage(message);
    // 发送消息后滚动到底部（使用微妙的平滑动画）
    setTimeout(() => {
      scrollToBottom('smooth');
    }, 50);
  }, [sendMessage, scrollToBottom, messages.length]);

  // 监听消息变化，实现智能滚动
  useEffect(() => {
    if (messages.length === 0) return;

    const lastMessage = messages[messages.length - 1];

    // 如果是AI消息且正在思考或流式输出，只有在用户处于底部时才自动滚动
    if (lastMessage.role === 'xi' && (isThinking || lastMessage.content)) {
      if (!userHasScrolled && isAtBottom()) {
        scrollToBottom('smooth');
      }
    }
  }, [messages, isThinking, userHasScrolled, isAtBottom, scrollToBottom]);

  if (!hasStarted) {
    return (
      <WelcomeScreen
        onSendMessage={handleSendMessage}
        disabled={isThinking}
      />
    );
  }

  // 对话状态：正常布局
  return (
    <div className="h-screen bg-background text-foreground font-sans relative">
      {/* 滚动区域 - 只包含消息内容 */}
      <div
        ref={scrollContainerRef}
        className="h-full pb-32 overflow-y-auto"
        style={{ 
          scrollBehavior: 'auto' // 移除CSS的smooth，由JS控制
        }}
      >
        <div className="flex justify-center">
          <LogStream messages={messages} isThinking={isThinking} />
        </div>
      </div>

      {/* 固定在底部的输入框 - 完全独立 */}
      <div
        ref={footerRef}
        className="absolute bottom-0 left-0 right-0"
      >
        <AnimatedFooter
          onSendMessage={handleSendMessage}
          disabled={isThinking}
          messagesCount={messages.length}
          isTransitioning={isTransitioning}
        />
      </div>

      {/* 滚动到底部按钮 */}
      <ScrollToBottomButton
        show={showScrollButton}
        onClick={() => scrollToBottom('smooth')}
      />
    </div>
  );
};