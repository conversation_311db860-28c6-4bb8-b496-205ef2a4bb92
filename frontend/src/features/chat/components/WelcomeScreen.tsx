// src/features/chat/components/WelcomeScreen.tsx
// 欢迎界面组件 - 负责初始状态的展示
import React from 'react';
import { motion } from 'framer-motion';
import { ChatInput } from './ChatInput';

interface WelcomeScreenProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ 
  onSendMessage, 
  disabled = false 
}) => {
  return (
    <div className="h-screen bg-background text-foreground font-sans flex flex-col items-center justify-center -translate-y-16">
      <motion.h1
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="text-4xl font-light text-secondary-foreground tracking-[0.2em] mb-12"
      >
        YX NEXUS
      </motion.h1>
      <ChatInput
        onSendMessage={onSendMessage}
        disabled={disabled}
        isInitialState={true}
      />
    </div>
  );
};
