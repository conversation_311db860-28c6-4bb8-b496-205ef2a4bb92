// src/features/chat/components/AnimatedFooter.tsx
// 动画输入框容器 - 处理从中央到底部的过渡动画
import React from 'react';
import { motion } from 'framer-motion';
import { ChatInput } from './ChatInput';

interface AnimatedFooterProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  messagesCount: number;
}

export const AnimatedFooter: React.FC<AnimatedFooterProps> = ({ 
  onSendMessage, 
  disabled = false,
  messagesCount 
}) => {
  return (
    <motion.footer
      layout
      initial={{ y: messagesCount === 1 ? "-40vh" : 0 }}
      animate={{ y: 0 }}
      transition={{
        duration: 0.6,
        ease: [0.22, 1, 0.36, 1],
        type: "tween"
      }}
      className="w-full p-6 bg-transparent"
    >
      <ChatInput
        onSendMessage={onSendMessage}
        disabled={disabled}
        isInitialState={false}
      />
    </motion.footer>
  );
};
