// src/features/chat/components/AnimatedFooter.tsx
// 动画输入框容器 - 处理从中央到底部的过渡动画
import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { ChatInput } from './ChatInput';

interface AnimatedFooterProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  messagesCount: number;
  isTransitioning?: boolean;
}

export const AnimatedFooter: React.FC<AnimatedFooterProps> = ({
  onSendMessage,
  disabled = false,
  messagesCount,
  isTransitioning = false
}) => {
  // 使用传入的过渡状态，或者基于消息数量判断
  const shouldAnimate = isTransitioning || messagesCount === 1;

  return (
    <motion.footer
      layout
      initial={{
        y: shouldAnimate ? "calc(-50vh + 3rem)" : 0,
        opacity: shouldAnimate ? 0.8 : 1
      }}
      animate={{
        y: 0,
        opacity: 1
      }}
      transition={{
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1],
        type: "tween"
      }}
      className="w-full p-6 bg-transparent"
    >
      <ChatInput
        onSendMessage={onSendMessage}
        disabled={disabled}
        isInitialState={false}
      />
    </motion.footer>
  );
};
