// src/features/chat/components/ChatInput.tsx
import { ArrowUp } from 'lucide-react';
import { useRef, useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/Button';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  isInitialState?: boolean;
}

export const ChatInput: React.FC<ChatInputProps> = ({ onSendMessage, disabled = false, isInitialState = false }) => {
  // 注意：isInitialState 参数保留用于接口兼容性，但现在所有状态下都使用统一样式
  const [message, setMessage] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const [initialHeight, setInitialHeight] = useState<number>(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 获取初始高度
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea && initialHeight === 0) {
      // 确保获取到真实的初始高度
      textarea.style.height = 'auto';
      const naturalHeight = textarea.scrollHeight;
      setInitialHeight(naturalHeight);
      textarea.style.height = `${naturalHeight}px`;
    }
  }, [initialHeight]);

  // 自动调整 textarea 高度，限制最大高度为初始高度的2倍
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea && initialHeight > 0) {
      textarea.style.height = 'auto';
      const scrollHeight = textarea.scrollHeight;
      const maxHeight = initialHeight * 2; // 最大高度为初始高度的2倍

      if (scrollHeight <= maxHeight) {
        textarea.style.height = `${Math.max(scrollHeight, initialHeight)}px`;
        textarea.style.overflowY = 'hidden';
      } else {
        textarea.style.height = `${maxHeight}px`;
        textarea.style.overflowY = 'auto';
      }
    }
  }, [message, initialHeight]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !disabled && !isComposing) {
      onSendMessage(message.trim());
      setMessage('');

      // 重置 textarea 高度和滚动
      if (textareaRef.current && initialHeight > 0) {
        textareaRef.current.style.height = `${initialHeight}px`;
        textareaRef.current.style.overflowY = 'hidden';
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const canSend = message.trim().length > 0 && !disabled && !isComposing;

  return (
    <div className="w-full max-w-2xl mx-auto">
      <form onSubmit={handleSubmit} className="relative">
        <div className={cn(
          'relative rounded-2xl border border-border shadow-lg shadow-black/20',
          'bg-card/75 backdrop-blur-xl',
          'transition-colors duration-200',
          'hover:border-foreground/20'
        )}>
          <textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            onCompositionStart={() => setIsComposing(true)}
            onCompositionEnd={() => setIsComposing(false)}
            placeholder="继续探索..."
            disabled={disabled}
            className={cn(
              'w-full bg-transparent text-foreground placeholder:text-secondary-foreground',
              'px-4 pr-12 text-base leading-relaxed resize-none border-none outline-none focus:ring-0',
              'py-4 min-h-[70px]' // 统一使用初始状态的样式
            )}
            rows={3} // 统一使用初始状态的行数
          />
          <Button
            type="submit"
            variant="primary"
            size="md"
            icon={<ArrowUp size={18} />}
            iconOnly
            disabled={!canSend}
            className="absolute right-2 bottom-2"
          />
        </div>
      </form>
    </div>
  );
};