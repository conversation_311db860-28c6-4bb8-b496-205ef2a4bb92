// src/features/chat/components/ChatMessage.tsx
// 重构的消息组件 - 使用符号标识，优化布局和思考动画
import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { MarkdownRenderer } from '@/components/ui/MarkdownRenderer';
import { Timestamp } from '@/components/ui/Timestamp';
import type { Message } from '../types';

interface ChatMessageProps {
  message: Message;
  isLastMessage: boolean;
  isThinking: boolean;
}

// 角色符号映射 - 独立一列显示，尺寸增大
const RoleSymbol: React.FC<{ role: Message['role']; isThinking?: boolean }> = ({ role, isThinking = false }) => {
  const symbols = {
    yu: '▲',
    xi: '●',
    system: '■'
  };

  return (
    <motion.div
      className={cn(
        'flex items-center justify-center w-8 h-8',
        'text-secondary-foreground text-lg font-mono select-none',
        'flex-shrink-0', // 防止收缩
        isThinking && 'animate-pulse'
      )}
      animate={isThinking ? { opacity: [0.4, 1, 0.4] } : {}}
      transition={isThinking ? { duration: 2, repeat: Infinity, ease: 'easeInOut' } : {}}
    >
      {symbols[role]}
    </motion.div>
  );
};

export const ChatMessage: React.FC<ChatMessageProps> = ({ message, isLastMessage, isThinking }) => {
  const isXiThinking = message.role === 'xi' && isLastMessage && isThinking;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, ease: 'easeOut' }}
      className="group relative py-6 flex items-start gap-4"
      data-message-id={message.id}
    >
      {/* 左侧：角色符号独立一列 */}
      <RoleSymbol role={message.role} isThinking={isXiThinking} />

      {/* 右侧：消息内容区域 */}
      <div className="flex-1 min-w-0 relative">
        {/* 时间戳 - 悬停显示，右上角 */}
        <div className="absolute top-0 right-0">
          <Timestamp
            date={new Date(message.timestamp)}
            format="smart"
            showOnHover={true}
          />
        </div>

        {/* 消息内容 */}
        <div className="pr-16"> {/* 右边距为时间戳留出空间 */}
          <MarkdownRenderer content={message.content} />
        </div>
      </div>
    </motion.div>
  );
};
