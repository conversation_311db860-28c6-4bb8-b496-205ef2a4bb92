// src/features/chat/store/chatStore.ts
import { create } from 'zustand';
import type { Message } from '../types';

interface ChatState {
  messages: Message[];
  isThinking: boolean;
  hasStarted: boolean;
  addMessage: (message: Message) => void;
  appendStreamChunk: (id: string, chunk: string, isFinished: boolean) => void;
  setThinking: (isThinking: boolean) => void;
  setInitialMessages: (messages: Message[]) => void;
  setHasStarted: (hasStarted: boolean) => void;
}

export const useChatStore = create<ChatState>((set) => ({
  messages: [],
  isThinking: false,
  hasStarted: false,

  setInitialMessages: (messages) => set({ messages }),

  addMessage: (message) => set((state) => ({
    messages: [...state.messages, message],
  })),

  appendStreamChunk: (id, chunk, isFinished) => set((state) => ({
    messages: state.messages.map(msg => {
      if (msg.id === id) {
        const newContent = msg.content + chunk;
        return { ...msg, content: newContent };
      }
      return msg;
    }),
  })),

  setThinking: (isThinking) => set({ isThinking }),

  setHasStarted: (hasStarted) => set({ hasStarted }),
}));
