// src/features/chat/hooks/useAutoScroll.ts
// 自动滚动管理hook - 处理聊天界面的智能滚动逻辑
import { useRef, useCallback, useState, useEffect } from 'react';

interface UseAutoScrollOptions {
  /**
   * 判断是否接近底部的阈值（像素）
   * 当距离底部小于此值时，认为用户在底部
   */
  threshold?: number;
  /**
   * 是否启用调试日志
   */
  debug?: boolean;
}

interface UseAutoScrollReturn {
  /** 滚动容器的ref */
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
  /** 是否显示滚动到底部按钮 */
  showScrollButton: boolean;
  /** 用户是否手动滚动过（脱离底部） */
  userHasScrolled: boolean;
  /** 滚动到底部的函数 */
  scrollToBottom: (behavior?: ScrollBehavior) => void;
  /** 检查是否在底部 */
  isAtBottom: () => boolean;
  /** 强制更新滚动状态 */
  updateScrollState: () => void;
}

export const useAutoScroll = (options: UseAutoScrollOptions = {}): UseAutoScrollReturn => {
  const { threshold = 100, debug = false } = options;
  
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [userHasScrolled, setUserHasScrolled] = useState(false);
  
  // 调试日志函数
  const log = useCallback((message: string, ...args: any[]) => {
    if (debug) {
      console.log(`[useAutoScroll] ${message}`, ...args);
    }
  }, [debug]);

  // 检查是否在底部
  const isAtBottom = useCallback((): boolean => {
    const container = scrollContainerRef.current;
    if (!container) return false;

    const { scrollTop, scrollHeight, clientHeight } = container;
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
    const atBottom = distanceFromBottom <= threshold;
    
    log(`检查底部状态: 距离底部 ${distanceFromBottom}px, 阈值 ${threshold}px, 在底部: ${atBottom}`);
    return atBottom;
  }, [threshold, log]);

  // 滚动到底部
  const scrollToBottom = useCallback((behavior: ScrollBehavior = 'smooth') => {
    const container = scrollContainerRef.current;
    if (!container) {
      log('滚动容器不存在，无法滚动');
      return;
    }

    log(`滚动到底部，行为: ${behavior}`);
    container.scrollTo({
      top: container.scrollHeight,
      behavior
    });

    // 滚动后重置用户滚动状态
    setTimeout(() => {
      setUserHasScrolled(false);
      setShowScrollButton(false);
    }, behavior === 'smooth' ? 300 : 0);
  }, [log]);

  // 更新滚动状态
  const updateScrollState = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const atBottom = isAtBottom();
    const shouldShowButton = !atBottom;
    
    log(`更新滚动状态: 在底部 ${atBottom}, 显示按钮 ${shouldShowButton}`);
    
    setShowScrollButton(shouldShowButton);
    setUserHasScrolled(!atBottom);
  }, [isAtBottom, log]);

  // 处理滚动事件
  const handleScroll = useCallback(() => {
    updateScrollState();
  }, [updateScrollState]);

  // 监听滚动事件
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    container.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  return {
    scrollContainerRef,
    showScrollButton,
    userHasScrolled,
    scrollToBottom,
    isAtBottom,
    updateScrollState
  };
};
