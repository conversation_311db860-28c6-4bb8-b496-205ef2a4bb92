// src/features/chat/hooks/useChat.ts
// 重构的聊天Hook - 简化模拟逻辑，优化思考动画
import { useEffect, useRef } from 'react';
import { useChatStore } from '../store/chatStore';
import wsManager from '@/services/websocket/manager';
import type { Message } from '../types';
import { v4 as uuidv4 } from 'uuid';
import { createMockMessage } from '../data/mockData';
import { mockResponses } from '../data/mockResponses';

export const useChat = () => {
  const { messages, addMessage, appendStreamChunk, setThinking, setInitialMessages, isThinking, hasStarted, setHasStarted } = useChatStore();
  const currentStreamId = useRef<string | null>(null);

  // 检查是否使用模拟数据模式
  const useMockData = import.meta.env.VITE_USE_MOCK_DATA === 'true';

  useEffect(() => {
    // 模拟数据模式：不预加载任何消息，从空白界面开始
    if (useMockData) {
      console.log('🎭 模拟数据模式已启用 - 从空白界面开始');
      return;
    }

    // 正常模式：连接WebSocket
    // TODO: 使用新API加载初始消息
    // fetch('/api/v1/history/latest').then(...)

    wsManager.connect();

    wsManager.onMessage((data: string) => {
      if (data === '[STREAM_END]') {
        if (currentStreamId.current) {
          appendStreamChunk(currentStreamId.current, '', true);
        }
        currentStreamId.current = null;
        setThinking(false);
        return;
      }

      if (!currentStreamId.current) {
        const newXiMessage: Message = {
          id: uuidv4(),
          role: 'xi',
          content: data,
          timestamp: new Date().toISOString(),
        };
        addMessage(newXiMessage);
        currentStreamId.current = newXiMessage.id;
      } else {
        appendStreamChunk(currentStreamId.current, data, false);
      }
    });

    return () => {
      wsManager.disconnect();
    };
  }, [addMessage, appendStreamChunk, setThinking, setInitialMessages, useMockData]);

  const sendMessage = (text: string) => {
    // 如果这是第一次发送消息，设置 hasStarted 为 true
    if (!hasStarted) {
      setHasStarted(true);
    }

    const userMessage: Message = {
      id: uuidv4(),
      role: 'yu',
      content: text,
      timestamp: new Date().toISOString(),
    };
    addMessage(userMessage);
    setThinking(true);

    if (useMockData) {
      // 模拟数据模式：生成模拟响应
      simulateMockResponse(text);
    } else {
      // 正常模式：发送WebSocket消息
      wsManager.sendMessage({ yu_input: text });
    }
  };

  // 简化的模拟响应函数
  const simulateMockResponse = (userInput: string) => {
    const input = userInput.trim();

    // 获取响应内容
    const responseContent = typeof mockResponses[input as keyof typeof mockResponses] === 'string'
      ? mockResponses[input as keyof typeof mockResponses] as string
      : mockResponses.default(input);

    // 立即创建空的AI消息来显示思考动画
    const mockResponse = createMockMessage('xi', '');
    addMessage(mockResponse);
    currentStreamId.current = mockResponse.id;

    // 3秒后开始流式输出
    setTimeout(() => {
      // 模拟打字机效果
      let currentIndex = 0;
      const content = responseContent;

      const typeInterval = setInterval(() => {
        if (currentIndex < content.length) {
          const chunk = content.slice(currentIndex, currentIndex + 2);
          appendStreamChunk(mockResponse.id, chunk, false);
          currentIndex += 2;
        } else {
          clearInterval(typeInterval);
          appendStreamChunk(mockResponse.id, '', true);
          setThinking(false);
          currentStreamId.current = null;
        }
      }, 25); // 优化的打字速度
    }, 3000); // 3秒思考时间，展示思考动画
  };

  return { messages, sendMessage, isThinking, hasStarted };
};
