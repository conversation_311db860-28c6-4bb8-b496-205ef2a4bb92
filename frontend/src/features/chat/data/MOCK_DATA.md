# 前端模拟数据功能

## 概述

为了方便前端开发和测试界面效果，我们实现了一个完整的模拟数据系统。该系统可以在不依赖后端服务的情况下，提供丰富的对话数据和交互体验。

## 功能特性

### 🎭 交互式测试模式
- **空白界面启动**：从最初的悬浮指令核心开始
- **数字指令触发**：通过输入1-5触发不同测试场景
- **完整交互流程**：包含发送、思考、流式输出的完整体验
- **真实动画效果**：所有UI组件和过渡动画正常工作

### 🤖 智能测试响应
- **分类测试内容**：5种不同类型的专门测试响应
- **流式输出模拟**：完整的打字机效果和思考动画
- **组件功能验证**：每种响应测试不同的UI组件
- **性能测试支持**：长文本、复杂格式的渲染测试

### 🎨 全面UI测试覆盖
- **基础文本渲染**：Markdown、粗体、斜体、列表
- **代码语法高亮**：Python、JavaScript等多语言支持
- **表格和引用**：复杂格式元素的渲染测试
- **工具调用动画**：模拟搜索和能力使用的视觉效果
- **长文本滚动**：性能和用户体验测试

## 使用方法

### 快速切换模式

使用提供的脚本快速切换模拟数据模式：

```bash
# 启用模拟数据模式
node scripts/toggle-mock.cjs on

# 禁用模拟数据模式（使用真实后端）
node scripts/toggle-mock.cjs off

# 查看当前状态
node scripts/toggle-mock.cjs
```

### 手动配置

编辑 `.env` 文件：

```env
# 启用模拟数据
VITE_USE_MOCK_DATA=true

# 禁用模拟数据（默认）
VITE_USE_MOCK_DATA=false
```

## 测试指令说明

### 数字指令系统
在模拟数据模式下，输入以下数字可触发对应的测试场景：

| 指令 | 测试内容 | 验证功能 |
|------|----------|----------|
| **1** | 基础文本响应 | Markdown渲染、文本格式、列表显示 |
| **2** | 工具调用模拟 | 🛠️图标显示、工具调用动画效果 |
| **3** | 代码块渲染 | 语法高亮、多语言支持、代码格式 |
| **4** | 长文本滚动 | 滚动性能、响应式布局、内容层次 |
| **5** | 表格和引用 | 表格渲染、引用样式、链接效果 |
| **其他** | 默认帮助 | 显示所有可用测试指令 |

### 测试流程体验
每次输入数字指令后，系统会：
1. **发送消息**：用户消息立即显示
2. **思考动画**：曦的紫色光晕开始呼吸闪烁
3. **流式输出**：模拟真实的打字机效果
4. **动画结束**：思考状态重置，准备下次交互

## 开发优势

### 🚀 开发效率
- **独立开发**：无需启动后端服务
- **快速迭代**：即时查看界面效果
- **稳定测试**：不受后端API变化影响

### 🎯 测试覆盖
- **UI组件测试**：所有界面元素
- **动画效果测试**：过渡和交互动画
- **响应式测试**：不同设备适配
- **边界情况测试**：长文本、特殊字符等

### 🔧 调试便利
- **可控环境**：完全可预测的数据
- **快速重现**：一致的测试场景
- **性能测试**：纯前端性能评估

## 技术实现

### 数据结构
```typescript
interface Message {
  id: string;
  role: 'yu' | 'xi' | 'system';
  content: string;
  timestamp: string;
  metadata?: Record<string, any>;
}
```

### 核心组件
- `mockData.ts`：模拟数据定义和生成
- `useChat.ts`：集成模拟数据逻辑
- `toggle-mock.cjs`：快速切换脚本

### 环境变量
- `VITE_USE_MOCK_DATA`：控制模拟数据模式开关

## 注意事项

1. **重启服务器**：切换模式后需要重启开发服务器
2. **生产环境**：确保生产环境中禁用模拟数据
3. **数据同步**：模拟数据不会同步到后端
4. **功能限制**：某些高级功能可能无法完全模拟

## 未来扩展

- [ ] 更多样化的模拟响应类型
- [ ] 用户自定义模拟数据
- [ ] 模拟网络延迟和错误状态
- [ ] 集成到自动化测试流程
