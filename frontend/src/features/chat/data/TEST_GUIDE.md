# YX Nexus 前端测试指南

## 🎯 快速测试步骤

### 1. 启用模拟数据模式
```bash
# 在 frontend 目录下执行
node scripts/toggle-mock.cjs on
# 重启开发服务器
pnpm run dev
```

### 2. 打开浏览器
访问 `http://localhost:5173`

### 3. 测试界面效果

#### 🌌 初始状态测试
- **宇宙背景动画**：观察星尘和粒子漂流效果
- **悬浮指令核心**：毛玻璃材质、辉光边框
- **YX Nexus 标题**：居中显示，优雅字体
- **输入框聚焦**：点击时边框和阴影增强

#### 🔢 数字指令测试
在输入框中依次输入以下数字，观察不同效果：

**输入 "1" - 基础文本测试**
- ✅ 界面从居中转换为对话模式
- ✅ 曦的紫色光晕开始呼吸闪烁
- ✅ 流式打字机效果
- ✅ Markdown 渲染（粗体、斜体、列表）

**输入 "2" - 工具调用测试**
- ✅ 工具调用图标 🛠️ 显示
- ✅ 工具使用提示动画
- ✅ 搜索和分析功能模拟

**输入 "3" - 代码块测试**
- ✅ Python 和 JavaScript 语法高亮
- ✅ 代码块格式和缩进
- ✅ 多语言支持验证

**输入 "4" - 长文本测试**
- ✅ 滚动性能测试
- ✅ 响应式布局适配
- ✅ 内容层次结构
- ✅ 大量文本渲染性能

**输入 "5" - 表格引用测试**
- ✅ 表格渲染和对齐
- ✅ 引用块样式
- ✅ 链接效果
- ✅ 分割线显示

**输入其他内容 - 帮助信息**
- ✅ 默认响应和使用指南
- ✅ 测试指令说明

## 🎨 重点观察项目

### 动画效果
- [ ] 宇宙背景粒子漂流
- [ ] 界面布局平滑过渡
- [ ] 曦的思考光晕呼吸
- [ ] 打字机流式输出
- [ ] 输入框聚焦辉光

### UI组件
- [ ] 毛玻璃悬浮指令核心
- [ ] AuraIndicator 身份标识
- [ ] MarkdownRenderer 内容渲染
- [ ] 代码语法高亮
- [ ] 表格和引用样式

### 交互体验
- [ ] 发送消息响应速度
- [ ] 思考状态视觉反馈
- [ ] 滚动性能流畅度
- [ ] 响应式布局适配
- [ ] 键盘快捷键支持

## 🔧 故障排除

### 模拟数据未生效
1. 检查 `.env` 文件中 `VITE_USE_MOCK_DATA=true`
2. 重启开发服务器
3. 清除浏览器缓存

### 动画效果不显示
1. 检查浏览器开发者工具中的CSS加载
2. 确认 Tailwind CSS 正确编译
3. 验证 framer-motion 库加载

### 组件渲染异常
1. 查看浏览器控制台错误信息
2. 检查 TypeScript 编译错误
3. 验证组件导入路径

## 📊 性能测试

### 建议测试场景
1. **快速连续输入**：测试状态管理稳定性
2. **长文本滚动**：验证渲染性能
3. **多次切换**：检查内存泄漏
4. **不同屏幕尺寸**：响应式适配测试

### 性能指标
- 首屏加载时间 < 2秒
- 动画帧率 > 60fps
- 内存使用稳定
- 交互响应 < 100ms

## 🚀 完成测试后

### 切换回真实模式
```bash
node scripts/toggle-mock.cjs off
# 重启开发服务器
```

### 测试报告
记录发现的问题和改进建议，为后续优化提供参考。
