// src/features/chat/data/mockResponses.ts
// 模拟响应数据 - 用于测试不同的UI组件和功能

export const mockResponses = {
  '1': `**基础文本响应测试** ✨

这是一个基础的文本响应，用于测试：
- 基本的Markdown渲染
- 文本换行和段落
- **粗体**和*斜体*效果
- 列表项目显示

界面渲染效果看起来不错！`,

  '2': `[正在使用我的能力...🛠️]

**工具调用模拟测试**

我正在使用搜索工具查找相关信息...

根据搜索结果，找到了以下内容：
- 🔍 搜索功能正常
- 📊 数据分析完成
- ✅ 工具调用动画效果测试成功

这个测试验证了工具调用提示的显示效果。`,

  '3': `**代码块渲染测试** 💻

让我展示一些代码示例来测试语法高亮：

\`\`\`python
# Python 代码示例
class TestComponent:
    def __init__(self, name):
        self.name = name
        self.status = 'active'
    
    def render(self):
        return f"Component {self.name} is {self.status}"

# 创建实例
component = TestComponent("YX Nexus")
print(component.render())
\`\`\`

\`\`\`javascript
// JavaScript 代码示例
const testFunction = (data) => {
  return data.map(item => ({
    ...item,
    processed: true
  }));
};
\`\`\`

代码高亮和格式化效果测试完成！`,

  '4': `**长文本滚动测试** 📜

这是一个用于测试长文本滚动和渲染性能的响应。

## 第一部分：系统架构

YX Nexus 采用了现代化的前端架构，包括：

### 技术栈
- **前端框架**: React + TypeScript
- **状态管理**: Zustand
- **样式系统**: Tailwind CSS + shadcn/ui
- **动画库**: Framer Motion
- **构建工具**: Vite

### 设计理念
1. **空间叙事**: 从虚无到流淌的界面转换
2. **生命感交互**: 呼吸光晕和微妙动画
3. **信息分层**: 默认简洁，交互揭示深度
4. **架构即认知**: 结构反映思维模式

## 第二部分：核心功能

### 悬浮指令核心
- 毛玻璃材质效果
- 动态辉光边框
- 聚焦状态增强
- 响应式尺寸调整

### 宇宙漂流背景
- 多层次动画系统
- 智能状态控制
- 性能优化实现
- 沉浸式体验营造

### 生命光晕系统
- 角色身份标识
- 思考状态动画
- 精确的光晕控制
- 流式输出指示

## 第三部分：用户体验

这个长文本测试验证了：
- 📱 响应式布局适配
- 🎨 Markdown 渲染质量
- ⚡ 滚动性能表现
- 🎯 内容层次结构
- ✨ 视觉效果一致性

测试完成！界面滚动和渲染效果良好。`,

  '5': `**表格和引用测试** 📊

> 这是一个引用块测试，用于验证引用样式的渲染效果。引用块应该有特殊的左边框和斜体样式。

下面是一个表格测试：

| 功能模块 | 状态 | 测试结果 | 备注 |
|---------|------|----------|------|
| 基础文本 | ✅ | 通过 | 渲染正常 |
| 代码高亮 | ✅ | 通过 | 语法正确 |
| 表格显示 | 🧪 | 测试中 | 当前项目 |
| 引用块 | ✅ | 通过 | 样式正确 |
| 列表项 | ✅ | 通过 | 缩进正常 |

### 链接测试
这里有一个[测试链接](https://example.com)，用于验证链接样式。

### 分割线测试

---

分割线上方和下方的内容应该有明显的视觉分隔。

**测试总结**: 表格、引用、链接等元素渲染效果良好！`,

  default: (input: string) => `**默认响应** 🤖

你输入了："${input}"

💡 **测试提示**：
- 输入 **1** - 基础文本响应测试
- 输入 **2** - 工具调用模拟测试  
- 输入 **3** - 代码块渲染测试
- 输入 **4** - 长文本滚动测试
- 输入 **5** - 表格和引用测试

每种测试都会展示不同的UI组件和动画效果，帮助验证界面的各项功能。

✨ 试试输入数字来体验不同的测试场景吧！`
};
