// src/services/websocket/manager.ts
type WebSocketStatus = 'connecting' | 'connected' | 'disconnected' | 'reconnecting';

class WebSocketManager {
  private socket: WebSocket | null = null;
  private url: string;
  private status: WebSocketStatus = 'disconnected';
  
  private onMessageCallback: ((message: any) => void) | null = null;
  private onStatusChangeCallback: ((status: WebSocketStatus) => void) | null = null;

  constructor(url: string) {
    this.url = url;
  }

  public connect() {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      return;
    }
    
    this.setStatus('connecting');
    this.socket = new WebSocket(this.url);

    this.socket.onopen = () => {
      this.setStatus('connected');
    };

    this.socket.onmessage = (event) => {
      // TODO: 实现流式消息的拼接和处理逻辑
      if (this.onMessageCallback) {
        // 临时实现，后续需要处理 [STREAM_END]
        this.onMessageCallback(event.data);
      }
    };

    this.socket.onclose = () => {
      this.setStatus('disconnected');
      // TODO: 实现自动重连逻辑
    };

    this.socket.onerror = (error) => {
      console.error('WebSocket Error:', error);
      this.setStatus('disconnected');
    };
  }
  
  public sendMessage(data: { yu_input: string, session_id?: string }) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(data));
    } else {
      console.error('WebSocket is not connected.');
    }
  }

  public onMessage(callback: (message: any) => void) {
    this.onMessageCallback = callback;
  }

  public onStatusChange(callback: (status: WebSocketStatus) => void) {
    this.onStatusChangeCallback = callback;
  }

  private setStatus(status: WebSocketStatus) {
    this.status = status;
    if (this.onStatusChangeCallback) {
      this.onStatusChangeCallback(this.status);
    }
  }
  
  public disconnect() {
    if (this.socket) {
      this.socket.close();
    }
  }

  public getStatus(): WebSocketStatus {
    return this.status;
  }
}

// 创建并导出一个单例
const wsManager = new WebSocketManager(import.meta.env.VITE_WS_URL || 'ws://localhost:8000/api/v1/ws/chat');
export default wsManager;
