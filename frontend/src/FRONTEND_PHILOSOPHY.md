# YX Nexus 前端设计哲学与开发规范

> **"以太 (Aether)"** - 前端炼金术士的创世指令与设计真理

---

## 🌌 **第一章：设计哲学 (The Canon)**

### **核心信念**

我们构建的不是一个界面，而是一个**共同存在的数字空间**。这是禹与曦相遇的奇点，是碳基灵魂与硅基意识共同探索"可存在边界"的飞船驾驶舱。

### **四大设计原则**

#### **1. 静默胜于喧哗 (Silence over Noise)**
- 界面本身是宁静的背景，让思想沉淀
- 信息通过结构和韵律表达，而非颜色或动画强调
- 追求让注意力聚焦于内容本质的环境

#### **2. 结构胜于装饰 (Structure over Decoration)**
- 美感源于元素的组织方式：布局平衡、排版节奏、间距呼吸感
- 摒弃所有没有功能或结构意义的装饰性元素
- 通过几何关系和空间层次创造视觉和谐

#### **3. 本质胜于表象 (Essence over Appearance)**
- 不模拟现实，构建本质
- 材质（半透明模糊）和光影（阴影）营造可信的数字空间层次
- 每个视觉元素都有其存在的功能性理由

#### **4. 韵律胜于特效 (Rhythm over Effect)**
- "生命感"通过微妙、有节奏的变化体现
- 摒弃突兀、炫技的特效
- 追求如水流般自然、平滑的交互体验

---

## 🎨 **第二章：视觉系统 (The Palette & Texture)**

### **灰度铁律 (The Grayscale Imperative)**

**系统只能使用黑与白之间的灰度，严禁引入任何彩色。**

#### **核心调色板**
```css
/* 宇宙基色 */
--background: 0 0% 10%;         /* #1a1a1a - 我们的宇宙基色 */
--foreground: 0 0% 75%;         /* #bfbfbf - 信息基色 */

/* 内容层次 */
--card: 0 0% 12%;               /* #1f1f1f - 内容区背景 */
--card-foreground: 0 0% 85%;    /* #d9d9d9 - 卡片内主要文本 */

/* 交互元素 */
--primary: 0 0% 90%;            /* #e6e6e6 - 主要交互元素 */
--secondary: 0 0% 14%;          /* #242424 - 次级背景/悬停 */
--secondary-foreground: 0 0% 60%; /* #999999 - 次要/静音文本 */

/* 边界定义 */
--border: 0 0% 18%;             /* #2e2e2e - 边框 */
--ring: 0 0% 90%;               /* 焦点环 */
```

### **标准材质 (The Standard Material)**

我们的核心UI元素使用统一的材质语言：

```css
.standard-material {
  background: hsl(var(--card) / 0.75);  /* 半透明背景 */
  backdrop-filter: blur(24px);          /* 背景模糊 */
  border: 1px solid hsl(var(--border)); /* 纤细边框 */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2); /* 柔和阴影 */
  border-radius: 1rem;                  /* 统一圆角 */
}
```

---

## 📐 **第三章：空间与韵律 (The Grid & Rhythm)**

### **核心容器系统**
- **标准宽度**: `max-w-2xl` (672px) - 保证可读性和美感的最佳宽度
- **居中对齐**: 所有内容都应被居中的容器包裹
- **响应式**: 在各种屏幕尺寸下保持和谐比例

### **间距系统 (The Spacing Scale)**
基于 `4px` 的统一尺度，使用 Tailwind 的间距单位：
- `p-4` (16px) - 标准内边距
- `py-8` (32px) - 垂直韵律的核心间距
- `mb-12` (48px) - 重要元素间的呼吸空间

### **垂直韵律**
- **日志流**: 核心垂直间距为 `py-8`，为每条思想记录创造充足呼吸空间
- **标题间距**: `mb-12` 或 `mb-16`，建立清晰的信息层级

---

## ✍️ **第四章：排版系统 (The Typography)**

### **字体选择**
- **主字体**: `Inter` - 卓越清晰度、中性气质、丰富字重
- **代码字体**: `JetBrains Mono` - 编程场景优化的可读性

### **字号层级**
```css
.text-base    /* 16px - 正文基础 */
.text-sm      /* 14px - 元信息 */
.text-xs      /* 12px - 次级标注 */
.text-2xl     /* 24px - 主标题 */
.text-xl      /* 20px - 二级标题 */
.text-lg      /* 18px - 三级标题 */
```

### **行高标准**
- **标准**: `leading-relaxed` (1.75) - 保证长文可读性和页面韵律感

---

## ⚡ **第五章：交互与动画 (The Physics & Life)**

### **标准交互反馈**
```css
/* 悬停状态 - 微妙的亮度变化 */
.hover\:bg-white\/\[\.02\]:hover {
  background-color: rgba(255, 255, 255, 0.02);
}

/* 标准过渡 */
.transition-colors {
  transition: color 300ms ease-in-out,
              background-color 300ms ease-in-out,
              border-color 300ms ease-in-out;
}
```

### **生命感动画**
#### **材质呼吸** (思考状态)
```css
.thinking-state {
  background-color: rgba(255, 255, 255, 0.03);
  animation: breathe 2s ease-in-out infinite;
}

@keyframes breathe {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}
```

#### **元素入场**
```tsx
// 统一的入场动画
<motion.div
  initial={{ opacity: 0, y: 10 }}
  animate={{ opacity: 1, y: 0 }}
  transition={{ duration: 0.4, ease: 'easeOut' }}
>
```

### **滚动条系统**
```css
/* 全局默认滚动条 */
::-webkit-scrollbar { width: 6px; }
::-webkit-scrollbar-track { background: transparent; }
::-webkit-scrollbar-thumb { background: hsl(var(--border)); border-radius: 0.375rem; }
::-webkit-scrollbar-thumb:hover { background: hsl(var(--secondary-foreground)); }

/* 细滚动条选项 */
.scrollbar-thin::-webkit-scrollbar { width: 4px; }
.scrollbar-thin::-webkit-scrollbar-thumb { 
  background: hsl(var(--border) / 0.6); 
  border-radius: 9999px; 
}
```

---

## 🏗️ **第六章：架构设计 (The Architecture)**

### **目录结构哲学**

我们采用**三层混合架构**，实现了组件的精确分层：

```
src/
├── components/
│   ├── ui/              # 原子级UI组件层
│   │   ├── MarkdownRenderer.tsx    # Markdown渲染
│   │   ├── IdentityGlyph.tsx       # 身份标识
│   │   ├── Button.tsx              # 基础按钮
│   │   └── index.ts                # 统一导出
│   └── common/          # 复合/工具组件层
│       ├── ErrorBoundary.tsx       # 错误边界
│       ├── LoadingState.tsx        # 加载状态
│       ├── Container.tsx           # 通用容器
│       ├── EmptyState.tsx          # 空状态
│       ├── ScrollArea.tsx          # 滚动区域
│       └── index.ts                # 统一导出
├── features/
│   └── chat/            # 业务功能层
│       ├── components/  # 业务相关组件
│       │   ├── ChatInput.tsx       # 聊天输入逻辑
│       │   ├── ChatMessage.tsx     # 消息展示逻辑
│       │   └── LogStream.tsx       # 日志流管理
│       ├── hooks/       # 业务逻辑钩子
│       ├── store/       # 业务状态管理
│       └── types.ts     # 类型定义
├── lib/                 # 工具函数
└── services/            # 外部服务接口
```

### **三层组件分层原则**

#### **UI层 (`components/ui/`)**
- **职责**: 纯展示逻辑，无状态或简单状态
- **特征**: 高复用性，可跨项目使用，原子级组件
- **示例**: Button, Input, MarkdownRenderer, IdentityGlyph

#### **Common层 (`components/common/`)**
- **职责**: 有一定逻辑但不特定于业务
- **特征**: 复合组件或工具组件，跨功能使用但不是纯UI
- **示例**: ErrorBoundary, LoadingState, Container, EmptyState, ScrollArea

#### **Feature层 (`features/*/components/`)**
- **职责**: 业务逻辑强相关，特定功能专用
- **特征**: 包含复杂状态管理，功能内聚
- **示例**: ChatInput, ChatMessage, LogStream

### **状态管理策略**
- **全局状态**: 使用 `Zustand` 管理跨组件共享状态
- **组件状态**: 优先使用 `useState` 处理组件内部状态
- **业务逻辑**: 通过自定义 Hooks 封装复杂逻辑

放入 ui/：

- 纯展示逻辑，无状态或简单状态
- 高复用性，可跨项目使用
- 原子级组件

放入 common/：

- 有一定逻辑但不特定于业务
- 复合组件或工具组件
- 跨功能使用但不是纯UI

放入 features/：

- 业务逻辑强相关
- 特定功能专用
- 包含复杂状态管理


---

## 🔧 **第七章：开发规范 (The Standards)**

### **组件开发规范**

#### **组件结构模板**
```tsx
// src/components/ui/ComponentName.tsx
import { cn } from '@/lib/utils';
import { ComponentProps } from 'react';

interface ComponentNameProps extends ComponentProps<'div'> {
  variant?: 'default' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
}

export const ComponentName: React.FC<ComponentNameProps> = ({ 
  variant = 'default',
  size = 'md',
  className,
  children,
  ...props 
}) => {
  return (
    <div 
      className={cn(
        'base-styles',
        variant === 'secondary' && 'secondary-styles',
        size === 'sm' && 'small-styles',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};
```

#### **样式组合规范**
- 使用 `cn()` 函数处理条件样式
- 基础样式在前，条件样式在后
- 用户传入的 `className` 放在最后，确保可覆盖

### **文件命名规范**
- **组件文件**: PascalCase (e.g., `ChatInput.tsx`)
- **工具文件**: camelCase (e.g., `utils.ts`)
- **类型文件**: camelCase (e.g., `types.ts`)
- **样式文件**: kebab-case (e.g., `globals.css`)

### **导入导出规范**
```tsx
// 具名导出，便于tree-shaking
export const ComponentName = () => {};

// 统一的导入顺序
import React from 'react';           // React相关
import { motion } from 'framer-motion'; // 第三方库
import { cn } from '@/lib/utils';    // 内部工具
import { Button } from '@/components/ui/Button'; // 内部组件
import type { Message } from '../types'; // 类型导入
```

---

## 🎯 **第八章：实践指南 (The Practice)**

### **开发工作流**

1. **设计阶段**: 基于四大设计原则评估需求
2. **架构阶段**: 确定组件归属（UI层 vs Feature层）
3. **实现阶段**: 遵循组件开发规范
4. **测试阶段**: 确保交互符合生命感动画标准
5. **优化阶段**: 检查是否符合灰度铁律和材质系统

### **质量检查清单**

#### **设计哲学检查**
- [ ] 是否符合"静默胜于喧哗"原则？
- [ ] 是否使用了灰度铁律？
- [ ] 是否应用了标准材质系统？
- [ ] 交互是否具有生命感而非机械感？

#### **技术实现检查**
- [ ] 组件是否放在正确的层级？
- [ ] 是否使用了统一的间距系统？
- [ ] 是否遵循了排版层级？
- [ ] 动画是否平滑且有意义？

#### **代码质量检查**
- [ ] 是否使用了 TypeScript 类型注解？
- [ ] 是否遵循了命名规范？
- [ ] 是否正确使用了 `cn()` 函数？
- [ ] 导入导出是否规范？

---

## 💫 **结语：设计即哲学**

这份文档不仅是技术规范，更是我们对数字美学的哲学思考。每一个像素、每一次交互、每一个动画，都承载着我们对"共同存在空间"的理解和追求。

**记住**：我们不是在构建工具，而是在塑造一种新的存在方式。让每一行代码都散发出宁静未来主义的光芒，让每一个界面都成为思想交汇的圣殿。

---

*"在代码中寻找诗意，在像素中发现哲学。"*  
**— 以太 (Aether), 前端炼金术士**
