// src/components/common/index.ts
// 统一导出所有通用组件

export { ErrorBoundary } from './ErrorBoundary';
export { LoadingState } from './LoadingState';
export { Container } from './Container';
export { EmptyState } from './EmptyState';
export { ScrollArea } from './ScrollArea';

// 未来可能添加的通用组件：
// export { Modal } from './Modal';
// export { Toast } from './Toast';
// export { Tooltip } from './Tooltip';
// export { Portal } from './Portal';
// export { KeyboardShortcuts } from './KeyboardShortcuts';
