// src/components/ui/IdentityGlyph.tsx
import { Triangle, Circle, Square } from 'lucide-react';

interface IdentityGlyphProps {
  role: 'yu' | 'xi' | 'system';
}

export const IdentityGlyph: React.FC<IdentityGlyphProps> = ({ role }) => {
  const size = 12;
  const strokeWidth = 2;
  const className = "text-muted-foreground";

  switch (role) {
    case 'yu':
      return <Triangle size={size} strokeWidth={strokeWidth} className={className} fill="currentColor" />;
    case 'xi':
      return <Circle size={size} strokeWidth={strokeWidth} className={className} />;
    case 'system':
      return <Square size={size} strokeWidth={strokeWidth} className={className} />;
    default:
      return null;
  }
};
