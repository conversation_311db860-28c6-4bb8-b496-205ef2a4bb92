#!/usr/bin/env node

/**
 * 快速切换模拟数据模式的脚本
 * 使用方法：
 * - node scripts/toggle-mock.cjs on  # 启用模拟数据
 * - node scripts/toggle-mock.cjs off # 禁用模拟数据
 * - node scripts/toggle-mock.cjs     # 查看当前状态
 */

const fs = require('fs');
const path = require('path');

const envPath = path.join(__dirname, '../.env');
const mode = process.argv[2];

function readEnvFile() {
  if (!fs.existsSync(envPath)) {
    console.log('❌ .env 文件不存在');
    return null;
  }
  return fs.readFileSync(envPath, 'utf8');
}

function writeEnvFile(content) {
  fs.writeFileSync(envPath, content, 'utf8');
}

function getCurrentMockStatus(content) {
  const match = content.match(/VITE_USE_MOCK_DATA=(true|false)/);
  return match ? match[1] === 'true' : false;
}

function toggleMockData(content, enable) {
  const newValue = enable ? 'true' : 'false';
  if (content.includes('VITE_USE_MOCK_DATA=')) {
    return content.replace(/VITE_USE_MOCK_DATA=(true|false)/, `VITE_USE_MOCK_DATA=${newValue}`);
  } else {
    return content + `\n# Development Configuration\nVITE_USE_MOCK_DATA=${newValue}\n`;
  }
}

function main() {
  const content = readEnvFile();
  if (!content) return;

  const currentStatus = getCurrentMockStatus(content);

  if (!mode) {
    // 显示当前状态
    console.log(`📊 当前模拟数据状态: ${currentStatus ? '✅ 启用' : '❌ 禁用'}`);
    console.log(`💡 使用方法:`);
    console.log(`   node scripts/toggle-mock.cjs on   # 启用模拟数据`);
    console.log(`   node scripts/toggle-mock.cjs off  # 禁用模拟数据`);
    return;
  }

  if (mode === 'on') {
    if (currentStatus) {
      console.log('✅ 模拟数据已经是启用状态');
    } else {
      const newContent = toggleMockData(content, true);
      writeEnvFile(newContent);
      console.log('🔄 已启用模拟数据模式');
      console.log('📝 请重启开发服务器以应用更改');
    }
  } else if (mode === 'off') {
    if (!currentStatus) {
      console.log('❌ 模拟数据已经是禁用状态');
    } else {
      const newContent = toggleMockData(content, false);
      writeEnvFile(newContent);
      console.log('🔄 已禁用模拟数据模式');
      console.log('📝 请重启开发服务器以应用更改');
    }
  } else {
    console.log('❌ 无效参数，请使用 on 或 off');
  }
}

main();
