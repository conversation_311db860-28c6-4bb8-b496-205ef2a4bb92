#!/usr/bin/env python3
"""
Xi ContextOS 日志清理脚本

自动清理测试日志文件，支持多种清理策略：
- 按时间清理（保留最近N天） --days N
- 按数量清理（保留最新N个文件） --keep N
- 按大小清理（清理超过指定大小的日志目录） --size N
- 完全清理（清空所有测试日志） --all

使用方法：
python scripts/clean_logs.py [选项]
"""

import os
import shutil
import argparse
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Tuple

# 获取脚本所在目录的父目录（backend目录）
SCRIPT_DIR = Path(__file__).parent
BACKEND_DIR = SCRIPT_DIR.parent

# 日志目录配置（使用绝对路径）
LOG_DIRS = [
    BACKEND_DIR / "logs" / "dev_monitor",    # System Prompt文件和开发监测日志
    BACKEND_DIR / "logs" / "test",           # 测试执行日志
    BACKEND_DIR / "logs" / "reports",        # 测试报告
    BACKEND_DIR / "logs" / "debug"           # 调试日志
]

# 日志文件模式
LOG_PATTERNS = [
    "*.log",
    "*.txt", 
    "*.md",
    "system_prompt_*.txt",
    "report_*.md",
    "xi_monitor_*.log"
]

def get_log_files(log_dir: Path) -> List[Tuple[Path, datetime]]:
    """获取日志目录中的所有日志文件及其修改时间"""
    files = []
    if not log_dir.exists():
        return files
    
    for pattern in LOG_PATTERNS:
        for file_path in log_dir.glob(pattern):
            if file_path.is_file():
                mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                files.append((file_path, mtime))
    
    # 按修改时间排序（最新的在前）
    files.sort(key=lambda x: x[1], reverse=True)
    return files

def clean_by_age(days: int = 7) -> int:
    """按时间清理：删除N天前的日志文件"""
    cutoff_time = datetime.now() - timedelta(days=days)
    deleted_count = 0
    
    print(f"🕐 清理 {days} 天前的日志文件...")
    
    for log_dir in LOG_DIRS:
        if not log_dir.exists():
            continue
            
        files = get_log_files(log_dir)
        for file_path, mtime in files:
            if mtime < cutoff_time:
                try:
                    file_path.unlink()
                    print(f"   删除: {file_path.name} ({mtime.strftime('%Y-%m-%d %H:%M')})")
                    deleted_count += 1
                except Exception as e:
                    print(f"   ❌ 删除失败: {file_path.name} - {e}")
    
    return deleted_count

def clean_by_count(keep_count: int = 10) -> int:
    """按数量清理：每个目录只保留最新的N个文件"""
    deleted_count = 0
    
    print(f"📊 每个目录保留最新 {keep_count} 个文件...")
    
    for log_dir in LOG_DIRS:
        if not log_dir.exists():
            continue
            
        files = get_log_files(log_dir)
        if len(files) > keep_count:
            files_to_delete = files[keep_count:]
            print(f"   📁 {log_dir.name}: {len(files)} 个文件，删除 {len(files_to_delete)} 个")
            
            for file_path, mtime in files_to_delete:
                try:
                    file_path.unlink()
                    print(f"     删除: {file_path.name}")
                    deleted_count += 1
                except Exception as e:
                    print(f"     ❌ 删除失败: {file_path.name} - {e}")
    
    return deleted_count

def clean_by_size(max_size_mb: int = 100) -> int:
    """按大小清理：清理超过指定大小的日志目录"""
    deleted_count = 0
    max_size_bytes = max_size_mb * 1024 * 1024
    
    print(f"💾 清理超过 {max_size_mb}MB 的日志目录...")
    
    for log_dir in LOG_DIRS:
        if not log_dir.exists():
            continue
            
        # 计算目录大小
        total_size = sum(f.stat().st_size for f in log_dir.rglob('*') if f.is_file())
        size_mb = total_size / (1024 * 1024)
        
        print(f"   📁 {log_dir.name}: {size_mb:.1f}MB")
        
        if total_size > max_size_bytes:
            files = get_log_files(log_dir)
            # 删除一半的旧文件
            files_to_delete = files[len(files)//2:]
            
            for file_path, mtime in files_to_delete:
                try:
                    file_path.unlink()
                    print(f"     删除: {file_path.name}")
                    deleted_count += 1
                except Exception as e:
                    print(f"     ❌ 删除失败: {file_path.name} - {e}")
    
    return deleted_count

def clean_all() -> int:
    """完全清理：删除所有测试日志"""
    deleted_count = 0
    
    print("🗑️ 完全清理所有测试日志...")
    
    for log_dir in LOG_DIRS:
        if not log_dir.exists():
            continue
            
        files = get_log_files(log_dir)
        print(f"   📁 {log_dir.name}: 删除 {len(files)} 个文件")
        
        for file_path, mtime in files:
            try:
                file_path.unlink()
                deleted_count += 1
            except Exception as e:
                print(f"     ❌ 删除失败: {file_path.name} - {e}")
    
    return deleted_count

def show_status():
    """显示当前日志状态"""
    print("📊 当前日志状态:")
    total_files = 0
    total_size = 0
    
    for log_dir in LOG_DIRS:
        if not log_dir.exists():
            print(f"   📁 {log_dir.name}: 目录不存在")
            continue
            
        files = get_log_files(log_dir)
        dir_size = sum(f.stat().st_size for f, _ in files)
        size_mb = dir_size / (1024 * 1024)
        
        print(f"   📁 {log_dir.name}: {len(files)} 个文件, {size_mb:.1f}MB")
        
        if files:
            latest = files[0][1].strftime('%Y-%m-%d %H:%M')
            oldest = files[-1][1].strftime('%Y-%m-%d %H:%M')
            print(f"      最新: {latest}, 最旧: {oldest}")
        
        total_files += len(files)
        total_size += dir_size
    
    total_size_mb = total_size / (1024 * 1024)
    print(f"\n📈 总计: {total_files} 个文件, {total_size_mb:.1f}MB")

def main():
    parser = argparse.ArgumentParser(description="Xi ContextOS 日志清理工具")
    parser.add_argument("--days", type=int, help="删除N天前的日志文件")
    parser.add_argument("--keep", type=int, help="每个目录保留最新N个文件")
    parser.add_argument("--size", type=int, help="清理超过N MB的目录")
    parser.add_argument("--all", action="store_true", help="删除所有测试日志")
    parser.add_argument("--status", action="store_true", help="显示当前日志状态")
    parser.add_argument("--auto", action="store_true", help="自动清理（保留7天或10个文件）")
    
    args = parser.parse_args()
    
    print("🧹 Xi ContextOS 日志清理工具")
    print("=" * 50)
    
    # 显示状态
    if args.status or not any(vars(args).values()):
        show_status()
        if not any(vars(args).values()):
            print("\n💡 使用 --help 查看清理选项")
        return
    
    deleted_count = 0
    
    # 执行清理
    if args.all:
        deleted_count = clean_all()
    elif args.days:
        deleted_count = clean_by_age(args.days)
    elif args.keep:
        deleted_count = clean_by_count(args.keep)
    elif args.size:
        deleted_count = clean_by_size(args.size)
    elif args.auto:
        print("🤖 自动清理模式...")
        deleted_count += clean_by_age(7)  # 删除7天前的文件
        deleted_count += clean_by_count(10)  # 每个目录保留10个文件
    
    print(f"\n✅ 清理完成，共删除 {deleted_count} 个文件")
    
    # 显示清理后状态
    print("\n" + "=" * 50)
    show_status()

if __name__ == "__main__":
    main()
