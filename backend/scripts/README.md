# Xi ContextOS Scripts 使用指南

这个目录包含了Xi ContextOS系统的所有管理和测试脚本。这些脚本用于系统维护、功能测试、日志管理和知识库维护。

## 📁 脚本概览

### 🧪 测试脚本

#### `test_xi.py` - 通用测试工具
Xi系统的主要测试脚本，支持全面的功能验证和性能监测。

**功能特性：**
- 系统健康检查
- 对话功能测试（基本/流式）
- System Prompt构建验证
- 工具系统测试
- 数据库连接验证
- 自动生成测试报告

**使用方法：**
```bash
# 完整综合测试（默认）
python scripts/test_xi.py

# 指定测试消息
python scripts/test_xi.py --message "你好曦，请介绍一下你自己"

# 单独功能测试
python scripts/test_xi.py --health          # 健康检查
python scripts/test_xi.py --prompt          # System Prompt测试
python scripts/test_xi.py --tools           # 工具系统测试
python scripts/test_xi.py --database        # 数据库测试
python scripts/test_xi.py --chat            # 对话功能测试

# 详细输出模式
python scripts/test_xi.py --verbose

# 指定服务器地址
python scripts/test_xi.py --url http://localhost:8000
```

**生成的文件：**
- `logs/test/xi_test_YYYYMMDD_HHMMSS.log` - 详细测试日志
- `logs/reports/test_report_YYYYMMDD_HHMMSS.md` - 测试报告
- `logs/dev_monitor/system_prompt_YYYYMMDD_HHMMSS.txt` - System Prompt快照

#### `simple_websocket_test.py` - WebSocket功能测试
专门用于测试Xi系统WebSocket通信功能的轻量级测试脚本。

**功能特性：**
- WebSocket连接建立测试
- 消息发送和接收验证
- 流式响应处理测试
- 错误处理验证
- 连接管理测试

**使用方法：**
```bash
# 运行WebSocket测试（确保Xi服务器正在运行）
python scripts/simple_websocket_test.py
```

**测试流程：**
1. 建立WebSocket连接到 `ws://localhost:8000/api/v1/ws/chat`
2. 发送测试消息（JSON格式）
3. 接收流式响应
4. 验证流结束信号 `[STREAM_END]`
5. 测试错误处理（无效JSON、空输入）

### 🧹 日志管理脚本

#### `clean_logs.py` - 智能日志清理工具
自动管理和清理测试日志文件，支持多种清理策略。

**功能特性：**
- 按时间清理（保留最近N天）
- 按数量清理（保留最新N个文件）
- 按大小清理（清理超过指定大小的目录）
- 完全清理（清空所有测试日志）
- 日志状态监控

**使用方法：**
```bash
# 查看当前日志状态
python scripts/clean_logs.py --status

# 自动清理（推荐）- 删除7天前的文件，每个目录保留10个文件
python scripts/clean_logs.py --auto

# 按时间清理
python scripts/clean_logs.py --days 3       # 删除3天前的文件
python scripts/clean_logs.py --days 7       # 删除7天前的文件

# 按数量清理
python scripts/clean_logs.py --keep 5       # 每个目录保留最新5个文件
python scripts/clean_logs.py --keep 10      # 每个目录保留最新10个文件

# 按大小清理
python scripts/clean_logs.py --size 50      # 清理超过50MB的目录

# 完全清理（谨慎使用）
python scripts/clean_logs.py --all          # 删除所有测试日志
```

**管理的目录：**
- `logs/dev_monitor/` - System Prompt文件和开发监测日志
- `logs/test/` - 测试执行日志
- `logs/reports/` - 测试报告
- `logs/debug/` - 调试日志（预留）

### 📚 知识库管理脚本

#### `build_notes_index.py` - 知识索引构建器
扫描笔记目录，自动生成知识索引文件。

**功能特性：**
- 自动扫描所有Markdown笔记文件
- 提取标题和摘要信息
- 生成Markdown格式的知识索引
- 支持增量更新

**使用方法：**
```bash
# 构建/更新知识索引
python scripts/build_notes_index.py
```

**处理的文件：**
- 输入：`xi_system/memory/notes/*.md` - 所有笔记文件
- 输出：`xi_system/prompts/xi/knowledge_index.md` - 知识索引

**注意事项：**
- 每次添加新笔记后都应该运行此脚本
- 生成的索引文件会被Xi系统自动加载
- 建议在系统重启前运行以确保索引最新

## 📊 日志文件说明

### 日志目录结构
```
backend/logs/
├── dev_monitor/          # 开发监测和System Prompt文件
│   ├── system_prompt_*.txt    # System Prompt快照
│   └── xi_monitor_*.log       # 开发监测日志（历史）
├── test/                 # 测试执行日志
│   └── xi_test_*.log          # 详细测试执行日志
├── reports/              # 测试报告
│   └── test_report_*.md       # Markdown格式的测试报告
└── debug/                # 调试日志（预留）
    └── debug_*.log            # 调试信息（按需生成）
```

### 文件命名规则
- **时间戳格式**: `YYYYMMDD_HHMMSS` (年月日_时分秒)
- **System Prompt**: `system_prompt_YYYYMMDD_HHMMSS.txt`
- **测试日志**: `xi_test_YYYYMMDD_HHMMSS.log`
- **测试报告**: `test_report_YYYYMMDD_HHMMSS.md`

### 文件内容说明

#### System Prompt文件 (`system_prompt_*.txt`)
- 包含完整的System Prompt内容
- 用于调试和验证System Prompt构建
- 包含人格定义、知识索引、工具描述等所有组件
- 典型大小：8000-10000字符

#### 测试日志文件 (`xi_test_*.log`)
- 详细的测试执行过程记录
- 包含时间戳、日志级别、具体操作
- 记录所有API调用、响应时间、错误信息
- 用于问题诊断和性能分析

#### 测试报告文件 (`test_report_*.md`)
- Markdown格式的测试结果汇总
- 包含所有测试模块的状态（通过/失败）
- 提供关键指标和统计信息
- 便于快速了解系统整体状态

## 🚀 使用建议

### 日常开发流程
1. **开发前检查**：`python scripts/test_xi.py --health`
2. **功能测试**：`python scripts/test_xi.py --all`
3. **添加笔记后**：`python scripts/build_notes_index.py`
4. **定期清理**：`python scripts/clean_logs.py --auto`

### 问题诊断流程
1. **查看测试报告**：检查最新的`test_report_*.md`
2. **查看详细日志**：检查对应的`xi_test_*.log`
3. **验证System Prompt**：检查`system_prompt_*.txt`
4. **单独测试**：使用具体的`--prompt`、`--tools`等选项

### 性能监控
- 定期运行完整测试，关注响应时间变化
- 监控数据库记录数量增长
- 观察System Prompt长度变化
- 跟踪工具调用成功率

## ⚠️ 注意事项

### 安全提醒
- `clean_logs.py --all` 会删除所有测试日志，使用前请确认
- 测试脚本会生成真实的API调用，确保服务器状态正常
- System Prompt文件可能包含敏感信息，注意访问权限

### 性能考虑
- 完整测试可能需要2-3分钟，请耐心等待
- 大量日志文件会占用磁盘空间，建议定期清理
- 数据库测试会产生真实的查询操作

### 维护建议
- 这些脚本需要随Xi系统发展持续维护更新
- 新增功能时应同步更新测试脚本
- 定期检查脚本的兼容性和准确性

---

**版本**: V0.82  
**最后更新**: 2025-07-07  
**维护者**: Xi ContextOS 开发团队
