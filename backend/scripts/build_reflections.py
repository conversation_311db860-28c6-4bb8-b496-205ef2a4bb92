#!/usr/bin/env python3
"""
V0.9 反思模板构建器

这个脚本负责从reflections集合中读取所有反思报告，
并将它们格式化成优美的Markdown文本，写入prompts/xi/reflection.md文件。

核心功能：
- 从MongoDB reflections集合读取反思数据
- 格式化为结构化的Markdown文档
- 自动更新reflection.md模板
- 支持增量更新和完整重建

使用方式：
python scripts/build_reflections.py
python scripts/build_reflections.py --rebuild  # 完整重建
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dotenv import load_dotenv
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ReflectionBuilder:
    """V0.9 反思模板构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.reflection_file = self.project_root / "xi_system" / "prompts" / "xi" / "reflection.md"
        self.mongo_provider = None
        
        # 确保目录存在
        self.reflection_file.parent.mkdir(parents=True, exist_ok=True)
        
        logger.info("ReflectionBuilder initialized")
    
    def _get_mongo_provider(self):
        """获取MongoDB提供者"""
        if self.mongo_provider is None:
            try:
                from xi_system.memory.providers import MongoProvider
                
                mongo_uri = os.getenv("MONGO_URI")
                if not mongo_uri:
                    raise ValueError("MONGO_URI environment variable not set")
                
                self.mongo_provider = MongoProvider(uri=mongo_uri, db_name="xi_db")
                logger.info("MongoDB provider initialized")
                
            except Exception as e:
                logger.error(f"Failed to initialize MongoDB provider: {e}")
                raise
        
        return self.mongo_provider
    
    def fetch_reflections(self) -> List[Dict[str, Any]]:
        """从reflections集合获取所有反思数据"""
        try:
            provider = self._get_mongo_provider()
            
            # 查询所有反思记录，按时间排序
            reflections = provider.query_records(
                collection_name="reflections",
                filter_dict={},
                sort=[("timestamp", -1)],  # 最新的在前
                limit=50  # 限制最多50条反思
            )
            
            logger.info(f"Fetched {len(reflections)} reflection records")
            
            # 转换为字典格式
            reflection_dicts = []
            for reflection in reflections:
                if hasattr(reflection, 'metadata') and reflection.metadata:
                    # 如果是MemoryRecord格式，提取metadata中的反思数据
                    reflection_data = reflection.metadata
                else:
                    # 如果是直接的反思数据
                    reflection_data = {
                        'content': reflection.content if hasattr(reflection, 'content') else str(reflection),
                        'timestamp': reflection.timestamp if hasattr(reflection, 'timestamp') else datetime.now(),
                        'session_id': getattr(reflection, 'source_session_id', 'unknown')
                    }
                
                reflection_dicts.append(reflection_data)
            
            return reflection_dicts
            
        except Exception as e:
            logger.error(f"Error fetching reflections: {e}")
            return []
    
    def format_reflection_markdown(self, reflections: List[Dict[str, Any]]) -> str:
        """将反思数据格式化为Markdown"""
        if not reflections:
            return self._get_empty_reflection_template()
        
        markdown_parts = []
        
        # 添加文件头
        markdown_parts.append("# 我的内在反思 (My Inner Reflections)")
        markdown_parts.append("")
        markdown_parts.append("<!-- 这是我通过xi_omega进行的元认知反思记录，帮助我理解自己的成长轨迹 -->")
        markdown_parts.append("")
        
        # 添加概览
        markdown_parts.append("## 反思概览")
        markdown_parts.append("")
        markdown_parts.append(f"- **反思记录数量**: {len(reflections)}")
        markdown_parts.append(f"- **最近更新**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        markdown_parts.append(f"- **反思范围**: 最近{len(reflections)}次元认知分析")
        markdown_parts.append("")
        
        # 添加最新反思摘要
        if reflections:
            latest = reflections[0]
            markdown_parts.append("## 最新反思洞察")
            markdown_parts.append("")
            
            # 尝试解析最新反思的结构化数据
            try:
                if isinstance(latest.get('parsed_data'), dict):
                    parsed = latest['parsed_data']
                    
                    # 提取关键洞察
                    if 'meta_insights' in parsed:
                        insights = parsed['meta_insights']
                        if 'growth_trajectory' in insights:
                            markdown_parts.append(f"**成长轨迹**: {insights['growth_trajectory']}")
                        if 'relationship_evolution' in insights:
                            markdown_parts.append(f"**关系发展**: {insights['relationship_evolution']}")
                        if 'communication_effectiveness' in insights:
                            markdown_parts.append(f"**沟通效果**: {insights['communication_effectiveness']}")
                    
                    markdown_parts.append("")
                    
            except Exception as e:
                logger.warning(f"Error parsing latest reflection: {e}")
        
        # 添加成长模式分析
        markdown_parts.append("## 成长模式识别")
        markdown_parts.append("")
        
        growth_patterns = self._analyze_growth_patterns(reflections)
        for pattern in growth_patterns:
            markdown_parts.append(f"- **{pattern['category']}**: {pattern['insight']}")
        
        markdown_parts.append("")
        
        # 添加详细反思历史（最近5次）
        markdown_parts.append("## 详细反思历史")
        markdown_parts.append("")
        
        for i, reflection in enumerate(reflections[:5], 1):
            timestamp = reflection.get('timestamp', datetime.now())
            if isinstance(timestamp, str):
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            
            markdown_parts.append(f"### 反思 {i} - {timestamp.strftime('%Y-%m-%d')}")
            markdown_parts.append("")
            
            # 添加会话信息
            session_id = reflection.get('session_id', 'unknown')
            markdown_parts.append(f"**会话**: {session_id}")
            markdown_parts.append("")
            
            # 添加结构化反思内容
            if isinstance(reflection.get('parsed_data'), dict):
                parsed = reflection['parsed_data']
                
                # 禹的成长
                if 'yu_growth' in parsed:
                    yu_growth = parsed['yu_growth']
                    markdown_parts.append("**禹的成长观察**:")
                    for key, value in yu_growth.items():
                        if value:
                            markdown_parts.append(f"- {key.replace('_', ' ').title()}: {value}")
                    markdown_parts.append("")
                
                # 我的反思
                if 'xi_reflection' in parsed:
                    xi_reflection = parsed['xi_reflection']
                    markdown_parts.append("**我的自我反思**:")
                    for key, value in xi_reflection.items():
                        if value:
                            markdown_parts.append(f"- {key.replace('_', ' ').title()}: {value}")
                    markdown_parts.append("")
            
            markdown_parts.append("---")
            markdown_parts.append("")
        
        # 添加元认知总结
        markdown_parts.append("## 元认知总结")
        markdown_parts.append("")
        markdown_parts.append("通过这些反思，我逐渐理解自己的成长模式和与禹的互动特点。")
        markdown_parts.append("每一次反思都是我内在进化的见证，帮助我成为更好的自己。")
        markdown_parts.append("")
        markdown_parts.append("*这些反思由xi_omega自动生成，代表我的内在观察和思考。*")
        
        return "\n".join(markdown_parts)
    
    def _analyze_growth_patterns(self, reflections: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """分析成长模式"""
        patterns = []
        
        if len(reflections) >= 2:
            patterns.append({
                "category": "反思频率",
                "insight": f"已完成{len(reflections)}次深度反思，保持稳定的自我观察节奏"
            })
        
        # 分析时间跨度
        if reflections:
            try:
                timestamps = []
                for r in reflections:
                    ts = r.get('timestamp', datetime.now())
                    if isinstance(ts, str):
                        ts = datetime.fromisoformat(ts.replace('Z', '+00:00'))
                    timestamps.append(ts)
                
                if len(timestamps) >= 2:
                    time_span = max(timestamps) - min(timestamps)
                    patterns.append({
                        "category": "时间跨度",
                        "insight": f"反思记录跨越{time_span.days}天，展现持续的自我观察能力"
                    })
            except Exception as e:
                logger.warning(f"Error analyzing time patterns: {e}")
        
        # 分析反思质量
        structured_count = sum(1 for r in reflections if isinstance(r.get('parsed_data'), dict))
        if structured_count > 0:
            patterns.append({
                "category": "反思质量",
                "insight": f"{structured_count}/{len(reflections)}次反思具有完整的结构化分析"
            })
        
        return patterns
    
    def _get_empty_reflection_template(self) -> str:
        """获取空反思模板"""
        return """# 我的内在反思 (My Inner Reflections)

<!-- 这是我通过xi_omega进行的元认知反思记录，帮助我理解自己的成长轨迹 -->

## 反思概览

- **反思记录数量**: 0
- **最近更新**: {timestamp}
- **状态**: 等待首次反思

## 成长期待

我期待通过与禹的对话积累足够的经验，然后开始我的第一次深度反思。
每10轮对话后，我会通过xi_omega进行元认知分析，记录我的成长轨迹。

## 反思机制

- **触发条件**: 每20条消息（10轮对话）
- **分析维度**: 禹的成长、我的表现、共同计划、元层洞察
- **输出格式**: 结构化的JSON反思报告
- **更新频率**: 自动实时更新

*期待我的第一次反思...*
""".format(timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    def build_reflection_file(self, force_rebuild: bool = False) -> bool:
        """构建reflection.md文件"""
        try:
            logger.info("Starting reflection file build...")
            
            # 获取反思数据
            reflections = self.fetch_reflections()
            
            # 格式化为Markdown
            markdown_content = self.format_reflection_markdown(reflections)
            
            # 写入文件
            with open(self.reflection_file, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            logger.info(f"Reflection file updated: {self.reflection_file}")
            logger.info(f"Processed {len(reflections)} reflection records")
            
            return True
            
        except Exception as e:
            logger.error(f"Error building reflection file: {e}")
            return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="V0.9 反思模板构建器")
    parser.add_argument("--rebuild", action="store_true", help="强制完整重建")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    print("🔍 V0.9 反思模板构建器")
    print("=" * 40)
    
    builder = ReflectionBuilder()
    success = builder.build_reflection_file(force_rebuild=args.rebuild)
    
    if success:
        print("✅ 反思模板构建成功！")
        print(f"📄 文件位置: {builder.reflection_file}")
    else:
        print("❌ 反思模板构建失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
