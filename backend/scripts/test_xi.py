#!/usr/bin/env python3
"""
Xi ContextOS 通用测试工具

这是Xi系统的主要测试脚本，用于验证系统各个组件的功能和性能。
支持多种测试模式，可以单独测试特定功能或进行综合测试。

功能特性：
- 系统健康检查
- WebSocket对话功能测试（基本/工具调用）
- System Prompt构建验证
- 工具系统测试
- 数据库连接验证
- API端点测试
- 架构完整性验证
- 性能监测

使用方法：
python scripts/test_xi.py [选项]
"""

import json
import time
import requests
import sys
import os
import argparse
import asyncio
import websockets
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 加载环境变量
load_dotenv()

class XiTester:
    """Xi ContextOS 通用测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000", verbose: bool = False):
        self.base_url = base_url
        self.verbose = verbose

        # 创建分类的日志目录
        self.test_log_dir = Path("logs/test")
        self.dev_monitor_dir = Path("logs/dev_monitor")
        self.reports_dir = Path("logs/reports")

        for dir_path in [self.test_log_dir, self.dev_monitor_dir, self.reports_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)

        # 创建测试日志文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = self.test_log_dir / f"xi_test_{timestamp}.log"
        self.timestamp = timestamp
        
        print(f"🧪 Xi ContextOS 通用测试工具")
        print(f"📝 测试日志: {self.log_file}")
        print("=" * 60)
    
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] [{level}] {message}"
        
        # 写入日志文件
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
        
        # 控制台输出
        if self.verbose or level in ["ERROR", "SUCCESS"]:
            print(log_entry)
        else:
            # 简化输出
            if level == "ERROR":
                print(f"❌ {message}")
            elif level == "SUCCESS":
                print(f"✅ {message}")
            elif level == "INFO":
                print(f"ℹ️ {message}")
            else:
                print(f"📊 {message}")
    
    def check_server_health(self) -> bool:
        """检查服务器健康状态"""
        self.log("检查服务器健康状态", "INFO")
        
        try:
            response = requests.get(f"{self.base_url}/api/v1/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                self.log(f"服务器健康检查通过: {health_data.get('status', 'unknown')}", "SUCCESS")
                if self.verbose:
                    self.log(f"服务器信息: {json.dumps(health_data, ensure_ascii=False, indent=2)}")
                return True
            else:
                self.log(f"服务器健康检查失败: HTTP {response.status_code}", "ERROR")
                return False
        except Exception as e:
            self.log(f"无法连接到服务器: {e}", "ERROR")
            return False
    
    def test_basic_chat(self, message: str) -> dict:
        """测试基本对话功能（WebSocket版本）"""
        self.log(f"测试WebSocket对话: {message[:50]}...", "INFO")

        # 使用asyncio运行WebSocket测试
        try:
            result = asyncio.run(self._test_websocket_chat(message))
            return result
        except Exception as e:
            self.log(f"WebSocket对话测试异常: {e}", "ERROR")
            return {"success": False, "error": str(e)}

    async def _test_websocket_chat(self, message: str) -> dict:
        """WebSocket对话测试的异步实现"""
        ws_url = f"ws://localhost:8000/api/v1/ws/chat"

        start_time = time.time()
        try:
            async with websockets.connect(ws_url) as websocket:
                # 发送消息
                test_message = {
                    "yu_input": message,
                    "session_id": f"test_session_{int(time.time())}"
                }

                await websocket.send(json.dumps(test_message))

                # 接收响应
                response_parts = []
                while True:
                    try:
                        response = await websocket.recv()

                        if response == "[STREAM_END]":
                            break

                        response_parts.append(response)

                    except Exception as e:
                        self.log(f"接收响应时发生错误: {e}", "ERROR")
                        break

                duration = time.time() - start_time
                full_response = "".join(response_parts)

                self.log(f"WebSocket对话测试成功 - 响应时间: {duration:.2f}秒", "SUCCESS")
                self.log(f"响应块数: {len(response_parts)}, 总长度: {len(full_response)}字符")

                return {
                    "success": True,
                    "duration": duration,
                    "response_length": len(full_response),
                    "response_chunks": len(response_parts),
                    "response": full_response
                }

        except Exception as e:
            duration = time.time() - start_time
            self.log(f"WebSocket连接失败: {e}", "ERROR")
            return {"success": False, "error": str(e), "duration": duration}
    
    def test_streaming_chat(self, message: str) -> dict:
        """测试WebSocket工具调用功能"""
        self.log(f"测试WebSocket工具调用: {message[:50]}...", "INFO")

        # 使用asyncio运行WebSocket测试
        try:
            result = asyncio.run(self._test_websocket_tools(message))
            return result
        except Exception as e:
            self.log(f"WebSocket工具调用测试异常: {e}", "ERROR")
            return {"success": False, "error": str(e)}

    async def _test_websocket_tools(self, message: str) -> dict:
        """WebSocket工具调用测试的异步实现"""
        ws_url = f"ws://localhost:8000/api/v1/ws/chat"

        start_time = time.time()
        try:
            async with websockets.connect(ws_url) as websocket:
                # 发送消息
                test_message = {
                    "yu_input": message,
                    "session_id": f"test_tools_{int(time.time())}"
                }

                await websocket.send(json.dumps(test_message))

                # 接收响应并检测工具调用
                response_parts = []
                tool_calls = 0

                while True:
                    try:
                        response = await websocket.recv()

                        if response == "[STREAM_END]":
                            break

                        response_parts.append(response)

                        # 检测工具调用
                        if "正在使用我的能力" in response or "🛠️" in response:
                            tool_calls += 1

                    except Exception as e:
                        self.log(f"接收响应时发生错误: {e}", "ERROR")
                        break

                duration = time.time() - start_time
                full_response = "".join(response_parts)

                self.log(f"WebSocket工具调用测试成功 - 响应时间: {duration:.2f}秒", "SUCCESS")
                self.log(f"响应块数: {len(response_parts)}, 工具调用: {tool_calls}次")

                return {
                    "success": True,
                    "duration": duration,
                    "chunks": len(response_parts),
                    "tool_calls": tool_calls,
                    "response": full_response
                }

        except Exception as e:
            duration = time.time() - start_time
            self.log(f"WebSocket连接失败: {e}", "ERROR")
            return {"success": False, "error": str(e), "duration": duration}
    
    def test_system_prompt(self, test_input: str = "测试输入") -> dict:
        """测试System Prompt构建"""
        self.log("测试System Prompt构建", "INFO")
        
        try:
            from xi_system.prompts.builder import StructuredPromptBuilder
            
            # 初始化构建器
            builder = StructuredPromptBuilder()
            self.log("StructuredPromptBuilder初始化成功", "SUCCESS")
            
            # 构建System Prompt
            system_prompt = builder.build_system_prompt(test_input, [])
            prompt_length = len(system_prompt)
            
            # 验证关键组件
            components = {
                "静态人格": "# 曦的核心人格与世界观定义" in system_prompt,
                "对话感知": "## 对话感知" in system_prompt,
                "知识索引": "# 知识索引" in system_prompt,
                "可用工具": "# 可用工具" in system_prompt,
                "时间信息": "## 时间信息" in system_prompt,
                "检索记忆": "## 长期检索记忆" in system_prompt
            }
            
            missing_components = [name for name, present in components.items() if not present]
            
            if not missing_components:
                self.log(f"System Prompt构建成功 - 长度: {prompt_length}字符", "SUCCESS")
                
                # 保存System Prompt到dev_monitor目录
                prompt_file = self.dev_monitor_dir / f"system_prompt_{self.timestamp}.txt"
                with open(prompt_file, 'w', encoding='utf-8') as f:
                    f.write(system_prompt)
                self.log(f"System Prompt已保存: {prompt_file}")
                
                return {
                    "success": True,
                    "length": prompt_length,
                    "components": components,
                    "file": str(prompt_file)
                }
            else:
                self.log(f"System Prompt缺失组件: {missing_components}", "ERROR")
                return {
                    "success": False,
                    "missing_components": missing_components
                }
                
        except Exception as e:
            self.log(f"System Prompt测试失败: {e}", "ERROR")
            return {"success": False, "error": str(e)}
    
    def test_tools(self) -> dict:
        """测试工具系统 (V0.9: 测试所有新工具)"""
        self.log("测试工具系统", "INFO")

        try:
            from xi_system.tools import get_tool_registry, get_tool_executor

            registry = get_tool_registry()
            executor = get_tool_executor()

            tools = registry.get_available_tools()
            tool_names = [tool['function']['name'] for tool in tools]

            self.log(f"发现工具: {tool_names}", "INFO")

            results = {
                "success": True,
                "tools_available": len(tools),
                "tool_names": tool_names
            }

            # 测试read_note工具
            try:
                test_result = executor.execute_tool_safe('read_note', filename="memento-memory-truth-and-ai-existence.md")
                if "记忆碎片：记忆、真实与AI存在的灵魂拷问" in test_result and len(test_result) > 1000:
                    results["read_note_success"] = True
                    self.log("read_note工具测试成功", "SUCCESS")
                else:
                    results["read_note_success"] = False
                    self.log(f"read_note返回异常: {test_result[:100]}", "WARNING")
            except Exception as e:
                results["read_note_success"] = False
                self.log(f"read_note测试失败: {e}", "ERROR")

            # 测试write_note工具
            try:
                test_filename = f"test-note-{int(time.time())}.md"
                test_content = "# 测试笔记\n\n这是一个自动生成的测试笔记。"

                write_result = executor.execute_tool_safe('write_note', filename=test_filename, content=test_content)
                if "创建成功" in write_result:
                    results["write_note_success"] = True
                    self.log("write_note工具测试成功", "SUCCESS")

                    # 验证能否读取刚创建的笔记
                    read_back = executor.execute_tool_safe('read_note', filename=test_filename)
                    if test_content in read_back:
                        self.log("write_note创建的笔记可以正常读取", "SUCCESS")
                    else:
                        self.log("write_note创建的笔记读取异常", "WARNING")
                else:
                    results["write_note_success"] = False
                    self.log(f"write_note测试失败: {write_result}", "ERROR")
            except Exception as e:
                results["write_note_success"] = False
                self.log(f"write_note测试异常: {e}", "ERROR")

            # 测试web_search工具
            try:
                # 检查是否有API key
                if os.getenv('TAVILY_API_KEY'):
                    search_result = executor.execute_tool_safe('web_search', query="AI news", max_results=2)
                    if "搜索结果" in search_result and "❌" not in search_result:
                        results["web_search_success"] = True
                        self.log("web_search工具测试成功", "SUCCESS")
                    else:
                        results["web_search_success"] = False
                        self.log(f"web_search返回异常: {search_result[:100]}", "WARNING")
                else:
                    results["web_search_success"] = "skipped"
                    self.log("跳过web_search测试 (未设置TAVILY_API_KEY)", "INFO")
            except Exception as e:
                results["web_search_success"] = False
                self.log(f"web_search测试异常: {e}", "ERROR")

            # 更新总体成功状态
            tool_successes = [
                results.get("read_note_success", False),
                results.get("write_note_success", False),
                results.get("web_search_success", "skipped") != False
            ]
            results["success"] = all(tool_successes)

            return results

        except Exception as e:
            self.log(f"工具系统测试失败: {e}", "ERROR")
            return {"success": False, "error": str(e)}
    
    def test_database(self) -> dict:
        """测试数据库连接"""
        self.log("测试数据库连接", "INFO")
        
        try:
            from xi_system.memory.providers import MongoProvider
            
            mongo_uri = os.getenv("MONGO_URI")
            if not mongo_uri:
                self.log("MONGO_URI环境变量未设置", "ERROR")
                return {"success": False, "error": "Missing MONGO_URI"}
            
            provider = MongoProvider(uri=mongo_uri, db_name="xi_db")
            self.log("MongoProvider初始化成功", "SUCCESS")
            
            # 获取统计信息
            stats = provider.get_stats()
            self.log(f"数据库统计: {stats.get('total_memories', 0)}条记录", "SUCCESS")
            
            return {
                "success": True,
                "stats": stats
            }
            
        except Exception as e:
            self.log(f"数据库测试失败: {e}", "ERROR")
            return {"success": False, "error": str(e)}

    def test_v09_cognitive_abilities(self) -> dict:
        """测试V0.9四大认知能力"""
        self.log("测试V0.9四大认知能力", "INFO")

        results = {
            "success": True,
            "metabolism": False,
            "growth": False,
            "perception": False,
            "introspection": False
        }

        try:
            # 1. 测试新陈代谢 (记忆策展)
            self.log("测试新陈代谢能力", "INFO")
            try:
                from xi_system.memory.curation import calculate_signal, should_archive_memory, Archiver
                from xi_system.memory.models import MemoryRecord, MessageRole
                from datetime import datetime, timedelta

                test_memory = MemoryRecord(
                    id='test-metabolism',
                    content='重要决策：实现V0.9的四大认知能力',
                    role=MessageRole.YU,
                    timestamp=datetime.now() - timedelta(days=1),
                    metadata={}
                )

                signal = calculate_signal(test_memory, relevance_score=0.8)
                should_archive = should_archive_memory(test_memory)

                if 0.0 <= signal <= 1.0 and isinstance(should_archive, bool):
                    results["metabolism"] = True
                    self.log(f"新陈代谢测试成功 - 信号强度: {signal:.3f}", "SUCCESS")
                else:
                    self.log("新陈代谢测试失败 - 返回值异常", "ERROR")

            except Exception as e:
                self.log(f"新陈代谢测试异常: {e}", "ERROR")

            # 2. 测试生长能力 (知识扩展)
            self.log("测试生长能力", "INFO")
            try:
                from xi_system.tools import get_tool_executor
                executor = get_tool_executor()

                test_filename = f"v09-growth-test-{int(time.time())}.md"
                test_content = "# V0.9生长能力测试\n\n这验证了曦的知识扩展能力。"

                write_result = executor.execute_tool_safe('write_note', filename=test_filename, content=test_content)
                if "创建成功" in write_result:
                    results["growth"] = True
                    self.log("生长能力测试成功", "SUCCESS")
                else:
                    self.log(f"生长能力测试失败: {write_result}", "ERROR")

            except Exception as e:
                self.log(f"生长能力测试异常: {e}", "ERROR")

            # 3. 测试感知能力 (网络搜索)
            self.log("测试感知能力", "INFO")
            try:
                from xi_system.tools import get_tool_executor
                executor = get_tool_executor()

                if os.getenv('TAVILY_API_KEY'):
                    search_result = executor.execute_tool_safe('web_search', query="latest AI developments", max_results=1)
                    if "搜索结果" in search_result and "❌" not in search_result:
                        results["perception"] = True
                        self.log("感知能力测试成功", "SUCCESS")
                    else:
                        self.log(f"感知能力测试失败: {search_result[:100]}", "ERROR")
                else:
                    results["perception"] = "skipped"
                    self.log("感知能力测试跳过 (未设置TAVILY_API_KEY)", "INFO")

            except Exception as e:
                self.log(f"感知能力测试异常: {e}", "ERROR")

            # 4. 测试内省能力 (元认知反思)
            self.log("测试内省能力", "INFO")
            try:
                from xi_system.agents.xi_omega_agent import OmegaAgent, ReflectionResult
                from xi_system.memory.models import MemoryRecord, MessageRole
                from xi_system.service import ServiceContainer

                # 创建模拟对话
                conversation = [
                    MemoryRecord(
                        id='test-conv-1',
                        content='测试V0.9的内省能力',
                        role=MessageRole.YU,
                        timestamp=datetime.now(),
                        metadata={}
                    )
                ]

                # 检查是否有LLM服务
                try:
                    container = ServiceContainer()
                    container.initialize()
                    llm_service = container.get_service('llm')

                    omega_agent = OmegaAgent(llm_service)
                    reflection_result = omega_agent.reflect(conversation)

                    if isinstance(reflection_result, ReflectionResult):
                        results["introspection"] = True
                        self.log("内省能力测试成功", "SUCCESS")
                    else:
                        self.log("内省能力测试失败 - 返回类型错误", "ERROR")

                except Exception as e:
                    results["introspection"] = "skipped"
                    self.log(f"内省能力测试跳过 (LLM服务不可用): {e}", "INFO")

            except Exception as e:
                self.log(f"内省能力测试异常: {e}", "ERROR")

            # 计算总体成功率
            ability_results = [
                results["metabolism"],
                results["growth"],
                results["perception"] if results["perception"] != "skipped" else True,
                results["introspection"] if results["introspection"] != "skipped" else True
            ]
            results["success"] = all(ability_results)

            success_count = sum(1 for r in ability_results if r is True)
            total_count = len(ability_results)
            self.log(f"V0.9认知能力测试完成: {success_count}/{total_count} 通过", "SUCCESS" if results["success"] else "WARNING")

            return results

        except Exception as e:
            self.log(f"V0.9认知能力测试失败: {e}", "ERROR")
            return {"success": False, "error": str(e)}
    
    def run_comprehensive_test(self, test_message: str = "你好曦，请介绍一下你自己") -> dict:
        """运行综合测试"""
        self.log("开始综合测试", "INFO")
        
        results = {}
        
        # 1. 健康检查
        results["health"] = self.check_server_health()
        
        if not results["health"]:
            self.log("服务器不可用，跳过其他测试", "ERROR")
            return results
        
        # 2. System Prompt测试
        results["system_prompt"] = self.test_system_prompt()
        
        # 3. 工具系统测试
        results["tools"] = self.test_tools()
        
        # 4. 数据库测试
        results["database"] = self.test_database()

        # 5. V0.9认知能力测试
        results["v09_cognitive"] = self.test_v09_cognitive_abilities()

        # 6. 基本对话测试
        results["basic_chat"] = self.test_basic_chat(test_message)

        # 7. 流式对话测试
        results["streaming_chat"] = self.test_streaming_chat(test_message)

        # 生成测试报告
        self._generate_test_report(results)
        
        return results
    
    def _generate_test_report(self, results: dict):
        """生成测试报告"""
        report_file = self.reports_dir / f"test_report_{self.timestamp}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"# Xi ContextOS 测试报告\n\n")
            f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            for test_name, result in results.items():
                if isinstance(result, dict):
                    status = "✅ 通过" if result.get("success", False) else "❌ 失败"
                elif isinstance(result, bool):
                    status = "✅ 通过" if result else "❌ 失败"
                else:
                    status = "❓ 未知"

                f.write(f"## {test_name.replace('_', ' ').title()}\n")
                f.write(f"**状态**: {status}\n\n")

                if isinstance(result, dict):
                    for key, value in result.items():
                        if key != "success":
                            f.write(f"- **{key}**: {value}\n")
                f.write("\n")
        
        self.log(f"测试报告已生成: {report_file}", "SUCCESS")

def main():
    parser = argparse.ArgumentParser(description="Xi ContextOS 通用测试工具")
    parser.add_argument("--url", default="http://localhost:8000", help="服务器URL")
    parser.add_argument("--message", default="你好曦，请介绍一下你自己", help="测试消息")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--health", action="store_true", help="只检查健康状态")
    parser.add_argument("--prompt", action="store_true", help="只测试System Prompt")
    parser.add_argument("--tools", action="store_true", help="只测试工具系统")
    parser.add_argument("--database", action="store_true", help="只测试数据库")
    parser.add_argument("--chat", action="store_true", help="只测试对话功能")
    parser.add_argument("--v09", action="store_true", help="只测试V0.9认知能力")
    parser.add_argument("--all", action="store_true", help="运行所有测试")
    
    args = parser.parse_args()
    
    tester = XiTester(base_url=args.url, verbose=args.verbose)
    
    # 根据参数选择测试
    if args.health:
        tester.check_server_health()
    elif args.prompt:
        tester.test_system_prompt()
    elif args.tools:
        tester.test_tools()
    elif args.database:
        tester.test_database()
    elif args.chat:
        tester.test_basic_chat(args.message)
        tester.test_streaming_chat(args.message)
    elif args.v09:
        tester.test_v09_cognitive_abilities()
    elif args.all or not any([args.health, args.prompt, args.tools, args.database, args.chat, args.v09]):
        # 默认运行综合测试
        tester.run_comprehensive_test(args.message)
    
    print("\n🎯 测试完成！")

if __name__ == "__main__":
    main()
