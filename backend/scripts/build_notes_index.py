#!/usr/bin/env python3
"""
知识库索引构建脚本

这个脚本会扫描 xi_system/memory/notes/ 目录下的所有 .md 文件，
提取每个文件的标题和摘要，然后生成一个 Markdown 索引文件。

使用方法：
python scripts/build_notes_index.py
"""

import os
from pathlib import Path
from datetime import datetime, timezone

# 配置路径 (V0.82)
NOTES_DIR = Path("xi_system/memory/notes")
INDEX_FILE = Path("xi_system/prompts/xi/knowledge_index.md")

def extract_title_from_markdown(content: str, filename: str) -> str:
    """从Markdown内容中提取标题"""
    lines = content.split('\n')
    for line in lines:
        line = line.strip()
        if line.startswith('# '):
            return line[2:].strip()
    
    # 回退到文件名
    return filename.replace('.md', '').replace('-', ' ').title()

def extract_summary_from_markdown(content: str) -> str:
    """从Markdown内容中提取摘要"""
    lines = content.split('\n')
    
    # 查找标题后的第一个引用块
    found_title = False
    for line in lines:
        line = line.strip()
        if line.startswith('# '):
            found_title = True
            continue
        
        if found_title and line.startswith('> '):
            return line[2:].strip()
    
    # 回退：提取第一个有意义的段落
    for line in lines:
        line = line.strip()
        if (line and 
            not line.startswith('#') and 
            not line.startswith('<!--') and 
            not line.startswith('```') and
            len(line) > 10):
            return line[:150] + "..." if len(line) > 150 else line
    
    return "暂无摘要"

def build_index():
    """构建知识索引Markdown文件"""
    print("🔍 开始构建知识索引...")
    
    # 检查目录是否存在
    if not NOTES_DIR.exists():
        print(f"❌ 笔记目录不存在: {NOTES_DIR}")
        return False
    
    # 构建Markdown内容
    md_parts = [
        "# 知识索引 (Knowledge Index)",
        "",
        "<!-- 这是我可查阅的知识笔记索引。（是我和禹过去共同探索的一些有价值的总结提炼的笔记）我可以通过调用`read_note`工具来阅读它们的全文。 -->",
        "",
        f"**生成时间**: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')} UTC+8",
        ""
    ]
    
    # 扫描所有Markdown文件
    markdown_files = sorted(NOTES_DIR.glob("*.md"))
    
    if not markdown_files:
        print(f"⚠️ 在 {NOTES_DIR} 中没有找到Markdown文件")
        return False
    
    print(f"📚 找到 {len(markdown_files)} 个笔记文件")
    
    # 处理每个文件
    for md_file in markdown_files:
        try:
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            title = extract_title_from_markdown(content, md_file.name)
            summary = extract_summary_from_markdown(content)
            
            # 添加到索引
            md_parts.append(f"## {title}")
            md_parts.append(f"**文件名**: `{md_file.name}`")
            md_parts.append(f"**摘要**: {summary}")
            md_parts.append("")
            
            print(f"✅ 处理完成: {md_file.name}")
            
        except Exception as e:
            print(f"❌ 处理文件失败 {md_file.name}: {e}")
            continue
    
    # 写入索引文件
    try:
        # 确保目录存在
        INDEX_FILE.parent.mkdir(parents=True, exist_ok=True)
        
        with open(INDEX_FILE, 'w', encoding='utf-8') as f:
            f.write("\n".join(md_parts))
        
        print(f"🎉 知识索引构建成功: {INDEX_FILE}")
        print(f"📊 总计索引笔记: {len(markdown_files)} 篇")
        return True
        
    except Exception as e:
        print(f"❌ 写入索引文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Xi ContextOS 知识索引构建器")
    print("=" * 50)
    
    success = build_index()
    
    if success:
        print("\n✅ 索引构建完成！")
        print(f"📁 索引文件位置: {INDEX_FILE}")
        print("\n💡 现在可以重启Xi系统以使用新的知识索引")
    else:
        print("\n❌ 索引构建失败")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
