"""
Xi Intelligent Agent System - Backend Package

Main backend package for the Xi intelligent agent system.
Provides unified access to the complete system through clean interfaces.

This package contains:
- xi_system/: Core system modules
- main.py: FastAPI application entry point
- scripts/: Development and maintenance scripts

Quick Start:
    from backend.xi_system import XiCore, initialize_services
    
    # Initialize system
    container = initialize_services()
    xi_core = Xi<PERSON>ore(container)
    
    # Process input
    response = ""
    for chunk in xi_core.process_input_stream("Hello"):
        response += chunk
    
    print(response)

For web service:
    from backend.main import app
    # Run with: uvicorn backend.main:app --reload
"""

# Re-export main system components for convenience
from .xi_system import (
    XiCore,
    initialize_services,
    cleanup_services,
    get_container,
    MemoryRecord,
    MessageRole,
    YU, XI,
    get_tool_executor,
    StructuredPromptBuilder
)

__all__ = [
    'XiCore',
    'initialize_services', 
    'cleanup_services',
    'get_container',
    'MemoryRecord',
    'MessageRole',
    'YU', 'XI',
    'get_tool_executor',
    'StructuredPromptBuilder'
]
