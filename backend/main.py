# main.py

"""
Xi Intelligent Agent System V0.83: ContextOS Architecture Purification

FastAPI web service entry point for the Xi ContextOS.
This file initializes the FastAPI application with the V0.83 architecture
featuring dependency injection, service orchestration, and agent delegation.
"""

import uvicorn
import logging
import os
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

from xi_system.api.routes import router as api_router

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Load environment variables at the very beginning
load_dotenv()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Lifespan context manager for FastAPI application.
    Handles startup and shutdown events for ContextOS.
    """
    # Startup
    logger.info("--- Xi System is starting up ---")

    try:
        # Verify environment variables
        required_env_vars = ["GEMINI_API_KEY", "MONGO_URI"]
        missing_vars = [var for var in required_env_vars if not os.getenv(var)]

        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")

        logger.info("Environment variables verified")

        # The XiCore will be initialized on first request (singleton pattern)
        # V0.83: Now uses ServiceContainer for dependency injection
        logger.info("System components ready for initialization")

        logger.info("--- Xi System is ready to serve ---")

    except Exception as e:
        logger.error(f"Startup failed: {e}")
        raise

    yield  # Application runs here

    # Shutdown
    logger.info("--- Xi System is shutting down ---")

    # Cleanup XiCore resources if needed
    try:
        from xi_system.api.routes import _xi_core_instance, _service_container
        if _service_container:
            _service_container.shutdown()
            logger.info("ServiceContainer shutdown complete")
        if _xi_core_instance:
            logger.info("XiCore resources cleaned up")
    except Exception as e:
        logger.warning(f"Error during cleanup: {e}")


# Create the FastAPI app instance with lifespan
app = FastAPI(
    title="Xi Intelligent Agent System",
    description="The API for interacting with Xi (曦), the AI soul companion for Yu (禹). Features architecture with dependency injection, service orchestration, and agent delegation.",
    version="...",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware for web client support
origins = [
    "http://localhost:5173",  # Vite default port
    "http://localhost:3000",  # Create React App default port
    "http://127.0.0.1:5173",  # Alternative localhost format
    "http://127.0.0.1:3000",  # Alternative localhost format
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)





# Include the API router in the main FastAPI app
# All routes defined in api/routes.py will be available under the "/api/v1" prefix
app.include_router(api_router, prefix="/api/v1", tags=["chat"])


@app.get("/", summary="Root endpoint for health check")
async def read_root():
    """
    Root endpoint providing basic service information.

    Returns:
        Dict with service status and basic information
    """
    return {
        "service": "Xi Intelligent Agent System - ContextOS",
        "version": "...",
        "status": "Xi System is running",
        "features": [
            "Architecture Purification",
            "Dependency Injection",
            "Service Orchestration",
            "Agent Delegation",
            "Modular Tool System",
            "Message Processing Pipeline"
        ],
        "architecture": "ServiceContainer + XiCore + Agents + Tools + Memory",
        "docs": "/docs",
        "api_base": "/api/v1"
    }


# This block allows running the server directly with `python main.py`
if __name__ == "__main__":
    # Use uvicorn to run the FastAPI app
    # Host should be "0.0.0.0" to be accessible from the network
    # Port can be 8000
    logger.info("Starting Xi API server...")
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        reload=False  # Set to True for development
    )
