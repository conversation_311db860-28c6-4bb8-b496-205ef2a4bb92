# Xi System Environment Variables
# Copy this file to .env and fill in your sensitive credentials.

# =============================================================================
# Environment Configuration
# =============================================================================
# The environment for Xi System (e.g., development, production)
# This can be used to load environment-specific config files like config.production.yml
XI_ENV=development

# =============================================================================
# Sensitive Credentials
# =============================================================================
# Google Gemini API Key
GEMINI_API_KEY=your_gemini_api_key_here

# MongoDB Connection URI
MONGO_URI=mongodb://localhost:27017/xi_db

# Tavily Search API Key
TAVILY_API_KEY=your_tavily_api_key_here

# OpenAI API Key (optional, for future use)
OPENAI_API_KEY=your_openai_api_key_here

# =============================================================================
# Future Provider API Keys (Reserved for Extension)
# =============================================================================
# GOOGLE_API_KEY=your_google_api_key_here
# AZURE_API_KEY=your_azure_api_key_here
# AZURE_ENDPOINT=https://your-resource.openai.azure.com/
# ANTHROPIC_API_KEY=your_anthropic_api_key_here

# =============================================================================
# Environment-Specific Overrides (Optional)
# =============================================================================
# Uncomment and modify these to override default config values for specific environments
# LOG_LEVEL=DEBUG
# DEBUG_MODE=true
# TEST_MODE=true
