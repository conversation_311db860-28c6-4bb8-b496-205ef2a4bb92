# xi_system/service/embedding/service.py

"""
统一嵌入服务

统一的嵌入向量生成服务，作为工厂和管理器。
根据配置选择和管理不同的嵌入提供商，为系统提供统一的嵌入接口。

设计原则：
- 工厂模式：根据配置创建合适的提供商
- 统一接口：为所有嵌入需求提供一致的API
- 配置驱动：通过配置文件控制提供商选择
- 可扩展性：易于添加新的嵌入提供商

支持的提供商：
- local: 本地 sentence-transformers 模型
- openai: OpenAI 嵌入API (text-embedding-3-small, text-embedding-3-large, text-embedding-ada-002)
- 未来可扩展：google, azure, anthropic 等云端API
"""

import logging
from typing import List, Optional, Dict, Any
from ..container import ServiceInterface
from ..config import ConfigService
from .providers.base import EmbeddingProvider
from .providers.local import LocalEmbeddingProvider

# 可选导入OpenAI提供商
try:
    from .providers.openai import OpenAIEmbeddingProvider
    _OPENAI_AVAILABLE = True
except ImportError:
    _OPENAI_AVAILABLE = False

logger = logging.getLogger(__name__)


class EmbeddingService(ServiceInterface):
    """
    统一的嵌入服务。
    根据配置管理和提供不同的嵌入模型提供商。
    """
    
    def __init__(self, config_service: ConfigService):
        """
        初始化嵌入服务
        
        Args:
            config_service: 配置服务实例
        """
        self.config = config_service
        self.provider: Optional[EmbeddingProvider] = None
        self._initialized = False
        
        logger.debug("EmbeddingService created")

    def initialize(self) -> None:
        """初始化嵌入服务，根据配置选择和初始化提供商"""
        if self._initialized:
            logger.debug("EmbeddingService already initialized")
            return

        try:
            # 获取配置
            provider_name = self.config.get('embedding.provider', 'local').lower()
            logger.info(f"Initializing EmbeddingService with provider: {provider_name}")

            # 根据配置创建提供商
            if provider_name == 'local':
                model_name = self.config.get('embedding.providers.local.model_name', 'all-MiniLM-L6-v2')
                self.provider = LocalEmbeddingProvider(model_name=model_name)
                logger.debug(f"Created LocalEmbeddingProvider with model: {model_name}")

            elif provider_name == 'openai':
                if not _OPENAI_AVAILABLE:
                    raise ValueError("OpenAI provider requested but openai library not available. Install with: pip install openai")

                # 获取OpenAI配置
                api_key = self.config.get('embedding.providers.openai.api_key')
                if not api_key:
                    raise ValueError("OpenAI API key not configured. Set OPENAI_API_KEY environment variable.")

                model_name = self.config.get('embedding.providers.openai.model', 'text-embedding-3-small')
                dimensions = self.config.get('embedding.providers.openai.dimensions')
                timeout = self.config.get('embedding.providers.openai.request_timeout', 30)
                max_retries = self.config.get('embedding.providers.openai.max_retries', 3)

                self.provider = OpenAIEmbeddingProvider(
                    api_key=api_key,
                    model_name=model_name,
                    dimensions=dimensions,
                    timeout=timeout,
                    max_retries=max_retries
                )
                logger.debug(f"Created OpenAIEmbeddingProvider with model: {model_name}, dimensions: {dimensions}")

            # elif provider_name == 'google':
            #     # 未来扩展：Google 嵌入提供商
            #     api_key = self.config.get('google_api_key')
            #     model_name = self.config.get('google_embedding_model', 'textembedding-gecko')
            #     self.provider = GoogleEmbeddingProvider(api_key=api_key, model_name=model_name)
            #     logger.debug(f"Created GoogleEmbeddingProvider with model: {model_name}")

            else:
                available_providers = ['local']
                if _OPENAI_AVAILABLE:
                    available_providers.append('openai')
                raise ValueError(f"Unsupported embedding provider: {provider_name}. Available providers: {available_providers}")

            # 初始化提供商
            self.provider.initialize()
            self._initialized = True
            
            logger.info("EmbeddingService initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize EmbeddingService: {e}")
            self.cleanup()
            raise RuntimeError(f"EmbeddingService initialization failed: {e}")

    def cleanup(self) -> None:
        """清理嵌入服务资源"""
        try:
            if self.provider:
                self.provider.cleanup()
                self.provider = None
            
            self._initialized = False
            logger.info("EmbeddingService cleaned up")
            
        except Exception as e:
            logger.error(f"Error during EmbeddingService cleanup: {e}")

    def generate_embedding(self, text: str) -> List[float]:
        """
        为单个文本生成嵌入向量
        
        Args:
            text: 输入文本
            
        Returns:
            嵌入向量（浮点数列表）
            
        Raises:
            RuntimeError: 如果服务未初始化或嵌入生成失败
            ValueError: 如果输入文本无效
        """
        if not self._initialized or not self.provider:
            raise RuntimeError("EmbeddingService not initialized. Call initialize() first.")
        
        try:
            return self.provider.generate_embedding(text)
        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            raise

    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        为多个文本批量生成嵌入向量
        
        Args:
            texts: 输入文本列表
            
        Returns:
            嵌入向量列表，与输入文本一一对应
            
        Raises:
            RuntimeError: 如果服务未初始化或嵌入生成失败
            ValueError: 如果输入文本列表无效
        """
        if not self._initialized or not self.provider:
            raise RuntimeError("EmbeddingService not initialized. Call initialize() first.")
        
        try:
            return self.provider.generate_embeddings(texts)
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
            raise

    def get_model_info(self) -> Dict[str, Any]:
        """
        获取当前嵌入模型信息
        
        Returns:
            包含模型信息的字典
        """
        base_info = {
            "service_initialized": self._initialized,
            "provider_available": self.provider is not None
        }
        
        if self.provider:
            provider_info = self.provider.get_model_info()
            base_info.update(provider_info)
        
        return base_info

    def health_check(self) -> Dict[str, Any]:
        """
        执行健康检查
        
        Returns:
            健康检查结果
        """
        try:
            if not self._initialized:
                return {
                    "status": "error",
                    "message": "EmbeddingService not initialized"
                }
            
            if not self.provider:
                return {
                    "status": "error",
                    "message": "No embedding provider available"
                }
            
            # 尝试生成一个测试嵌入
            test_embedding = self.provider.generate_embedding("test")
            
            return {
                "status": "healthy",
                "message": "EmbeddingService is working correctly",
                "embedding_dimension": len(test_embedding),
                "provider_info": self.provider.get_model_info()
            }
            
        except Exception as e:
            logger.error(f"EmbeddingService health check failed: {e}")
            return {
                "status": "error",
                "message": f"Health check failed: {e}"
            }

    def is_initialized(self) -> bool:
        """检查服务是否已初始化"""
        return self._initialized and self.provider is not None
