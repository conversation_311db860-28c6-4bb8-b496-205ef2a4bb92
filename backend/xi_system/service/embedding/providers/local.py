# xi_system/service/embedding/providers/local.py

"""
本地嵌入提供商实现

使用本地 sentence-transformers 模型生成嵌入向量。
将原本分散在 MongoProvider 和 Retriever 中的嵌入逻辑统一封装。

特性：
- 延迟加载模型，节省内存
- 支持单个和批量嵌入生成
- 健壮的错误处理
- 可配置的模型名称

支持的模型：
- all-MiniLM-L6-v2 (默认，384维)
- all-mpnet-base-v2 (768维，更高质量)
- paraphrase-multilingual-MiniLM-L12-v2 (多语言支持)
"""

import logging
from typing import List, Optional
from sentence_transformers import SentenceTransformer
from .base import EmbeddingProvider

logger = logging.getLogger(__name__)


class LocalEmbeddingProvider(EmbeddingProvider):
    """使用本地 sentence-transformers 模型生成嵌入的提供商。"""

    def __init__(self, model_name: str = 'all-MiniLM-L6-v2'):
        """
        初始化本地嵌入提供商
        
        Args:
            model_name: sentence-transformers 模型名称
        """
        self.model_name = model_name
        self._model: Optional[SentenceTransformer] = None
        self._initialized = False
        
        logger.debug(f"LocalEmbeddingProvider created with model: {model_name}")

    def initialize(self) -> None:
        """初始化并加载 sentence-transformers 模型"""
        if self._initialized:
            return
        
        try:
            logger.info(f"Loading local embedding model: {self.model_name}")
            self._model = SentenceTransformer(self.model_name)
            self._initialized = True
            logger.info("Local embedding model loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load local embedding model: {e}")
            raise RuntimeError(f"Embedding model loading failed: {e}")

    def _get_model(self) -> SentenceTransformer:
        """获取模型实例，如果未初始化则自动初始化"""
        if not self._initialized or self._model is None:
            self.initialize()
        return self._model

    def generate_embedding(self, text: str) -> List[float]:
        """
        为单个文本生成嵌入向量
        
        Args:
            text: 输入文本
            
        Returns:
            嵌入向量（浮点数列表）
            
        Raises:
            ValueError: 如果输入文本为空
            RuntimeError: 如果嵌入生成失败
        """
        if not text or not text.strip():
            raise ValueError("Input text cannot be empty")
        
        try:
            model = self._get_model()
            embedding = model.encode(text, convert_to_tensor=False)
            result = embedding.tolist()
            
            logger.debug(f"Generated embedding for text (length: {len(text)}) -> vector dim: {len(result)}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to generate embedding for text: {e}")
            raise RuntimeError(f"Embedding generation failed: {e}")

    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        为多个文本批量生成嵌入向量
        
        Args:
            texts: 输入文本列表
            
        Returns:
            嵌入向量列表，与输入文本一一对应
            
        Raises:
            ValueError: 如果输入文本列表为空或包含空文本
            RuntimeError: 如果嵌入生成失败
        """
        if not texts:
            raise ValueError("Input texts list cannot be empty")
        
        # 验证所有文本都不为空
        for i, text in enumerate(texts):
            if not text or not text.strip():
                raise ValueError(f"Text at index {i} cannot be empty")
        
        try:
            model = self._get_model()
            embeddings = model.encode(texts, convert_to_tensor=False)
            results = [emb.tolist() for emb in embeddings]
            
            logger.debug(f"Generated embeddings for {len(texts)} texts -> vector dim: {len(results[0]) if results else 0}")
            return results
            
        except Exception as e:
            logger.error(f"Failed to generate embeddings for {len(texts)} texts: {e}")
            raise RuntimeError(f"Batch embedding generation failed: {e}")

    def cleanup(self) -> None:
        """清理模型资源"""
        if self._model is not None:
            # sentence-transformers 模型通常不需要显式清理
            # 但我们可以清除引用以帮助垃圾回收
            self._model = None
            self._initialized = False
            logger.info("Local embedding model cleaned up")

    def get_model_info(self) -> dict:
        """获取模型信息"""
        base_info = super().get_model_info()
        base_info.update({
            "model_name": self.model_name,
            "provider_type": "local",
            "library": "sentence-transformers"
        })
        
        if self._initialized and self._model is not None:
            try:
                # 尝试获取模型的维度信息
                test_embedding = self._model.encode("test", convert_to_tensor=False)
                base_info["embedding_dimension"] = len(test_embedding)
            except Exception:
                base_info["embedding_dimension"] = "unknown"
        
        return base_info
