# xi_system/service/embedding/providers/__init__.py

"""
嵌入提供商统一导出

提供各种嵌入模型提供商的统一访问接口。
包含抽象基类和具体实现。

使用示例:
    from xi_system.service.embedding.providers import EmbeddingProvider, LocalEmbeddingProvider
    
    # 创建本地嵌入提供商
    provider = LocalEmbeddingProvider(model_name='all-MiniLM-L6-v2')
    provider.initialize()
    
    # 生成嵌入
    embedding = provider.generate_embedding("Hello world")
"""

from .base import EmbeddingProvider
from .local import LocalEmbeddingProvider

# 可选导入OpenAI提供商（需要openai库）
try:
    from .openai import OpenAIEmbeddingProvider
    _OPENAI_AVAILABLE = True
except ImportError:
    _OPENAI_AVAILABLE = False

# 导出所有公共接口
__all__ = [
    # 抽象基类
    'EmbeddingProvider',

    # 具体实现
    'LocalEmbeddingProvider'
]

# 如果OpenAI库可用，添加到导出列表
if _OPENAI_AVAILABLE:
    __all__.append('OpenAIEmbeddingProvider')
