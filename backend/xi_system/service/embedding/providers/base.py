# xi_system/service/embedding/providers/base.py

"""
嵌入提供商抽象基类

定义所有嵌入模型提供商都必须遵守的统一接口。
遵循"面向接口编程"原则，确保不同提供商的可互换性。

设计原则：
- 统一的接口定义
- 支持单个和批量嵌入生成
- 延迟初始化模式
- 清晰的错误处理

支持的提供商类型：
- 本地模型（sentence-transformers）
- 云端API（OpenAI、Google、Azure等）
- 自定义嵌入服务
"""

from abc import ABC, abstractmethod
from typing import List


class EmbeddingProvider(ABC):
    """
    所有嵌入模型提供商的抽象基类。
    定义了生成嵌入向量的统一接口。
    
    所有具体实现都必须遵循这个接口，确保可互换性。
    """

    @abstractmethod
    def initialize(self) -> None:
        """
        初始化提供商，如加载模型或API客户端。
        
        这个方法应该是幂等的，多次调用不会产生副作用。
        如果初始化失败，应该抛出适当的异常。
        
        Raises:
            RuntimeError: 如果初始化失败
        """
        pass

    @abstractmethod
    def generate_embedding(self, text: str) -> List[float]:
        """
        为单个文本生成嵌入向量。

        Args:
            text: 输入文本，不能为空

        Returns:
            表示文本的嵌入向量（浮点数列表）
            
        Raises:
            RuntimeError: 如果嵌入生成失败
            ValueError: 如果输入文本无效
        """
        pass

    @abstractmethod
    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        为多个文本批量生成嵌入向量（优化性能）。

        Args:
            texts: 输入的文本列表，不能为空

        Returns:
            一个嵌入向量的列表，与输入文本一一对应
            
        Raises:
            RuntimeError: 如果嵌入生成失败
            ValueError: 如果输入文本列表无效
        """
        pass

    def cleanup(self) -> None:
        """
        清理资源，如关闭连接或释放模型。
        
        这个方法是可选的，默认实现为空。
        子类可以重写此方法来实现特定的清理逻辑。
        """
        pass

    def get_model_info(self) -> dict:
        """
        获取模型信息。
        
        Returns:
            包含模型信息的字典，如模型名称、版本等
        """
        return {
            "provider_type": self.__class__.__name__,
            "initialized": hasattr(self, '_initialized') and self._initialized
        }
