# xi_system/service/embedding/providers/openai.py

"""
OpenAI嵌入提供商实现

使用OpenAI的嵌入API生成嵌入向量。
支持text-embedding-ada-002等模型。

特性：
- 云端API调用，无需本地模型
- 支持批量处理
- 自动重试和错误处理
- 可配置的API密钥和模型
"""

import logging
import openai
from typing import List, Optional
from .base import EmbeddingProvider

logger = logging.getLogger(__name__)


class OpenAIEmbeddingProvider(EmbeddingProvider):
    """使用OpenAI API生成嵌入的提供商。"""

    def __init__(self, api_key: str, model_name: str = 'text-embedding-3-small',
                 dimensions: Optional[int] = None, timeout: int = 30, max_retries: int = 3):
        """
        初始化OpenAI嵌入提供商

        Args:
            api_key: OpenAI API密钥
            model_name: OpenAI嵌入模型名称
            dimensions: 嵌入向量维度（仅text-embedding-3系列支持）
            timeout: API请求超时时间（秒）
            max_retries: 最大重试次数
        """
        self.api_key = api_key
        self.model_name = model_name
        self.dimensions = dimensions
        self.timeout = timeout
        self.max_retries = max_retries
        self._client: Optional[openai.OpenAI] = None
        self._initialized = False

        # 验证维度参数
        if dimensions is not None:
            valid_dimensions = [256, 512, 1024, 1536]
            if dimensions not in valid_dimensions:
                raise ValueError(f"嵌入维度必须是以下值之一: {valid_dimensions}, 当前值: {dimensions}")

            if not model_name.startswith('text-embedding-3-'):
                logger.warning(f"模型 {model_name} 可能不支持自定义维度参数，仅text-embedding-3系列支持")

        logger.debug(f"OpenAIEmbeddingProvider created with model: {model_name}, dimensions: {dimensions}")

    def initialize(self) -> None:
        """初始化OpenAI客户端"""
        if self._initialized:
            return
        
        try:
            logger.info(f"Initializing OpenAI embedding client with model: {self.model_name}")
            self._client = openai.OpenAI(
                api_key=self.api_key,
                timeout=self.timeout,
                max_retries=self.max_retries
            )
            self._initialized = True
            logger.info("OpenAI embedding client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI embedding client: {e}")
            raise RuntimeError(f"OpenAI embedding client initialization failed: {e}")

    def _get_client(self) -> openai.OpenAI:
        """获取OpenAI客户端实例，如果未初始化则自动初始化"""
        if not self._initialized or self._client is None:
            self.initialize()
        return self._client

    def generate_embedding(self, text: str) -> List[float]:
        """
        为单个文本生成嵌入向量
        
        Args:
            text: 输入文本
            
        Returns:
            嵌入向量（浮点数列表）
            
        Raises:
            ValueError: 如果输入文本为空
            RuntimeError: 如果嵌入生成失败
        """
        if not text or not text.strip():
            raise ValueError("Input text cannot be empty")
        
        try:
            client = self._get_client()

            # 构建请求参数
            request_params = {
                'input': text,
                'model': self.model_name
            }

            # 如果指定了维度且模型支持，添加维度参数
            if self.dimensions is not None and self.model_name.startswith('text-embedding-3-'):
                request_params['dimensions'] = self.dimensions

            response = client.embeddings.create(**request_params)
            
            embedding = response.data[0].embedding
            logger.debug(f"Generated OpenAI embedding for text (length: {len(text)}) -> vector dim: {len(embedding)}")
            return embedding
            
        except Exception as e:
            logger.error(f"Failed to generate OpenAI embedding for text: {e}")
            raise RuntimeError(f"OpenAI embedding generation failed: {e}")

    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        为多个文本批量生成嵌入向量
        
        Args:
            texts: 输入文本列表
            
        Returns:
            嵌入向量列表，与输入文本一一对应
            
        Raises:
            ValueError: 如果输入文本列表为空或包含空文本
            RuntimeError: 如果嵌入生成失败
        """
        if not texts:
            raise ValueError("Input texts list cannot be empty")
        
        # 验证所有文本都不为空
        for i, text in enumerate(texts):
            if not text or not text.strip():
                raise ValueError(f"Text at index {i} cannot be empty")
        
        try:
            client = self._get_client()

            # 构建请求参数
            request_params = {
                'input': texts,
                'model': self.model_name
            }

            # 如果指定了维度且模型支持，添加维度参数
            if self.dimensions is not None and self.model_name.startswith('text-embedding-3-'):
                request_params['dimensions'] = self.dimensions

            response = client.embeddings.create(**request_params)
            
            embeddings = [data.embedding for data in response.data]
            logger.debug(f"Generated OpenAI embeddings for {len(texts)} texts -> vector dim: {len(embeddings[0]) if embeddings else 0}")
            return embeddings
            
        except Exception as e:
            logger.error(f"Failed to generate OpenAI embeddings for {len(texts)} texts: {e}")
            raise RuntimeError(f"OpenAI batch embedding generation failed: {e}")

    def cleanup(self) -> None:
        """清理客户端资源"""
        if self._client is not None:
            # OpenAI客户端通常不需要显式清理
            # 但我们可以清除引用以帮助垃圾回收
            self._client = None
            self._initialized = False
            logger.info("OpenAI embedding client cleaned up")

    def get_model_info(self) -> dict:
        """获取模型信息"""
        base_info = super().get_model_info()
        base_info.update({
            "model_name": self.model_name,
            "provider_type": "openai",
            "library": "openai",
            "api_based": True,
            "timeout": self.timeout,
            "max_retries": self.max_retries
        })

        if self._initialized and self._client is not None:
            # 根据模型和配置确定嵌入维度
            if self.dimensions is not None:
                base_info["embedding_dimension"] = self.dimensions
            elif "ada-002" in self.model_name:
                base_info["embedding_dimension"] = 1536
            elif "text-embedding-3-small" in self.model_name:
                base_info["embedding_dimension"] = 1536  # 默认维度
            elif "text-embedding-3-large" in self.model_name:
                base_info["embedding_dimension"] = 3072  # 默认维度
            else:
                base_info["embedding_dimension"] = "unknown"
        
        return base_info
