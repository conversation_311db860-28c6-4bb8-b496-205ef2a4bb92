# xi_system/service/embedding/__init__.py

"""
嵌入服务统一导出

提供嵌入向量生成服务的统一访问接口。
支持多种嵌入提供商（本地模型、云端API等）。

使用示例:
    from xi_system.service.embedding import EmbeddingService
    
    # 通过服务容器获取
    container = get_service_container()
    embedding_service = container.get_service('embedding')
    
    # 生成单个文本嵌入
    embedding = embedding_service.generate_embedding("Hello world")
    
    # 批量生成嵌入
    embeddings = embedding_service.generate_embeddings(["Text 1", "Text 2"])
"""

from .service import EmbeddingService

# 导出所有公共接口
__all__ = [
    'EmbeddingService'
]
