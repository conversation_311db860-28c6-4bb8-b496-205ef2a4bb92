# xi_system/service/config.py

"""
配置管理服务

统一管理系统配置，支持结构化YAML配置和环境变量覆盖。
提供类型安全的配置访问接口和点分路径访问。

设计原则：
- 结构化配置：使用YAML文件组织分层配置
- 安全分离：敏感信息通过环境变量注入
- 点分路径：支持 config.get('llm.provider') 格式访问
- 环境切换：支持多环境配置文件
- 配置验证：提供完整的配置验证和类型转换
"""

import os
import logging
import yaml
import re
from typing import Any, Optional, Dict
from pathlib import Path
from dotenv import load_dotenv

from .container import ServiceInterface

logger = logging.getLogger(__name__)


class ConfigService(ServiceInterface):
    """
    配置管理服务

    负责加载、验证和提供系统配置。
    支持结构化YAML配置文件和环境变量覆盖。
    """

    def __init__(self, config_file: str = 'config.default.yml', env_file: Optional[str] = None):
        """
        初始化配置服务

        Args:
            config_file: YAML配置文件路径，默认为config.default.yml
            env_file: .env文件路径，默认为项目根目录的.env
        """
        self.config_file = config_file
        self.env_file = env_file or self._find_env_file()
        self._config: Dict[str, Any] = {}
        self._loaded = False
    
    def _find_env_file(self) -> Optional[str]:
        """自动查找.env文件"""
        # 从当前文件向上查找.env文件
        current_dir = Path(__file__).parent
        for _ in range(5):  # 最多向上查找5级目录
            env_path = current_dir / ".env"
            if env_path.exists():
                return str(env_path)
            current_dir = current_dir.parent
        return None
    
    def initialize(self) -> None:
        """初始化配置服务"""
        if self._loaded:
            return

        logger.info("Initializing ConfigService with structured YAML...")

        # 1. 加载.env文件，使环境变量可用
        if self.env_file and Path(self.env_file).exists():
            load_dotenv(self.env_file)
            logger.info(f"Loaded environment variables from: {self.env_file}")
        else:
            logger.warning("No .env file found, using system environment variables only")

        # 2. 加载基础YAML配置
        config_path = Path(self.config_file)
        if not config_path.exists():
            raise FileNotFoundError(f"Base config file not found: {self.config_file}")

        with open(config_path, 'r', encoding='utf-8') as f:
            base_config = yaml.safe_load(f)

        # 3. 加载环境特定配置并合并（未来扩展）
        env = os.getenv('XI_ENV', 'development')
        env_config_path = Path(f'config.{env}.yml')
        if env_config_path.exists():
            with open(env_config_path, 'r', encoding='utf-8') as f:
                env_config = yaml.safe_load(f)
            base_config = self._deep_merge(base_config, env_config)
            logger.info(f"Loaded and merged environment config: {env_config_path}")

        # 4. 替换环境变量
        self._config = self._substitute_env_vars(base_config)

        # 5. 标记为已加载，以便验证方法可以调用get()
        self._loaded = True

        # 6. 验证配置
        self._validate_required_configs()
        logger.info("ConfigService initialized successfully.")
    
    def cleanup(self) -> None:
        """清理配置服务"""
        self._config.clear()
        self._loaded = False
        logger.debug("ConfigService cleaned up")

    def _substitute_env_vars(self, config_value: Any) -> Any:
        """
        递归替换配置中的环境变量

        支持 ${VAR_NAME} 和 ${VAR_NAME:-default_value} 格式
        """
        if isinstance(config_value, dict):
            return {k: self._substitute_env_vars(v) for k, v in config_value.items()}
        elif isinstance(config_value, list):
            return [self._substitute_env_vars(i) for i in config_value]
        elif isinstance(config_value, str):
            # 正则表达式匹配 ${VAR_NAME} 或 ${VAR_NAME:-default}
            pattern = re.compile(r'\$\{(\w+)(?::-([^}]+))?\}')
            return pattern.sub(lambda m: os.getenv(m.group(1), m.group(2) or ''), config_value)
        return config_value

    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """
        深度合并两个配置字典

        Args:
            base: 基础配置字典
            override: 覆盖配置字典

        Returns:
            合并后的配置字典
        """
        for key, value in override.items():
            if isinstance(value, dict) and key in base and isinstance(base[key], dict):
                base[key] = self._deep_merge(base[key], value)
            else:
                base[key] = value
        return base
    


    def _validate_required_configs(self) -> None:
        """验证必需的配置项"""
        required_paths = [
            'llm.providers.google.api_key',
            'database.mongo_uri'
        ]

        missing = [path for path in required_paths if self.get(path) is None]
        if missing:
            raise ValueError(f"Missing required configuration values: {', '.join(missing)}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持点分路径访问

        Args:
            key: 配置键名，支持点分路径如 'llm.provider' 或 'llm.providers.google.api_key'
            default: 默认值

        Returns:
            配置值
        """
        if not self._loaded:
            raise RuntimeError("ConfigService not initialized")

        # 支持点分路径访问
        keys = key.split('.')
        value = self._config
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_str(self, key: str, default: str = "") -> str:
        """获取字符串配置"""
        value = self.get(key, default)
        return str(value) if value is not None else default
    
    def get_int(self, key: str, default: int = 0) -> int:
        """获取整数配置"""
        value = self.get(key, default)
        try:
            return int(value) if value is not None else default
        except (ValueError, TypeError):
            logger.warning(f"Invalid integer config for '{key}': {value}, using default: {default}")
            return default
    
    def get_bool(self, key: str, default: bool = False) -> bool:
        """获取布尔配置"""
        value = self.get(key, default)
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        return default
    
    def get_list(self, key: str, default: list = None, separator: str = ',') -> list:
        """获取列表配置"""
        if default is None:
            default = []
        
        value = self.get(key)
        if value is None:
            return default
        
        if isinstance(value, list):
            return value
        
        if isinstance(value, str):
            return [item.strip() for item in value.split(separator) if item.strip()]
        
        return default
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值（运行时修改）
        
        Args:
            key: 配置键名
            value: 配置值
        """
        self._config[key] = value
        logger.debug(f"Config updated: {key} = {value}")
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置（用于调试）"""
        # 返回配置的深拷贝，隐藏敏感信息
        import copy
        safe_config = copy.deepcopy(self._config)

        # 隐藏敏感信息
        def hide_sensitive_values(config_dict, path=""):
            for key, value in config_dict.items():
                current_path = f"{path}.{key}" if path else key
                if isinstance(value, dict):
                    hide_sensitive_values(value, current_path)
                elif 'api_key' in key.lower() or 'uri' in key.lower():
                    config_dict[key] = "***HIDDEN***" if value else None

        hide_sensitive_values(safe_config)
        return safe_config
    
    def is_debug_mode(self) -> bool:
        """是否为调试模式"""
        return self.get_bool('system.debug_mode', False)

    def is_test_mode(self) -> bool:
        """是否为测试模式"""
        return self.get_bool('system.test_mode', False)

    # 便捷的配置获取方法
    def get_reflection_message_threshold(self) -> int:
        """获取反思消息阈值"""
        return self.get_int('task.reflection.message_threshold', 20)

    def get_reflection_min_interval_hours(self) -> float:
        """获取反思最小间隔（小时）"""
        return float(self.get('task.reflection.min_interval_hours', 1.0))

    def get_reflection_conversation_limit(self) -> int:
        """获取反思对话限制"""
        return self.get_int('task.reflection.conversation_limit', 50)

    def get_task_max_concurrent(self) -> int:
        """获取最大并发任务数"""
        return self.get_int('task.max_concurrent', 3)

    def get_task_timeout_seconds(self) -> int:
        """获取任务超时时间（秒）"""
        return self.get_int('task.timeout_seconds', 300)

    def get_llm_max_tokens(self) -> int:
        """获取LLM最大token数"""
        return self.get_int('llm.max_tokens', 2000)

    def get_llm_temperature(self) -> float:
        """获取LLM温度参数"""
        return float(self.get('llm.temperature', 0.7))

    def get_rag_max_results(self) -> int:
        """获取RAG最大结果数"""
        return self.get_int('rag.max_results', 10)

    def get_rag_similarity_threshold(self) -> float:
        """获取RAG相似度阈值"""
        return float(self.get('rag.similarity_threshold', 0.7))
