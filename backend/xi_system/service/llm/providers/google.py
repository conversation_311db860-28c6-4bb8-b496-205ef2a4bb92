# xi_system/service/llm/providers/google.py

"""
Google LLM提供商实现

使用Google Gemini API（通过OpenAI兼容接口）的LLM提供商。
支持同步和异步对话，提供流式响应功能。

技术实现：
- 使用OpenAI Python库访问Google的OpenAI兼容API
- 支持流式和非流式响应
- 包含连接测试和错误处理
- 支持模型特定参数传递

配置要求：
- api_key: Google API密钥
- base_url: Google OpenAI兼容API端点
- model: 模型名称（如gemini-2.5-flash）
- timeout: 请求超时时间
"""

import logging
from typing import List, Dict, Any, AsyncGenerator, Optional
from openai import OpenAI, AsyncOpenAI
from .base import LLMProvider

logger = logging.getLogger(__name__)


class GoogleLLMProvider(LLMProvider):
    """使用Google Gemini API（通过OpenAI兼容接口）的LLM提供商。"""

    def __init__(self, api_key: str, base_url: str, model: str, timeout: int):
        """
        初始化Google LLM提供商
        
        Args:
            api_key: Google API密钥
            base_url: Google OpenAI兼容API端点
            model: 模型名称
            timeout: 请求超时时间（秒）
        """
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.timeout = timeout
        self.client: Optional[OpenAI] = None
        self.async_client: Optional[AsyncOpenAI] = None

    def initialize(self) -> None:
        """初始化Google LLM提供商"""
        if not self.api_key:
            raise ValueError("Google LLM API key not configured.")
        
        try:
            # 创建同步和异步客户端
            self.client = OpenAI(
                api_key=self.api_key, 
                base_url=self.base_url, 
                timeout=self.timeout
            )
            self.async_client = AsyncOpenAI(
                api_key=self.api_key, 
                base_url=self.base_url, 
                timeout=self.timeout
            )
            
            # 简单的连接测试
            self._test_connection()
            
            logger.info(f"GoogleLLMProvider initialized successfully for model: {self.model}")
            
        except Exception as e:
            logger.error(f"Failed to initialize GoogleLLMProvider: {e}")
            raise RuntimeError(f"GoogleLLMProvider initialization failed: {e}")

    def _test_connection(self) -> None:
        """测试与Google API的连接"""
        try:
            # 发送一个简单的测试请求
            test_messages = [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Hello"}
            ]
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=test_messages,
                max_tokens=10,
                timeout=10
            )
            
            if not response.choices:
                raise RuntimeError("Google LLM test request returned no choices")
            
            logger.debug("Google LLM connection test successful")
            
        except Exception as e:
            logger.error(f"Google LLM connection test failed: {e}")
            raise RuntimeError(f"Google LLM connection test failed: {e}")

    def sync_chat(self, messages: List[Dict[str, Any]], **kwargs) -> str:
        """
        同步对话补全
        
        Args:
            messages: 对话消息列表
            **kwargs: 额外的模型参数
            
        Returns:
            完整的响应内容
        """
        if not self.client:
            raise RuntimeError("GoogleLLMProvider not initialized")
        
        # 准备请求参数
        request_params = {
            "model": self.model, 
            "messages": messages, 
            "stream": False, 
            **kwargs
        }
        
        logger.debug(f"Starting Google sync chat with {len(messages)} messages")
        
        try:
            response = self.client.chat.completions.create(**request_params)
            
            if not response.choices:
                raise RuntimeError("LLM response contains no choices.")
            
            content = response.choices[0].message.content or ""
            logger.debug(f"Google sync chat completed, response length: {len(content)}")
            
            return content
            
        except Exception as e:
            logger.error(f"Google sync chat error: {e}")
            raise RuntimeError(f"Google sync chat failed: {e}")

    async def stream_chat(self, messages: List[Dict[str, Any]], **kwargs) -> AsyncGenerator[str, None]:
        """
        异步流式对话补全
        
        Args:
            messages: 对话消息列表
            **kwargs: 额外的模型参数
            
        Yields:
            响应文本块
        """
        if not self.async_client:
            raise RuntimeError("GoogleLLMProvider not initialized")
        
        # 准备请求参数
        request_params = {
            "model": self.model, 
            "messages": messages, 
            "stream": True, 
            **kwargs
        }
        
        logger.debug(f"Starting Google stream chat with {len(messages)} messages")
        
        try:
            stream = await self.async_client.chat.completions.create(**request_params)
            
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    yield content
                    
        except Exception as e:
            logger.error(f"Google stream chat error: {e}")
            raise RuntimeError(f"Google stream chat failed: {e}")

    def get_client(self) -> Any:
        """获取Google OpenAI客户端实例"""
        if not self.client:
            raise RuntimeError("GoogleLLMProvider not initialized")
        return self.client
