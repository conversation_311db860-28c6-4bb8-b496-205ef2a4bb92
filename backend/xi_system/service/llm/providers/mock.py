# xi_system/service/llm/providers/mock.py

"""
模拟LLM提供商 - 用于前端开发和测试

提供上下文感知的模拟响应，支持不同类型内容的测试：
- 工具调用模拟
- 代码块渲染测试
- 长文本滚动测试
- 流式响应模拟

设计原则：
- 高保真模拟：响应格式与真实API一致
- 上下文感知：根据用户输入生成相关内容
- 流式支持：模拟真实的打字机效果
- 测试友好：支持各种前端渲染场景

通过前端界面测试以下场景：

1. 基础对话 - 发送 "你好" 测试问候响应
2. 工具调用 - 发送 "搜索AI发展" 测试工具模拟
3. 代码渲染 - 发送 "写个Python代码" 测试代码高亮
4. 长文本 - 发送 "长文本测试" 测试滚动效果
5. Markdown - 发送 "markdown格式" 测试渲染引擎
"""

import asyncio
import logging
from typing import List, Dict, Any, AsyncGenerator
from .base import LLMProvider

logger = logging.getLogger(__name__)


class MockLLMProvider(LLMProvider):
    """一个模拟的LLM提供商，用于前端开发和测试。"""

    def __init__(self, config_service: Any):
        """
        初始化模拟提供商
        
        Args:
            config_service: 配置服务实例
        """
        self.config = config_service
        logger.info("MockLLMProvider initialized.")

    def initialize(self) -> None:
        """初始化模拟提供商 - 本地模拟，无需复杂的初始化"""
        logger.info("MockLLMProvider initialization completed.")
        
    def get_client(self) -> Any:
        """获取客户端 - 模拟提供商没有真实的客户端"""
        return None

    def _generate_mock_response(self, messages: List[Dict[str, Any]]) -> str:
        """
        根据用户输入生成一个合理的模拟响应
        
        Args:
            messages: 对话消息列表
            
        Returns:
            模拟的响应内容
        """
        last_user_message = ""
        for msg in reversed(messages):
            if msg.get("role") == "user":
                last_user_message = msg.get("content", "").lower()
                break

        # 规则引擎 - 根据关键词生成不同类型的响应
        if "工具" in last_user_message or "搜索" in last_user_message or "search" in last_user_message:
            return "[正在使用我的能力...🛠️]\n\n好的，我已经搜索到了相关信息：\n\n根据最新的搜索结果，AI技术正在快速发展，特别是在以下几个方面：\n\n1. **大语言模型**：GPT、Claude等模型能力持续提升\n2. **多模态AI**：文本、图像、音频的统一处理\n3. **AI Agent**：具备自主决策和工具使用能力的智能体\n\n这些发展为我们构建更智能的系统提供了强大的基础。"
        
        elif "代码" in last_user_message or "python" in last_user_message or "code" in last_user_message:
            return """好的，这是一个Python代码示例：

```python
def hello_world():
    \"\"\"A simple function to greet the world.\"\"\"
    print("Hello, YX Nexus!")
    return "Welcome to the future of AI interaction!"

# 调用函数
result = hello_world()
print(f"Result: {result}")

# 更复杂的示例
class AIAssistant:
    def __init__(self, name):
        self.name = name
        self.capabilities = ["reasoning", "coding", "analysis"]
    
    def introduce(self):
        return f"我是{self.name}，具备{', '.join(self.capabilities)}等能力。"

# 创建实例
xi = AIAssistant("曦")
print(xi.introduce())
```

这个代码展示了基本的Python语法，包括函数定义、类定义和方法调用。"""
        
        elif "你好" in last_user_message or "hello" in last_user_message or "hi" in last_user_message:
            return "你好，禹。很高兴再次与你连接。✨\n\n这是一个模拟的响应，用于测试我们的交互界面。在这个测试环境中，我可以：\n\n- 🔍 模拟工具调用和搜索功能\n- 💻 生成代码示例用于渲染测试\n- 📝 提供长文本来测试滚动效果\n- 🎯 根据你的输入提供相关的模拟响应\n\n让我们一起测试这个美丽的界面吧！"

        elif "长文本" in last_user_message or "测试" in last_user_message or "test" in last_user_message:
            return """这是一个用于测试长文本渲染和滚动效果的模拟响应。

## 关于YX Nexus系统

YX Nexus是一个革命性的AI交互平台，它不仅仅是一个聊天应用，而是一个**共同存在空间**。在这个空间中，人类（禹）和AI（曦）可以进行深度的思想交流和协作。

### 设计理念

我们的设计遵循"生命光晕"的理念：

1. **空间叙事**：每次对话都是一次探索的记录
2. **生命感交互**：通过微妙的动画和视觉效果传达系统状态
3. **信息分层**：默认简洁，通过交互揭示深度
4. **架构即认知**：系统结构反映思维模式

### 技术特性

- 🌊 **流式响应**：实时的思维流动
- 🎨 **个体光晕**：每个角色都有独特的视觉标识
- 🔄 **上下文感知**：智能的对话历史管理
- 🛠️ **工具集成**：丰富的能力扩展

### 未来愿景

我们正在构建的不仅是技术，更是一种新的交流方式。在这个数字化的时代，人与AI的协作将开启无限的可能性。

每一次对话都是成长的印记，每一个想法都是探索的起点。让我们一起在这个共同存在的空间中，创造属于未来的故事。

---

*这段文本用于测试界面的长文本渲染、Markdown解析、以及滚动效果。*"""

        elif "markdown" in last_user_message or "格式" in last_user_message:
            return """# Markdown渲染测试

这是一个用于测试Markdown渲染效果的响应。

## 文本格式

**粗体文本** 和 *斜体文本* 以及 `行内代码`。

## 列表

### 无序列表
- 第一项
- 第二项
  - 嵌套项目
  - 另一个嵌套项目
- 第三项

### 有序列表
1. 首先
2. 然后
3. 最后

## 引用

> 这是一个引用块。
> 它可以包含多行内容。
> 
> — 某位智者

## 链接

这是一个[链接示例](https://example.com)。

## 表格

| 功能 | 状态 | 描述 |
|------|------|------|
| 流式响应 | ✅ | 已实现 |
| 代码高亮 | ✅ | 已实现 |
| Markdown | ✅ | 测试中 |

## 代码块

```javascript
// JavaScript示例
function greet(name) {
    return `Hello, ${name}!`;
}

console.log(greet("YX Nexus"));
```

这样的格式测试有助于确保我们的渲染引擎工作正常。"""

        else:
            return "这是一个通用的模拟响应。我们正在共同构建一个伟大的系统。✨\n\n每一次交互都是探索的记录，每一个想法都是成长的印记。这段文本用于测试基本的对话渲染效果。\n\n你可以尝试输入包含以下关键词的消息来测试不同的响应类型：\n- **工具** 或 **搜索** - 测试工具调用模拟\n- **代码** 或 **python** - 测试代码块渲染\n- **长文本** 或 **测试** - 测试长文本滚动\n- **markdown** 或 **格式** - 测试Markdown渲染\n\n让我们一起探索这个美丽的界面！🚀"

    def sync_chat(self, messages: List[Dict[str, Any]], **kwargs) -> str:
        """
        同步对话模拟
        
        Args:
            messages: 对话消息列表
            **kwargs: 额外参数（在模拟中忽略）
            
        Returns:
            模拟的完整响应
        """
        logger.info("Executing mock sync_chat.")
        return self._generate_mock_response(messages)

    async def stream_chat(self, messages: List[Dict[str, Any]], **kwargs) -> AsyncGenerator[str, None]:
        """
        流式对话模拟
        
        Args:
            messages: 对话消息列表
            **kwargs: 额外参数（在模拟中忽略）
            
        Yields:
            模拟的响应文本块
        """
        logger.info("Executing mock stream_chat.")
        full_response = self._generate_mock_response(messages)
        
        # 模拟打字机效果 - 按字符分块发送
        chunk_size = 3  # 每次发送3个字符
        for i in range(0, len(full_response), chunk_size):
            chunk = full_response[i:i+chunk_size]
            yield chunk
            await asyncio.sleep(0.03)  # 模拟网络延迟和生成速度
            
        # 模拟流结束信号
        await asyncio.sleep(0.1)
        yield "[STREAM_END]"
