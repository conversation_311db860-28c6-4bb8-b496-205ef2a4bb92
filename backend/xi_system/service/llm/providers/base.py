# xi_system/service/llm/providers/base.py

"""
LLM提供商抽象基类

定义所有大语言模型提供商必须实现的接口契约。
支持同步和异步对话，提供统一的API抽象。

设计原则：
- 统一接口：所有提供商实现相同的方法签名
- 灵活参数：支持特定于模型的额外参数
- 错误处理：明确的异常处理和错误信息
- 可扩展性：易于添加新的提供商实现
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, AsyncGenerator


class LLMProvider(ABC):
    """所有大语言模型提供商的抽象基类。"""

    @abstractmethod
    def initialize(self) -> None:
        """
        初始化提供商，如API客户端。
        
        Raises:
            ValueError: 配置参数无效时抛出
            RuntimeError: 初始化失败时抛出
        """
        pass

    @abstractmethod
    def sync_chat(self, messages: List[Dict[str, Any]], **kwargs) -> str:
        """
        以同步方式执行对话补全。

        Args:
            messages: 对话消息列表，格式为[{"role": "user", "content": "..."}]
            **kwargs: 特定于模型的额外参数，如temperature、max_tokens等

        Returns:
            模型的完整文本响应

        Raises:
            RuntimeError: 对话请求失败时抛出
        """
        pass

    @abstractmethod
    async def stream_chat(self, messages: List[Dict[str, Any]], **kwargs) -> AsyncGenerator[str, None]:
        """
        以流式方式执行对话补全。

        Args:
            messages: 对话消息列表，格式为[{"role": "user", "content": "..."}]
            **kwargs: 特定于模型的额外参数，如temperature、max_tokens等

        Yields:
            模型的响应文本块

        Raises:
            RuntimeError: 流式对话请求失败时抛出
        """
        pass

    @abstractmethod
    def get_client(self) -> Any:
        """
        获取底层的API客户端实例。
        
        Returns:
            底层API客户端对象
            
        Raises:
            RuntimeError: 提供商未初始化时抛出
        """
        pass
