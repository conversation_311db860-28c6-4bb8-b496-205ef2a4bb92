# Xi 基础设施服务层 (Xi Infrastructure Service Layer)

## 概述 (Overview)

Xi 基础设施服务层是整个曦智能体系统的"基石"，负责管理所有核心基础设施服务的生命周期、配置和依赖关系。该层通过依赖注入容器模式，为上层业务逻辑提供统一、可靠的服务访问接口，确保系统的高内聚、低耦合架构原则。

### 核心理念

- **依赖注入**: 通过ServiceContainer统一管理服务依赖，避免循环导入和紧耦合
- **生命周期管理**: 自动化的服务初始化、健康监控和资源清理
- **配置统一**: 集中式配置管理，支持环境变量和.env文件
- **服务抽象**: 为数据库、LLM、任务管理等提供统一的服务接口

### 在整体架构中的位置

```
Xi 系统架构层次:
├── core/           # 轻量级业务流程编排器
├── service/        # 基础设施服务层 ← 本模块
│   ├── container   # 依赖注入容器
│   ├── config      # 配置管理服务
│   ├── database    # 数据库服务
│   ├── llm         # LLM服务
│   └── task        # 任务管理服务
├── agents/         # AI交互处理器
├── memory/         # 记忆管理系统
├── tools/          # 工具系统
├── prompts/        # 提示词系统
└── tasks/          # 后台认知任务系统
```

## 系统架构 (Architecture)

### 核心组件

#### 1. ServiceContainer (服务容器)
```python
class ServiceContainer:
    """中央依赖注入容器"""
    
    # 核心属性
    _instance: Optional['ServiceContainer']     # 单例实例
    _services: Dict[str, Any]                   # 服务实例存储
    _service_types: Dict[str, Type]             # 服务类型注册
    _initialization_order: list                 # 初始化顺序
    
    # 生命周期方法
    @classmethod
    def get_instance() -> 'ServiceContainer'    # 获取单例实例
    def register_service(name, service_type, *args, **kwargs)  # 注册服务
    def initialize() -> None                    # 初始化所有服务
    def get_service(name: str) -> Any          # 获取服务实例
    def cleanup_all() -> None                  # 清理所有服务
```

#### 2. ConfigService (配置服务)
```python
class ConfigService(ServiceInterface):
    """统一配置管理服务"""
    
    # 核心功能
    def initialize() -> None                    # 加载配置
    def get(key: str, default: Any) -> Any     # 获取配置值
    def get_str(key: str, default: str) -> str # 获取字符串配置
    def get_int(key: str, default: int) -> int # 获取整数配置
    def get_bool(key: str, default: bool) -> bool  # 获取布尔配置
```

#### 3. DatabaseService (数据库服务)
```python
class DatabaseService(ServiceInterface):
    """数据库连接和提供者管理"""
    
    # 核心功能
    def get_provider(name: str) -> DatabaseProvider     # 获取数据库提供者
    def get_primary_provider() -> DatabaseProvider      # 获取主数据库提供者
    def get_memory_provider()                           # 获取内存提供者
    def get_retriever()                                 # 获取检索器
    def health_check() -> Dict[str, Any]               # 健康检查
```

#### 4. LLMService (LLM服务)
```python
class LLMService(ServiceInterface):
    """LLM客户端管理和交互"""
    
    # 核心功能
    async def stream_chat(messages, **kwargs) -> AsyncGenerator[str, None]  # 流式对话
    def sync_chat(messages, **kwargs) -> str           # 同步对话
    def apply_role_mapping(messages) -> List[Dict]     # 角色映射
    def health_check() -> Dict[str, Any]               # 健康检查
```

#### 5. TaskService (任务服务)
```python
class TaskService(ServiceInterface):
    """后台任务管理服务"""
    
    # 核心功能
    def get_task_manager() -> TaskManager              # 获取任务管理器
    def submit_task(task, context) -> str              # 提交任务
    def get_task_status(task_id: str) -> Dict          # 获取任务状态
    async def start_async() -> None                    # 异步启动
    async def stop_async() -> None                     # 异步停止
```

### 类层次结构

```
ServiceInterface (抽象基类)
├── ConfigService           # 配置管理服务
├── DatabaseService         # 数据库服务
├── LLMService             # LLM服务
└── TaskService            # 任务管理服务

ServiceContainer (单例)
├── 服务注册管理
├── 依赖注入解析
├── 生命周期控制
└── 健康状态监控

DatabaseProvider (抽象基类)
└── MongoProvider          # MongoDB提供者实现
```

### 设计模式

1. **单例模式**: ServiceContainer确保全局唯一的服务容器实例
2. **依赖注入**: 通过构造函数注入解决服务间依赖关系
3. **工厂模式**: 服务容器作为服务实例的工厂
4. **策略模式**: 不同的数据库提供者实现统一接口
5. **观察者模式**: 服务健康状态监控和通知机制

## 使用示例 (Usage Examples)

### 基本使用流程

```python
from xi_system.service import initialize_services, get_container
from xi_system.service import get_config_service, get_database_service, get_llm_service

# 1. 初始化所有服务
container = initialize_services(env_file=".env")

# 2. 获取服务实例
config_service = get_config_service()
database_service = get_database_service()
llm_service = get_llm_service()

# 3. 使用配置服务
api_key = config_service.get_str('llm_api_key')
debug_mode = config_service.get_bool('debug_mode', False)

# 4. 使用数据库服务
memory_provider = database_service.get_memory_provider()
retriever = database_service.get_retriever()

# 5. 使用LLM服务
messages = [
    {"role": "xi_system", "content": "You are Xi, a helpful AI assistant."},
    {"role": "yu", "content": "Hello, how are you?"}
]

# 流式对话
async for chunk in llm_service.stream_chat(messages):
    print(chunk, end='', flush=True)

# 同步对话
response = llm_service.sync_chat(messages)
print(response)
```

### 服务容器直接使用

```python
from xi_system.service import ServiceContainer, ConfigService, DatabaseService

# 1. 获取容器实例
container = ServiceContainer.get_instance()

# 2. 手动注册服务
config_service = ConfigService(env_file=".env")
config_service.initialize()
container.register_service('config', config_service)

# 3. 注册依赖服务
container.register_service('database', DatabaseService, config_service)

# 4. 初始化所有服务
container.initialize()

# 5. 获取服务
db_service = container.get_service('database')
provider = db_service.get_primary_provider()
```

### 自定义服务扩展

```python
from xi_system.service.container import ServiceInterface

class CustomCacheService(ServiceInterface):
    """自定义缓存服务示例"""
    
    def __init__(self, config_service: ConfigService):
        self.config = config_service
        self.cache = {}
        self._initialized = False
    
    def initialize(self) -> None:
        """初始化缓存服务"""
        if self._initialized:
            return
        
        cache_size = self.config.get_int('cache_max_size', 1000)
        self.cache = {}  # 实际实现中可能使用Redis等
        self._initialized = True
        
    def cleanup(self) -> None:
        """清理缓存资源"""
        self.cache.clear()
        self._initialized = False
    
    def get(self, key: str) -> Any:
        """获取缓存值"""
        return self.cache.get(key)
    
    def set(self, key: str, value: Any) -> None:
        """设置缓存值"""
        self.cache[key] = value

# 注册自定义服务
container = get_container()
config_service = container.get_service('config')
container.register_service('cache', CustomCacheService, config_service)
container.initialize()

# 使用自定义服务
cache_service = container.get_service('cache')
cache_service.set('user_123', {'name': 'Alice', 'role': 'admin'})
user_data = cache_service.get('user_123')
```

### 错误处理最佳实践

```python
from xi_system.service import initialize_services, cleanup_services
import logging

logger = logging.getLogger(__name__)

def safe_service_initialization():
    """安全的服务初始化示例"""
    container = None
    
    try:
        # 初始化服务
        container = initialize_services()
        
        # 验证关键服务
        llm_service = container.get_service('llm')
        health = llm_service.health_check()
        
        if health['status'] != 'healthy':
            raise RuntimeError(f"LLM service unhealthy: {health}")
        
        logger.info("All services initialized successfully")
        return container
        
    except ValueError as e:
        logger.error(f"Configuration error: {e}")
        if container:
            cleanup_services()
        raise
        
    except RuntimeError as e:
        logger.error(f"Service initialization error: {e}")
        if container:
            cleanup_services()
        raise
        
    except Exception as e:
        logger.error(f"Unexpected error during service initialization: {e}")
        if container:
            cleanup_services()
        raise

# 使用示例
try:
    container = safe_service_initialization()
    # 使用服务...
    
finally:
    # 确保清理资源
    cleanup_services()
```

## API 参考 (API Reference)

### ServiceContainer 类

#### 构造函数
```python
def __init__(self)
    """私有构造函数，使用get_instance()获取实例"""
```

#### 核心方法
```python
@classmethod
def get_instance(cls) -> 'ServiceContainer'
    """获取容器单例实例

    Returns:
        ServiceContainer: 容器实例
    """

def register_service(self, name: str, service_type_or_instance, *args, **kwargs) -> None
    """注册服务类型或实例

    Args:
        name: 服务名称
        service_type_or_instance: 服务类型或已初始化的实例
        *args, **kwargs: 服务初始化参数（仅当传入类型时使用）
    """

def initialize(self) -> None
    """初始化所有已注册的服务

    按注册顺序初始化服务，调用ServiceInterface.initialize()方法

    Raises:
        Exception: 服务初始化失败时抛出异常
    """

def get_service(self, name: str) -> Any
    """获取服务实例

    Args:
        name: 服务名称

    Returns:
        Any: 服务实例

    Raises:
        ValueError: 服务不存在时抛出异常
    """
```

#### 管理方法
```python
def cleanup_all(self) -> None
    """清理所有服务资源"""

def replace_service(self, name: str, service_instance: Any) -> None
    """替换服务实例（主要用于测试Mock）

    Args:
        name: 服务名称
        service_instance: 新的服务实例
    """

def get_stats(self) -> dict
    """获取容器统计信息

    Returns:
        dict: 包含初始化状态、服务数量等信息
    """

@classmethod
def reset_instance(cls) -> None
    """重置实例（主要用于测试）"""
```

### ConfigService 类

#### 构造函数
```python
def __init__(self, env_file: Optional[str] = None)
    """初始化配置服务

    Args:
        env_file: .env文件路径，默认自动查找
    """
```

#### 配置获取方法
```python
def get(self, key: str, default: Any = None) -> Any
    """获取配置值

    Args:
        key: 配置键名
        default: 默认值

    Returns:
        Any: 配置值

    Raises:
        RuntimeError: 服务未初始化时抛出异常
    """

def get_str(self, key: str, default: str = "") -> str
    """获取字符串配置

    Args:
        key: 配置键名
        default: 默认值

    Returns:
        str: 字符串配置值
    """

def get_int(self, key: str, default: int = 0) -> int
    """获取整数配置

    Args:
        key: 配置键名
        default: 默认值

    Returns:
        int: 整数配置值
    """

def get_bool(self, key: str, default: bool = False) -> bool
    """获取布尔配置

    Args:
        key: 配置键名
        default: 默认值

    Returns:
        bool: 布尔配置值
    """
```

### DatabaseService 类

#### 构造函数
```python
def __init__(self, config_service: ConfigService)
    """初始化数据库服务

    Args:
        config_service: 配置服务实例
    """
```

#### 提供者管理方法
```python
def get_provider(self, name: str = 'mongo') -> DatabaseProvider
    """获取数据库提供者

    Args:
        name: 提供者名称，默认为'mongo'

    Returns:
        DatabaseProvider: 数据库提供者实例

    Raises:
        ValueError: 提供者不存在时抛出异常
        RuntimeError: 服务未初始化时抛出异常
    """

def get_primary_provider(self) -> DatabaseProvider
    """获取主数据库提供者

    Returns:
        DatabaseProvider: 主数据库提供者实例
    """

def get_memory_provider(self)
    """获取内存提供者（兼容V0.83）

    Returns:
        DatabaseProvider: 内存数据库提供者
    """

def get_retriever(self)
    """获取检索器（兼容V0.83）

    Returns:
        Retriever: 内存检索器实例
    """
```

### LLMService 类

#### 构造函数
```python
def __init__(self, config_service: ConfigService)
    """初始化LLM服务

    Args:
        config_service: 配置服务实例
    """
```

#### 对话方法
```python
async def stream_chat(self, messages: List[Dict[str, Any]], **kwargs) -> AsyncGenerator[str, None]
    """流式对话

    Args:
        messages: 消息列表，支持内部角色名（xi_system, yu, xi）
        **kwargs: 额外的LLM参数

    Yields:
        str: 流式响应内容

    Raises:
        RuntimeError: 服务未初始化时抛出异常
    """

def sync_chat(self, messages: List[Dict[str, Any]], **kwargs) -> str
    """同步对话

    Args:
        messages: 消息列表，支持内部角色名（xi_system, yu, xi）
        **kwargs: 额外的LLM参数

    Returns:
        str: 完整的响应内容

    Raises:
        RuntimeError: 服务未初始化时抛出异常
    """

def apply_role_mapping(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]
    """应用角色映射

    将内部个性化角色名转换为外部API格式

    Args:
        messages: 消息列表

    Returns:
        List[Dict[str, Any]]: 映射后的消息列表
    """
```

#### 配置和状态方法
```python
def get_config(self) -> Dict[str, Any]
    """获取LLM配置

    Returns:
        Dict[str, Any]: LLM配置信息
    """

def get_model_info(self) -> Dict[str, Any]
    """获取当前模型信息

    Returns:
        Dict[str, Any]: 模型信息和配置
    """

def update_role_mapping(self, mapping: Dict[str, str]) -> None
    """更新角色映射

    Args:
        mapping: 新的角色映射字典
    """

def health_check(self) -> Dict[str, Any]
    """LLM服务健康检查

    Returns:
        Dict[str, Any]: 健康检查结果
    """
```

### TaskService 类

#### 构造函数
```python
def __init__(self, config_service: ConfigService)
    """初始化任务服务

    Args:
        config_service: 配置服务实例
    """
```

#### 任务管理方法
```python
def get_task_manager(self) -> Optional[TaskManager]
    """获取任务管理器实例

    Returns:
        Optional[TaskManager]: 任务管理器实例
    """

def submit_task(self, task, context) -> Optional[str]
    """提交任务执行

    Args:
        task: 要执行的任务实例
        context: 任务执行上下文

    Returns:
        Optional[str]: 任务ID，如果提交失败返回None
    """

def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]
    """获取任务状态

    Args:
        task_id: 任务ID

    Returns:
        Optional[Dict[str, Any]]: 任务状态信息
    """

async def start_async(self) -> None
    """异步启动任务管理器"""

async def stop_async(self) -> None
    """异步停止任务管理器"""
```

### 数据类型定义

#### ServiceInterface (服务接口)
```python
class ServiceInterface(ABC):
    """所有服务的抽象基类"""

    @abstractmethod
    def initialize(self) -> None:
        """初始化服务"""
        pass

    @abstractmethod
    def cleanup(self) -> None:
        """清理服务资源"""
        pass
```

#### 健康检查结果格式
```python
# 健康检查返回格式
{
    'status': 'healthy' | 'error',      # 服务状态
    'connected': bool,                   # 连接状态（适用于外部服务）
    'message': str,                      # 状态描述信息
    'error': str,                        # 错误信息（仅在status为error时）
    # 服务特定的额外信息...
}
```

## 系统集成 (Integration Guide)

### 与 XiCore 的集成

服务层作为XiCore的基础依赖，通过依赖注入模式实现松耦合集成：

```python
# XiCore 中的服务集成
from xi_system.service import initialize_services

class XiCore:
    """Xi系统核心编排器"""

    def __init__(self, container: ServiceContainer):
        """
        初始化XiCore

        Args:
            container: 已初始化的服务容器
        """
        self.container = container

        # 获取核心服务
        self.config = container.get_service('config')
        self.database = container.get_service('database')
        self.llm = container.get_service('llm')
        self.task = container.get_service('task')

        # 获取业务组件
        self.memory_provider = self.database.get_memory_provider()
        self.retriever = self.database.get_retriever()
        self.prompt_builder = self.database.get_prompt_builder()

# 应用启动时的集成
def create_xi_system():
    """创建完整的Xi系统"""
    # 1. 初始化服务层
    container = initialize_services()

    # 2. 创建核心编排器
    xi_core = XiCore(container)

    # 3. 启动后台任务系统
    task_service = container.get_service('task')
    asyncio.create_task(task_service.start_async())

    return xi_core, container
```

### 与其他模块的协作机制

#### 1. Memory模块集成
```python
# Memory模块通过DatabaseService获取数据提供者
from xi_system.service import get_database_service

def get_memory_components():
    """获取内存系统组件"""
    db_service = get_database_service()

    # 获取数据提供者
    provider = db_service.get_primary_provider()

    # 获取检索器
    retriever = db_service.get_retriever()

    return provider, retriever
```

#### 2. Agents模块集成
```python
# Agents模块通过服务容器获取LLM和配置服务
from xi_system.service import get_container

class XiOmegaAgent:
    """Xi Omega智能代理"""

    def __init__(self):
        container = get_container()
        self.llm_service = container.get_service('llm')
        self.config_service = container.get_service('config')

    async def process_reflection(self, context):
        """处理反思任务"""
        # 使用LLM服务进行推理
        messages = self._build_reflection_messages(context)
        response = await self.llm_service.stream_chat(messages)

        return response
```

#### 3. Tools模块集成
```python
# Tools模块通过服务容器访问所需服务
from xi_system.service import get_container

class ToolExecutor:
    """工具执行器"""

    def __init__(self):
        self.container = get_container()

    def execute_tool(self, tool_name: str, **kwargs):
        """执行工具"""
        # 根据工具需要获取相应服务
        if tool_name == 'read_note':
            db_service = self.container.get_service('database')
            provider = db_service.get_memory_provider()
            # 执行工具逻辑...
```

### 依赖关系和初始化顺序

服务初始化遵循严格的依赖顺序：

```python
# 服务依赖关系图
"""
ConfigService (无依赖)
    ↓
DatabaseService (依赖 ConfigService)
    ↓
LLMService (依赖 ConfigService)
    ↓
TaskService (依赖 ConfigService)
"""

# 初始化顺序实现
def initialize_services(env_file: Optional[str] = None) -> ServiceContainer:
    """按依赖顺序初始化服务"""
    container = get_container()

    # 1. 配置服务（最先初始化，无依赖）
    config_service = ConfigService(env_file)
    config_service.initialize()
    container.register_service('config', config_service)

    # 2. 数据库服务（依赖配置服务）
    container.register_service('database', DatabaseService, config_service)

    # 3. LLM服务（依赖配置服务）
    container.register_service('llm', LLMService, config_service)

    # 4. 任务服务（依赖配置服务）
    container.register_service('task', TaskService, config_service)

    # 5. 统一初始化所有服务
    container.initialize()

    return container
```

### 事件系统和回调机制

服务层支持健康状态监控和事件通知：

```python
# 健康监控集成
from xi_system.service import health_check

async def monitor_service_health():
    """服务健康监控"""
    while True:
        health_status = health_check()

        if health_status['status'] != 'healthy':
            # 发送告警通知
            await send_health_alert(health_status)

            # 尝试重启不健康的服务
            await restart_unhealthy_services(health_status['services'])

        await asyncio.sleep(30)  # 30秒检查一次

# 服务状态回调
class ServiceHealthCallback:
    """服务健康状态回调"""

    def on_service_healthy(self, service_name: str):
        """服务恢复健康时的回调"""
        logger.info(f"Service {service_name} is now healthy")

    def on_service_unhealthy(self, service_name: str, error: str):
        """服务不健康时的回调"""
        logger.error(f"Service {service_name} is unhealthy: {error}")
        # 可以在这里实现自动重启逻辑
```

## 扩展指南 (Extension Guide)

### 添加新的服务类型

1. **实现ServiceInterface接口**：
```python
from xi_system.service.container import ServiceInterface

class NewCustomService(ServiceInterface):
    """新的自定义服务示例"""

    def __init__(self, config_service: ConfigService, dependency_service: AnotherService):
        """
        初始化自定义服务

        Args:
            config_service: 配置服务依赖
            dependency_service: 其他服务依赖
        """
        self.config = config_service
        self.dependency = dependency_service
        self._initialized = False

    def initialize(self) -> None:
        """初始化服务"""
        if self._initialized:
            return

        # 从配置获取参数
        param1 = self.config.get_str('custom_param1')
        param2 = self.config.get_int('custom_param2', 100)

        # 初始化逻辑
        self._setup_custom_logic(param1, param2)

        self._initialized = True
        logger.info("NewCustomService initialized")

    def cleanup(self) -> None:
        """清理服务资源"""
        # 清理逻辑
        self._cleanup_custom_logic()
        self._initialized = False
        logger.info("NewCustomService cleaned up")

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        if not self._initialized:
            return {'status': 'error', 'message': 'Service not initialized'}

        # 实现健康检查逻辑
        return {'status': 'healthy', 'custom_metric': self._get_custom_metric()}
```

2. **注册新服务**：
```python
# 在service/__init__.py中添加新服务
def initialize_services(env_file: Optional[str] = None) -> ServiceContainer:
    container = get_container()

    # ... 现有服务注册 ...

    # 注册新的自定义服务
    config_service = container.get_service('config')
    dependency_service = container.get_service('database')  # 假设依赖数据库服务
    container.register_service('custom', NewCustomService, config_service, dependency_service)

    container.initialize()
    return container

# 添加便捷访问函数
def get_custom_service() -> NewCustomService:
    """获取自定义服务"""
    return get_container().get_service('custom')
```

### 自定义数据库提供者

```python
from xi_system.memory.providers.base import DatabaseProvider

class CustomDatabaseProvider(DatabaseProvider):
    """自定义数据库提供者"""

    def __init__(self, connection_string: str, **kwargs):
        self.connection_string = connection_string
        self.connection = None

    def connect(self) -> None:
        """建立数据库连接"""
        # 实现连接逻辑
        pass

    def disconnect(self) -> None:
        """断开数据库连接"""
        # 实现断开逻辑
        pass

    def is_connected(self) -> bool:
        """检查连接状态"""
        # 实现连接状态检查
        return self.connection is not None

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        # 实现健康检查逻辑
        pass

# 在DatabaseService中集成自定义提供者
class DatabaseService(ServiceInterface):
    def _initialize_custom_provider(self) -> None:
        """初始化自定义数据库提供者"""
        connection_string = self.config.get_str('custom_db_connection')

        if connection_string:
            custom_provider = CustomDatabaseProvider(connection_string)
            custom_provider.connect()

            self._providers['custom'] = custom_provider
            logger.info("Custom database provider initialized")
```

### 插件机制和扩展点

服务层支持插件式扩展，允许在运行时动态加载服务：

```python
# 插件接口定义
class ServicePlugin(ABC):
    """服务插件接口"""

    @abstractmethod
    def get_service_name(self) -> str:
        """获取服务名称"""
        pass

    @abstractmethod
    def create_service(self, container: ServiceContainer) -> ServiceInterface:
        """创建服务实例"""
        pass

    @abstractmethod
    def get_dependencies(self) -> List[str]:
        """获取服务依赖列表"""
        pass

# 插件管理器
class PluginManager:
    """服务插件管理器"""

    def __init__(self, container: ServiceContainer):
        self.container = container
        self.plugins: Dict[str, ServicePlugin] = {}

    def register_plugin(self, plugin: ServicePlugin):
        """注册插件"""
        service_name = plugin.get_service_name()
        self.plugins[service_name] = plugin

        # 检查依赖并注册服务
        dependencies = plugin.get_dependencies()
        for dep in dependencies:
            if not self.container.has_service(dep):
                raise ValueError(f"Plugin {service_name} requires missing dependency: {dep}")

        # 创建并注册服务
        service_instance = plugin.create_service(self.container)
        self.container.register_service(service_name, service_instance)

        logger.info(f"Plugin {service_name} registered successfully")

# 插件使用示例
class CacheServicePlugin(ServicePlugin):
    """缓存服务插件"""

    def get_service_name(self) -> str:
        return 'cache'

    def create_service(self, container: ServiceContainer) -> ServiceInterface:
        config_service = container.get_service('config')
        return CacheService(config_service)

    def get_dependencies(self) -> List[str]:
        return ['config']

# 使用插件
plugin_manager = PluginManager(container)
cache_plugin = CacheServicePlugin()
plugin_manager.register_plugin(cache_plugin)
```

### 未来功能扩展的技术路径

#### 1. 分布式服务支持
```python
class DistributedServiceContainer(ServiceContainer):
    """分布式服务容器"""

    def __init__(self, cluster_config: Dict[str, Any]):
        super().__init__()
        self.cluster_config = cluster_config
        self.service_registry = {}  # 服务注册中心

    def register_remote_service(self, name: str, node_address: str):
        """注册远程服务"""
        self.service_registry[name] = {
            'type': 'remote',
            'address': node_address,
            'health_endpoint': f"{node_address}/health"
        }

    def get_service(self, name: str) -> Any:
        """获取服务（支持远程服务）"""
        if name in self.service_registry:
            service_info = self.service_registry[name]
            if service_info['type'] == 'remote':
                return self._create_remote_proxy(name, service_info)

        return super().get_service(name)
```

#### 2. 服务网格集成
```python
class ServiceMeshIntegration:
    """服务网格集成"""

    def __init__(self, mesh_config: Dict[str, Any]):
        self.mesh_config = mesh_config
        self.service_discovery = ServiceDiscovery(mesh_config)
        self.load_balancer = LoadBalancer(mesh_config)

    def register_service_with_mesh(self, service_name: str, service_instance):
        """将服务注册到服务网格"""
        # 实现服务网格注册逻辑
        pass

    def discover_services(self) -> List[str]:
        """发现可用服务"""
        return self.service_discovery.list_services()
```

## 配置选项 (Configuration Options)

### 环境变量配置

```bash
# 基础配置
LOG_LEVEL=INFO                          # 日志级别 (DEBUG, INFO, WARN, ERROR)
DEBUG_MODE=false                        # 调试模式开关
MAX_MEMORY_HISTORY=5                    # 最大内存历史记录数
MAX_RAG_RESULTS=5                       # 最大RAG检索结果数

# LLM服务配置
GEMINI_API_KEY=your_api_key_here        # Gemini API密钥 (必需)
LLM_BASE_URL=https://generativelanguage.googleapis.com/v1beta/openai/  # LLM API基础URL
LLM_MODEL=gemini-2.5-flash          # 使用的LLM模型
LLM_TIMEOUT=30                          # LLM请求超时时间(秒)
LLM_REASONING_EFFORT=none               # 推理努力程度 (none, low, medium, high)

# 数据库配置
MONGO_URI=mongodb+srv://username:<EMAIL>/xi_system  # MongoDB Atlas连接URI (必需)
MONGO_DB_NAME=xi_system                 # MongoDB数据库名称
MONGO_TIMEOUT=10                        # MongoDB连接超时时间(秒)

# API服务配置
API_HOST=0.0.0.0                        # API服务监听地址
API_PORT=8000                           # API服务端口
API_CORS_ORIGINS=*                      # CORS允许的源地址(逗号分隔)

# 任务系统配置
TASK_MAX_CONCURRENT=3                   # 最大并发任务数
TASK_DEFAULT_TIMEOUT=300                # 默认任务超时时间(秒)
TASK_QUEUE_SIZE_LIMIT=100              # 任务队列大小限制
REFLECTION_MESSAGE_THRESHOLD=20         # 反思触发的消息数量阈值
```

### 代码配置

```python
# 服务配置字典
service_config = {
    # 容器配置
    'container': {
        'max_services': 50,
        'initialization_timeout': 60,
        'health_check_interval': 30
    },

    # LLM服务配置
    'llm': {
        'model': 'gemini-2.5-flash',
        'timeout': 30,
        'max_retries': 3,
        'retry_delay': 1.0,
        'reasoning_effort': 'none'
    },

    # 数据库服务配置
    'database': {
        'primary_provider': 'mongo',
        'connection_pool_size': 10,
        'connection_timeout': 10,
        'retry_attempts': 3
    },

    # 任务服务配置
    'task': {
        'max_concurrent_tasks': 3,
        'default_timeout': 300,
        'queue_size_limit': 100,
        'enable_persistence': True
    }
}

# 使用代码配置
def create_configured_container(config: Dict[str, Any]) -> ServiceContainer:
    """使用自定义配置创建容器"""
    container = ServiceContainer.get_instance()

    # 应用配置到各个服务
    for service_name, service_config in config.items():
        if service_name in container._services:
            service = container.get_service(service_name)
            if hasattr(service, 'update_config'):
                service.update_config(service_config)

    return container
```

### 推荐配置值

| 配置项 | 开发环境 | 测试环境 | 生产环境 | 说明 |
|--------|----------|----------|----------|------|
| LOG_LEVEL | DEBUG | INFO | WARN | 日志级别 |
| LLM_TIMEOUT | 15 | 30 | 60 | LLM超时时间(秒) |
| MONGO_TIMEOUT | 5 | 10 | 15 | 数据库超时时间(秒) |
| TASK_MAX_CONCURRENT | 2 | 3 | 5 | 最大并发任务数 |
| API_CORS_ORIGINS | * | localhost:3000 | https://yourdomain.com | CORS配置 |

### 配置验证和错误处理

```python
class ConfigValidator:
    """配置验证器"""

    def __init__(self, config_service: ConfigService):
        self.config = config_service

    def validate_all(self) -> List[str]:
        """验证所有配置"""
        errors = []

        # 验证必需配置
        errors.extend(self._validate_required_configs())

        # 验证配置格式
        errors.extend(self._validate_config_formats())

        # 验证配置值范围
        errors.extend(self._validate_config_ranges())

        return errors

    def _validate_required_configs(self) -> List[str]:
        """验证必需配置"""
        required = ['llm_api_key', 'mongo_uri']
        errors = []

        for key in required:
            if not self.config.get(key):
                errors.append(f"Missing required configuration: {key}")

        return errors

    def _validate_config_formats(self) -> List[str]:
        """验证配置格式"""
        errors = []

        # 验证URL格式
        mongo_uri = self.config.get('mongo_uri')
        if mongo_uri and not mongo_uri.startswith('mongodb://'):
            errors.append("Invalid MongoDB URI format")

        # 验证端口范围
        api_port = self.config.get_int('api_port', 8000)
        if not (1 <= api_port <= 65535):
            errors.append("API port must be between 1 and 65535")

        return errors

    def _validate_config_ranges(self) -> List[str]:
        """验证配置值范围"""
        errors = []

        # 验证超时时间
        llm_timeout = self.config.get_int('llm_timeout', 30)
        if llm_timeout <= 0:
            errors.append("LLM timeout must be positive")

        # 验证并发数
        max_concurrent = self.config.get_int('task_max_concurrent', 3)
        if max_concurrent <= 0:
            errors.append("Max concurrent tasks must be positive")

        return errors

# 使用配置验证
def validate_service_config():
    """验证服务配置"""
    config_service = get_config_service()
    validator = ConfigValidator(config_service)

    errors = validator.validate_all()
    if errors:
        error_msg = "Configuration validation failed:\n" + "\n".join(errors)
        raise ValueError(error_msg)

    logger.info("Service configuration validated successfully")
```

## 故障排除 (Troubleshooting)

### 常见问题及解决方案

#### 1. 服务初始化失败
**问题**: 服务容器初始化时出现异常
```
ERROR: Failed to initialize services: Service 'llm' initialization failed
```

**解决方案**:
- 检查环境变量配置是否完整
- 验证API密钥和连接字符串的有效性
- 查看详细错误日志确定具体原因

```python
# 调试服务初始化问题
def debug_service_initialization():
    """调试服务初始化问题"""
    try:
        # 逐个初始化服务以定位问题
        config_service = ConfigService()
        config_service.initialize()
        print("✓ ConfigService initialized")

        database_service = DatabaseService(config_service)
        database_service.initialize()
        print("✓ DatabaseService initialized")

        llm_service = LLMService(config_service)
        llm_service.initialize()
        print("✓ LLMService initialized")

    except Exception as e:
        print(f"✗ Service initialization failed: {e}")
        import traceback
        traceback.print_exc()

# 运行调试
debug_service_initialization()
```

#### 2. 数据库连接问题
**问题**: MongoDB连接失败或超时
```
ERROR: MongoDB connection failed: ServerSelectionTimeoutError
```

**解决方案**:
- 检查MongoDB服务是否运行
- 验证连接URI格式和认证信息
- 调整连接超时时间
- 检查网络连接和防火墙设置

```python
# 数据库连接诊断
def diagnose_database_connection():
    """诊断数据库连接问题"""
    config_service = get_config_service()
    mongo_uri = config_service.get_str('mongo_uri')

    print(f"Testing MongoDB connection: {mongo_uri}")

    try:
        from pymongo import MongoClient
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)

        # 测试连接
        client.admin.command('ping')
        print("✓ MongoDB connection successful")

        # 测试数据库访问
        db_name = config_service.get_str('mongo_db_name')
        db = client[db_name]
        collections = db.list_collection_names()
        print(f"✓ Database '{db_name}' accessible, collections: {collections}")

    except Exception as e:
        print(f"✗ MongoDB connection failed: {e}")
        print("Troubleshooting steps:")
        print("1. Check if MongoDB service is running")
        print("2. Verify connection URI format")
        print("3. Check network connectivity")
        print("4. Verify authentication credentials")
```

#### 3. LLM服务连接问题
**问题**: LLM API调用失败或超时
```
ERROR: LLM connection test failed: Authentication failed
```

**解决方案**:
- 验证API密钥的有效性
- 检查API基础URL是否正确
- 调整请求超时时间
- 检查网络代理设置

```python
# LLM连接诊断
def diagnose_llm_connection():
    """诊断LLM连接问题"""
    config_service = get_config_service()

    api_key = config_service.get_str('llm_api_key')
    base_url = config_service.get_str('llm_base_url')
    model = config_service.get_str('llm_model')

    print(f"Testing LLM connection:")
    print(f"  Base URL: {base_url}")
    print(f"  Model: {model}")
    print(f"  API Key: {'*' * (len(api_key) - 8) + api_key[-8:] if api_key else 'NOT SET'}")

    try:
        from openai import OpenAI
        client = OpenAI(api_key=api_key, base_url=base_url, timeout=10)

        # 发送测试请求
        response = client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": "Hello"}],
            max_tokens=5
        )

        print("✓ LLM connection successful")
        print(f"✓ Test response: {response.choices[0].message.content}")

    except Exception as e:
        print(f"✗ LLM connection failed: {e}")
        print("Troubleshooting steps:")
        print("1. Verify API key is valid and active")
        print("2. Check base URL format")
        print("3. Ensure model name is correct")
        print("4. Check network connectivity and proxy settings")
```

#### 4. 服务依赖循环问题
**问题**: 服务间出现循环依赖导致初始化失败
```
ERROR: Circular dependency detected between services
```

**解决方案**:
- 重新设计服务依赖关系
- 使用延迟初始化避免循环依赖
- 通过事件机制解耦服务间通信

```python
# 检测循环依赖
def detect_circular_dependencies():
    """检测服务间的循环依赖"""
    container = get_container()

    # 构建依赖图
    dependency_graph = {}
    for service_name in container._services.keys():
        service = container.get_service(service_name)
        dependencies = getattr(service, '_dependencies', [])
        dependency_graph[service_name] = dependencies

    # 使用DFS检测循环
    def has_cycle(graph, node, visited, rec_stack):
        visited[node] = True
        rec_stack[node] = True

        for neighbor in graph.get(node, []):
            if not visited.get(neighbor, False):
                if has_cycle(graph, neighbor, visited, rec_stack):
                    return True
            elif rec_stack.get(neighbor, False):
                return True

        rec_stack[node] = False
        return False

    visited = {}
    rec_stack = {}

    for node in dependency_graph:
        if not visited.get(node, False):
            if has_cycle(dependency_graph, node, visited, rec_stack):
                print(f"✗ Circular dependency detected involving: {node}")
                return True

    print("✓ No circular dependencies detected")
    return False
```

#### 5. 内存泄漏问题
**问题**: 长时间运行后内存使用持续增长
```
WARNING: Memory usage increasing continuously
```

**解决方案**:
- 定期清理服务缓存和临时数据
- 检查服务是否正确释放资源
- 监控服务实例的生命周期

```python
# 内存使用监控
import psutil
import gc

def monitor_memory_usage():
    """监控内存使用情况"""
    process = psutil.Process()

    print(f"Memory usage: {process.memory_info().rss / 1024 / 1024:.2f} MB")
    print(f"Memory percent: {process.memory_percent():.2f}%")

    # 检查服务容器状态
    container = get_container()
    stats = container.get_stats()
    print(f"Services registered: {stats['service_count']}")

    # 强制垃圾回收
    collected = gc.collect()
    print(f"Garbage collected: {collected} objects")

def cleanup_service_resources():
    """清理服务资源"""
    container = get_container()

    # 清理各个服务的缓存
    for service_name in container._services.keys():
        service = container.get_service(service_name)

        if hasattr(service, 'clear_cache'):
            service.clear_cache()
            print(f"✓ Cleared cache for {service_name}")

        if hasattr(service, 'cleanup_temp_data'):
            service.cleanup_temp_data()
            print(f"✓ Cleaned temp data for {service_name}")
```

### 调试技巧

#### 1. 启用详细日志
```python
import logging

# 设置详细日志级别
logging.getLogger('xi_system.service').setLevel(logging.DEBUG)
logging.getLogger('xi_system.service.container').setLevel(logging.DEBUG)
logging.getLogger('xi_system.service.llm').setLevel(logging.DEBUG)

# 添加控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)

logger = logging.getLogger('xi_system.service')
logger.addHandler(console_handler)
```

#### 2. 服务状态监控
```python
def monitor_service_status():
    """监控所有服务状态"""
    container = get_container()

    print("=== Service Status Report ===")

    for service_name in container._services.keys():
        try:
            service = container.get_service(service_name)

            if hasattr(service, 'health_check'):
                health = service.health_check()
                status = health.get('status', 'unknown')
                print(f"{service_name}: {status}")

                if status != 'healthy':
                    print(f"  Error: {health.get('error', 'Unknown error')}")
            else:
                print(f"{service_name}: no health check available")

        except Exception as e:
            print(f"{service_name}: ERROR - {e}")

    print("=== End Report ===")

# 定期监控
import asyncio

async def periodic_health_check():
    """定期健康检查"""
    while True:
        monitor_service_status()
        await asyncio.sleep(60)  # 每分钟检查一次
```

#### 3. 性能分析
```python
import time
from functools import wraps

def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()

        execution_time = end_time - start_time
        print(f"Function {func.__name__} executed in {execution_time:.4f} seconds")

        return result
    return wrapper

# 应用到服务方法
@performance_monitor
def timed_service_call():
    """带性能监控的服务调用"""
    llm_service = get_llm_service()
    messages = [{"role": "user", "content": "Hello"}]
    return llm_service.sync_chat(messages)
```

### 性能考虑

#### 1. 服务初始化优化
- 使用延迟初始化减少启动时间
- 并行初始化无依赖关系的服务
- 缓存重复的初始化操作

#### 2. 连接池管理
- 为数据库连接使用连接池
- 复用LLM客户端连接
- 定期清理空闲连接

#### 3. 内存管理
- 定期清理服务缓存
- 避免在服务中保存大量状态
- 使用弱引用避免循环引用

#### 4. 错误恢复
- 实现自动重连机制
- 使用断路器模式防止级联失败
- 记录详细的错误信息用于调试

---

## 总结

Xi 基础设施服务层为整个曦智能体系统提供了坚实的基础架构支撑。通过依赖注入容器模式，实现了服务间的松耦合和统一管理。该层的设计遵循了高内聚、低耦合的架构原则，为上层业务逻辑提供了稳定、可靠的服务访问接口。

通过合理的配置管理、健康监控和错误处理机制，服务层能够在各种环境下稳定运行，为Xi系统的其他模块提供可靠的基础设施支持。
```
```
