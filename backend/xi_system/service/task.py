"""
Task Service - Background Task Management Service

This module provides the task management service that integrates <PERSON>'s background
task system with the service container architecture.
"""

import logging
import asyncio
from typing import Optional, Dict, Any

from .container import ServiceInterface
from ..tasks import TaskManager, ConversationCountTrigger

logger = logging.getLogger(__name__)


class TaskService(ServiceInterface):
    """
    Task management service for Xi's background cognitive tasks.
    
    Integrates the TaskManager with the service container and provides
    lifecycle management for background tasks.
    """
    
    def __init__(self, config_service):
        """
        Initialize task service.
        
        Args:
            config_service: Configuration service instance
        """
        self.config = config_service
        self.task_manager: Optional[TaskManager] = None
        self._initialized = False
        
        logger.info("TaskService created")
    
    def initialize(self) -> None:
        """Initialize the task service"""
        if self._initialized:
            logger.debug("TaskService already initialized")
            return
        
        try:
            # Create task manager with configuration service
            self.task_manager = TaskManager(config_service=self.config)

            # Register default triggers
            self._register_default_triggers()

            # Start task manager (this should be done in a separate async context)
            # For now, we'll mark it as initialized and start it later
            self._initialized = True
            
            logger.info("TaskService initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize TaskService: {e}")
            raise
    
    def cleanup(self) -> None:
        """Cleanup task service resources"""
        if self.task_manager:
            # Note: This should be called from an async context
            # asyncio.create_task(self.task_manager.stop())
            logger.info("TaskService cleanup requested")
        
        self._initialized = False
    
    def _register_default_triggers(self):
        """Register default task triggers"""
        try:
            # Register conversation count trigger for reflection (uses config service)
            conversation_trigger = ConversationCountTrigger(config_service=self.config)
            
            self.task_manager.register_trigger('conversation_stored', conversation_trigger)
            
            logger.info("Default task triggers registered")
            
        except Exception as e:
            logger.error(f"Error registering default triggers: {e}")
            raise
    
    async def start_async(self):
        """Start the task manager asynchronously"""
        if self.task_manager and not self.task_manager.running:
            await self.task_manager.start()
            logger.info("TaskManager started")

    async def ensure_started(self):
        """Ensure task manager is started (auto-start if needed)"""
        if self.task_manager and not self.task_manager.running:
            logger.info("Auto-starting task manager...")
            await self.start_async()
        elif not self.task_manager:
            logger.error("Task manager not available for auto-start")
    
    async def stop_async(self):
        """Stop the task manager asynchronously"""
        if self.task_manager and self.task_manager.running:
            await self.task_manager.stop()
            logger.info("TaskManager stopped")
    
    def get_task_manager(self) -> Optional[TaskManager]:
        """Get the task manager instance"""
        return self.task_manager
    
    def submit_task(self, task, context) -> Optional[str]:
        """Submit a task for execution"""
        if not self.task_manager:
            logger.warning("Task manager not available")
            return None
        
        return self.task_manager.submit_task(task, context)
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task status"""
        if not self.task_manager:
            return None
        
        return self.task_manager.get_task_status(task_id)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get task service statistics"""
        if not self.task_manager:
            return {"status": "not_initialized"}
        
        return {
            "status": "initialized",
            "task_manager": self.task_manager.get_stats()
        }
    
    def health_check(self) -> Dict[str, Any]:
        """Check task service health"""
        try:
            if not self._initialized:
                return {
                    "status": "error",
                    "message": "TaskService not initialized"
                }
            
            if not self.task_manager:
                return {
                    "status": "error", 
                    "message": "TaskManager not available"
                }
            
            stats = self.task_manager.get_stats()
            
            return {
                "status": "healthy",
                "running": stats.get("running", False),
                "active_tasks": stats.get("active_tasks", 0),
                "queue_size": stats.get("queue_size", 0)
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
