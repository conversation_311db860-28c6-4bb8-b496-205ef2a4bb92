# xi_system/service/__init__.py

"""
Service Layer - Infrastructure Services

Provides unified access to core infrastructure services through dependency injection.
Manages service lifecycle, configuration, and health monitoring.

Components:
- container.py: Central dependency injection container
- config.py: Configuration management with environment variables
- database.py: Database service with MongoDB provider
- llm.py: LLM service with Gemini integration

Key Features:
- Automatic service initialization and cleanup
- Health monitoring and reconnection logic
- Configuration validation and management
- Service dependency resolution

Usage:
    from xi_system.service import initialize_services, get_container

    # Initialize all services
    container = initialize_services()

    # Get specific services
    llm_service = container.get_service('llm')
    database_service = container.get_service('database')

    # Health check
    health = health_check()
"""

import logging
from typing import Optional

from .container import ServiceContainer
from .config import ConfigService
from .database import DatabaseService
from .llm import LLMService
from .task import TaskService
from .embedding import EmbeddingService

logger = logging.getLogger(__name__)

# 全局容器实例
_container: Optional[ServiceContainer] = None


def get_container() -> ServiceContainer:
    """
    获取全局服务容器实例
    
    Returns:
        服务容器实例
    """
    global _container
    if _container is None:
        _container = ServiceContainer.get_instance()
    return _container


def initialize_services(env_file: Optional[str] = None) -> ServiceContainer:
    """
    初始化所有系统服务
    
    Args:
        env_file: 环境配置文件路径
        
    Returns:
        初始化后的服务容器
    """
    container = get_container()
    
    if container._initialized:
        logger.debug("Services already initialized")
        return container
    
    logger.info("Initializing all system services...")
    
    try:
        # 1. 创建并注册配置服务（最先初始化）
        config_service = ConfigService(env_file=env_file)
        config_service.initialize()  # 立即初始化配置服务
        container.register_service('config', config_service)

        # 2. 注册嵌入服务（依赖配置服务）
        embedding_service = EmbeddingService(config_service)
        embedding_service.initialize()
        container.register_service('embedding', embedding_service)

        # 3. 注册数据库服务（依赖配置服务和嵌入服务）
        container.register_service('database', DatabaseService, config_service, embedding_service)

        # 4. 注册LLM服务（依赖配置服务）
        container.register_service('llm', LLMService, config_service)

        # 5. 注册任务服务（依赖配置服务）
        container.register_service('task', TaskService, config_service)

        # 6. Initialize all services
        container.initialize()

        # 7. Store task manager reference in container for easy access
        task_service = container.get_service('task')
        task_manager = task_service.get_task_manager()
        container._task_manager = task_manager

        # Note: Task manager will be started later when needed
        # We don't start it here to avoid multiple event loop conflicts
        logger.info("Task manager initialization deferred to first API request")

        logger.info("All system services initialized successfully")
        return container
        
    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        container.cleanup_all()
        raise


def cleanup_services() -> None:
    """清理所有服务"""
    global _container
    if _container:
        _container.cleanup_all()
        _container = None
        logger.info("All services cleaned up")


def get_config_service() -> ConfigService:
    """获取配置服务"""
    return get_container().get_service('config')


def get_database_service() -> DatabaseService:
    """获取数据库服务"""
    return get_container().get_service('database')


def get_llm_service() -> LLMService:
    """获取LLM服务"""
    return get_container().get_service('llm')


def get_embedding_service() -> EmbeddingService:
    """获取嵌入服务"""
    return get_container().get_service('embedding')


def health_check() -> dict:
    """
    系统服务健康检查
    
    Returns:
        健康检查结果
    """
    try:
        container = get_container()
        
        if not container._initialized:
            return {
                'status': 'error',
                'message': 'Services not initialized',
                'services': {}
            }
        
        service_health = {}
        overall_status = 'healthy'
        
        # 检查各个服务的健康状态
        services_to_check = ['config', 'embedding', 'database', 'llm', 'task']
        
        for service_name in services_to_check:
            try:
                service = container.get_service(service_name)
                
                # 如果服务有health_check方法，调用它
                if hasattr(service, 'health_check'):
                    health = service.health_check()
                    service_health[service_name] = health
                    
                    # 检查服务状态
                    if health.get('status') == 'error':
                        overall_status = 'error'
                    elif health.get('status') in ['degraded', 'warning'] and overall_status == 'healthy':
                        overall_status = 'degraded'
                else:
                    # 如果没有health_check方法，假设服务正常
                    service_health[service_name] = {
                        'status': 'healthy',
                        'message': 'Service available'
                    }
                    
            except Exception as e:
                service_health[service_name] = {
                    'status': 'error',
                    'error': str(e)
                }
                overall_status = 'error'
        
        return {
            'status': overall_status,
            'container_stats': container.get_stats(),
            'services': service_health
        }
        
    except Exception as e:
        return {
            'status': 'error',
            'message': f'Health check failed: {e}',
            'services': {}
        }


# Export main interfaces
__all__ = [
    # Core container
    'ServiceContainer',

    # Service classes
    'ConfigService',
    'DatabaseService',
    'LLMService',
    'TaskService',
    'EmbeddingService',

    # Container management
    'get_container',
    'initialize_services',
    'cleanup_services',

    # Service accessors
    'get_config_service',
    'get_database_service',
    'get_llm_service',
    'get_embedding_service',

    # Health monitoring
    'health_check'
]
