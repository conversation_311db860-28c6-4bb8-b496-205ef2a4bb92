# xi_system/service/container.py

"""
依赖注入容器

中央服务容器，管理所有单例服务的生命周期，解决循环导入问题，
支持延迟初始化和测试时的Mock替换。

设计原则：
- 单例模式确保全局唯一性
- 延迟初始化避免启动时的复杂依赖
- 服务注册机制支持灵活的依赖管理
"""

import logging
from typing import Dict, Any, Optional, Type
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class ServiceInterface(ABC):
    """服务接口基类"""
    
    @abstractmethod
    def initialize(self) -> None:
        """初始化服务"""
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """清理服务资源"""
        pass


class ServiceContainer:
    """
    中央服务容器
    
    管理所有系统服务的生命周期，提供依赖注入能力。
    采用单例模式确保全局唯一性。
    """
    
    _instance: Optional['ServiceContainer'] = None

    def __init__(self):
        if ServiceContainer._instance is not None:
            raise RuntimeError("ServiceContainer is a singleton. Use get_instance() instead.")

        self._services: Dict[str, Any] = {}
        self._service_types: Dict[str, Type] = {}
        self._initialization_order: list = []
        self._initialized: bool = False
        
    @classmethod
    def get_instance(cls) -> 'ServiceContainer':
        """获取容器单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @classmethod
    def reset_instance(cls) -> None:
        """重置实例（主要用于测试）"""
        if cls._instance:
            cls._instance.cleanup_all()
        cls._instance = None
    
    def register_service(self, name: str, service_type_or_instance, *args, **kwargs) -> None:
        """
        注册服务类型或实例

        Args:
            name: 服务名称
            service_type_or_instance: 服务类型或已初始化的实例
            *args, **kwargs: 服务初始化参数（仅当传入类型时使用）
        """
        if name in self._service_types:
            logger.warning(f"Service '{name}' is already registered. Overwriting.")

        # 检查是否是已初始化的实例
        if hasattr(service_type_or_instance, '__class__') and not isinstance(service_type_or_instance, type):
            # 这是一个实例，直接存储
            self._services[name] = service_type_or_instance
            logger.debug(f"Registered service instance: {name}")
        else:
            # 这是一个类型，存储用于延迟初始化
            self._service_types[name] = service_type_or_instance
            self._initialization_order.append((name, args, kwargs))
            logger.debug(f"Registered service type: {name}")
    
    def initialize(self) -> None:
        """延迟初始化所有注册的服务"""
        if self._initialized:
            logger.debug("ServiceContainer already initialized")
            return
        
        logger.info("Initializing ServiceContainer...")
        
        try:
            # 按注册顺序初始化服务
            for name, args, kwargs in self._initialization_order:
                # 跳过已经注册为实例的服务
                if name in self._services:
                    logger.debug(f"Service '{name}' already registered as instance, skipping")
                    continue

                service_type = self._service_types[name]
                logger.debug(f"Initializing service: {name}")

                service_instance = service_type(*args, **kwargs)

                # 如果服务实现了ServiceInterface，调用其initialize方法
                if isinstance(service_instance, ServiceInterface):
                    service_instance.initialize()

                self._services[name] = service_instance
                logger.debug(f"Service '{name}' initialized successfully")
            
            self._initialized = True
            logger.info(f"ServiceContainer initialized with {len(self._services)} services")
            
        except Exception as e:
            logger.error(f"Failed to initialize ServiceContainer: {e}")
            self.cleanup_all()
            raise
    
    def get_service(self, name: str) -> Any:
        """
        获取服务实例
        
        Args:
            name: 服务名称
            
        Returns:
            服务实例
            
        Raises:
            ValueError: 如果服务未注册或未初始化
        """
        if not self._initialized:
            raise ValueError("ServiceContainer not initialized. Call initialize() first.")
        
        if name not in self._services:
            raise ValueError(f"Service '{name}' not found. Available services: {list(self._services.keys())}")
        
        return self._services[name]
    
    def has_service(self, name: str) -> bool:
        """检查服务是否存在"""
        return name in self._services
    
    def list_services(self) -> list:
        """列出所有已注册的服务名称"""
        return list(self._services.keys())
    
    def cleanup_all(self) -> None:
        """清理所有服务资源"""
        logger.info("Cleaning up ServiceContainer...")
        
        for name, service in self._services.items():
            try:
                if isinstance(service, ServiceInterface):
                    service.cleanup()
                logger.debug(f"Service '{name}' cleaned up")
            except Exception as e:
                logger.error(f"Error cleaning up service '{name}': {e}")
        
        self._services.clear()
        self._initialized = False
        logger.info("ServiceContainer cleanup completed")
    
    def replace_service(self, name: str, service_instance: Any) -> None:
        """
        替换服务实例（主要用于测试Mock）
        
        Args:
            name: 服务名称
            service_instance: 新的服务实例
        """
        if name not in self._services:
            raise ValueError(f"Service '{name}' not found")
        
        old_service = self._services[name]
        if isinstance(old_service, ServiceInterface):
            old_service.cleanup()
        
        self._services[name] = service_instance
        logger.debug(f"Service '{name}' replaced")
    
    def get_stats(self) -> dict:
        """获取容器统计信息"""
        return {
            "initialized": self._initialized,
            "service_count": len(self._services),
            "services": list(self._services.keys()),
            "registered_types": list(self._service_types.keys())
        }
