# xi_system/service/database.py

"""
数据库服务

统一管理数据库连接和提供者，支持多种数据库后端。
提供数据库连接池管理和健康检查功能。

设计原则：
- 抽象数据库访问，支持多种后端
- 连接池管理，提升性能
- 健康检查和自动重连
- 统一的错误处理和日志记录
"""

import logging
from typing import Optional, Dict, Any
from abc import ABC, abstractmethod

from .container import ServiceInterface
from .config import ConfigService

logger = logging.getLogger(__name__)


class DatabaseProvider(ABC):
    """数据库提供者抽象基类"""
    
    @abstractmethod
    def connect(self) -> None:
        """建立数据库连接"""
        pass
    
    @abstractmethod
    def disconnect(self) -> None:
        """断开数据库连接"""
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """检查连接状态"""
        pass
    
    @abstractmethod
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        pass


class DatabaseService(ServiceInterface):
    """
    数据库服务
    
    管理数据库连接和提供者，提供统一的数据库访问接口。
    支持连接池管理、健康检查和自动重连。
    """
    
    def __init__(self, config_service: ConfigService, embedding_service):
        """
        初始化数据库服务

        Args:
            config_service: 配置服务实例
            embedding_service: 嵌入服务实例
        """
        self.config = config_service
        self.embedding_service = embedding_service
        self._providers: Dict[str, DatabaseProvider] = {}
        self._primary_provider: Optional[DatabaseProvider] = None
        self._initialized = False
    
    def initialize(self) -> None:
        """初始化数据库服务"""
        if self._initialized:
            return
        
        logger.info("Initializing DatabaseService...")
        
        try:
            # 初始化MongoDB提供者
            self._initialize_mongo_provider()
            
            self._initialized = True
            logger.info("DatabaseService initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize DatabaseService: {e}")
            raise
    
    def cleanup(self) -> None:
        """清理数据库服务"""
        logger.info("Cleaning up DatabaseService...")
        
        for name, provider in self._providers.items():
            try:
                provider.disconnect()
                logger.debug(f"Database provider '{name}' disconnected")
            except Exception as e:
                logger.error(f"Error disconnecting provider '{name}': {e}")
        
        self._providers.clear()
        self._primary_provider = None
        self._initialized = False
        logger.info("DatabaseService cleanup completed")
    
    def _initialize_mongo_provider(self) -> None:
        """初始化MongoDB提供者"""
        try:
            # 延迟导入避免循环依赖
            from ..memory.providers.mongo import MongoProvider
            
            mongo_uri = self.config.get_str('database.mongo_uri')
            db_name = self.config.get_str('database.db_name')
            timeout = self.config.get_int('database.timeout')
            
            if not mongo_uri:
                raise ValueError("MongoDB URI not configured")
            
            mongo_provider = MongoProvider(
                uri=mongo_uri,
                db_name=db_name,
                timeout=timeout,
                embedding_service=self.embedding_service
            )
            
            # 测试连接
            mongo_provider.connect()
            health = mongo_provider.health_check()
            
            if not health.get('connected', False):
                raise RuntimeError("MongoDB connection failed")
            
            self._providers['mongo'] = mongo_provider
            self._primary_provider = mongo_provider
            
            logger.info(f"MongoDB provider initialized: {db_name}")
            
        except Exception as e:
            logger.error(f"Failed to initialize MongoDB provider: {e}")
            raise
    
    def get_provider(self, name: str = 'mongo') -> DatabaseProvider:
        """
        获取数据库提供者
        
        Args:
            name: 提供者名称，默认为'mongo'
            
        Returns:
            数据库提供者实例
            
        Raises:
            ValueError: 如果提供者不存在
        """
        if not self._initialized:
            raise RuntimeError("DatabaseService not initialized")
        
        if name not in self._providers:
            raise ValueError(f"Database provider '{name}' not found. Available: {list(self._providers.keys())}")
        
        provider = self._providers[name]
        
        # 检查连接状态，如果断开则尝试重连
        if not provider.is_connected():
            logger.warning(f"Database provider '{name}' disconnected, attempting to reconnect...")
            try:
                provider.connect()
                logger.info(f"Database provider '{name}' reconnected successfully")
            except Exception as e:
                logger.error(f"Failed to reconnect database provider '{name}': {e}")
                raise
        
        return provider
    
    def get_primary_provider(self) -> DatabaseProvider:
        """获取主数据库提供者"""
        if not self._primary_provider:
            raise RuntimeError("No primary database provider configured")

        return self.get_provider('mongo')  # 使用get_provider确保连接检查

    def get_memory_provider(self):
        """获取内存提供者（兼容V0.83）"""
        return self.get_primary_provider()

    def get_retriever(self):
        """获取检索器（兼容V0.83）"""
        from ..memory.retrieval import Retriever
        provider = self.get_primary_provider()
        return Retriever(provider, embedding_service=self.embedding_service, config_service=self.config)

    def get_prompt_builder(self):
        """获取提示词构建器（兼容V0.83）"""
        from ..prompts.builder import StructuredPromptBuilder
        return StructuredPromptBuilder()
    
    def health_check(self) -> Dict[str, Any]:
        """
        数据库服务健康检查
        
        Returns:
            健康检查结果
        """
        if not self._initialized:
            return {
                'status': 'error',
                'message': 'DatabaseService not initialized',
                'providers': {}
            }
        
        provider_health = {}
        overall_status = 'healthy'
        
        for name, provider in self._providers.items():
            try:
                health = provider.health_check()
                provider_health[name] = health
                
                if not health.get('connected', False):
                    overall_status = 'degraded'
                    
            except Exception as e:
                provider_health[name] = {
                    'status': 'error',
                    'error': str(e),
                    'connected': False
                }
                overall_status = 'error'
        
        return {
            'status': overall_status,
            'initialized': self._initialized,
            'provider_count': len(self._providers),
            'providers': provider_health
        }
    
    def list_providers(self) -> list:
        """列出所有可用的数据库提供者"""
        return list(self._providers.keys())
    
    def add_provider(self, name: str, provider: DatabaseProvider) -> None:
        """
        添加数据库提供者
        
        Args:
            name: 提供者名称
            provider: 提供者实例
        """
        if name in self._providers:
            logger.warning(f"Database provider '{name}' already exists, replacing...")
            old_provider = self._providers[name]
            try:
                old_provider.disconnect()
            except Exception as e:
                logger.error(f"Error disconnecting old provider '{name}': {e}")
        
        self._providers[name] = provider
        logger.info(f"Database provider '{name}' added")
    
    def remove_provider(self, name: str) -> None:
        """
        移除数据库提供者
        
        Args:
            name: 提供者名称
        """
        if name not in self._providers:
            raise ValueError(f"Database provider '{name}' not found")
        
        provider = self._providers[name]
        try:
            provider.disconnect()
        except Exception as e:
            logger.error(f"Error disconnecting provider '{name}': {e}")
        
        del self._providers[name]
        
        # 如果移除的是主提供者，需要重新设置
        if provider == self._primary_provider:
            self._primary_provider = next(iter(self._providers.values())) if self._providers else None
        
        logger.info(f"Database provider '{name}' removed")
