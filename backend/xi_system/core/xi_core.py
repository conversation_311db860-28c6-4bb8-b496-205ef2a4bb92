"""
<PERSON><PERSON>ore - Lightweight Conductor for Xi Intelligent Agent System

This module implements the central orchestration system for the ContextOS.
XiCore serves as a lightweight conductor that coordinates services through
dependency injection and uses unified tool and message processing interfaces.

Core Architecture:
- Dependency injection through ServiceContainer
- Unified tool system (registry + executor)
- Unified message processing (message formatter)
- Clean separation of concerns
- Service orchestration rather than direct implementation

Key Responsibilities:
- Business process flow orchestration
- Service coordination and delegation
- Memory and conversation management
- Streaming response coordination
- Health monitoring and system status
"""

import logging
import uuid
from datetime import datetime, timezone
from typing import List, Generator, Dict, Any

from ..service import ServiceContainer
from ..memory.models import MemoryRecord, MessageRole
from ..agents import AgenticLoopProcessor
from ..memory.session.message_formatter import get_message_formatter
from ..tools import get_tool_executor

logger = logging.getLogger(__name__)


class XiCore:
    """
    Lightweight conductor for the Xi ContextOS.

    XiCore serves as a pure business flow orchestrator that coordinates
    services through dependency injection. It uses unified tool and
    message processing interfaces for clean separation of concerns.

    Architecture Principles:
    1. Service orchestration through ServiceContainer
    2. Unified tool system (registry + executor)
    3. Unified message processing (message formatter)
    4. Clean separation of concerns
    5. Dependency injection pattern
    """

    def __init__(self, container: ServiceContainer):
        """
        Initialize XiCore with dependency injection.

        Args:
            container: ServiceContainer with all required services
        """
        try:
            logger.info("Initializing XiCore...")

            # Store service container
            self.container = container

            # Initialize unified components
            self.message_formatter = get_message_formatter()

            # 传入配置服务给工具执行器
            config_service = container.get_service('config')
            self.tool_executor = get_tool_executor(config_service)

            logger.info("XiCore initialization completed successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize XiCore: {e}")
            raise
    
    def run_stream(self, yu_input: str, session_id: str = None,
                   history: List[MemoryRecord] = None) -> Generator[str, None, None]:
        """
        Process user input and generate streaming response.

        This method serves as a lightweight conductor that delegates to specialized services
        for pure business flow orchestration.

        Args:
            yu_input: User's input message (using personalized naming)
            session_id: Session identifier for context management
            history: Recent conversation history as MemoryRecord objects

        Yields:
            Streaming response content chunks
        """
        try:
            # 1. Generate session ID if not provided
            if not session_id:
                session_id = str(uuid.uuid4())

            # 2. Load recent conversation history (调用 memory.provider)
            if history is None:
                history = self._load_recent_history(session_id)

            # 3. RAG检索 (调用 memory.retriever)
            retrieved_memories = self._retrieve_memories(yu_input)

            # 4. 构建System Prompt (调用 prompts.builder)
            system_prompt = self._build_system_prompt(yu_input, retrieved_memories)

            # 5. 构建完整消息列表 (调用 memory.context_builder)
            messages = self._build_message_list(system_prompt, history, yu_input)

            # 6. 执行代理循环 (调用 agents.agentic_loop)
            accumulated_response = ""
            for chunk in self._execute_agentic_loop(messages):
                accumulated_response += chunk
                yield chunk

            # 7. 存储对话 (调用 memory.provider)
            self._store_conversation(session_id, yu_input, accumulated_response)

        except Exception as e:
            logger.error(f"Error in run_stream: {e}")
            yield f"处理请求时发生错误: {str(e)}"
    
    def _load_recent_history(self, session_id: str) -> List[MemoryRecord]:
        """Load recent conversation history (调用 memory.provider)"""
        try:
            database_service = self.container.get_service('database')
            memory_provider = database_service.get_memory_provider()

            # 使用配置中的对话历史限制
            config_service = self.container.get_service('config')
            limit = config_service.get_int('conversation.max_history', 20)

            return memory_provider.retrieve_recent("conversations", limit=limit, session_id=session_id)
        except Exception as e:
            logger.error(f"Error loading recent history: {e}")
            return []

    def _retrieve_memories(self, yu_input: str) -> List[MemoryRecord]:
        """RAG检索 (调用 memory.retriever)"""
        try:
            database_service = self.container.get_service('database')
            retriever = database_service.get_retriever()

            # 使用配置中的RAG检索限制
            config_service = self.container.get_service('config')
            limit = config_service.get_int('rag.max_results', 10)

            return retriever.retrieve_memories(yu_input, limit=limit)
        except Exception as e:
            logger.error(f"Error retrieving memories: {e}")
            return []

    def _build_system_prompt(self, yu_input: str, retrieved_memories: List[MemoryRecord]) -> str:
        """构建System Prompt (调用 prompts.builder)"""
        try:
            database_service = self.container.get_service('database')
            prompt_builder = database_service.get_prompt_builder()
            return prompt_builder.build_system_prompt(yu_input, retrieved_memories)
        except Exception as e:
            logger.error(f"Error building system prompt: {e}")
            return "系统提示词构建失败，使用基础配置。"

    def _build_message_list(self, system_prompt: str, history: List[MemoryRecord], yu_input: str) -> List[Dict[str, Any]]:
        """构建完整消息列表 (使用统一消息格式化器)"""
        try:
            # 使用统一消息格式化器
            messages = self.message_formatter.build_messages(
                system_prompt=system_prompt,
                history=history,
                current_input=yu_input
            )

            # 验证消息格式
            if self.message_formatter.validate_messages(messages):
                return messages
            else:
                logger.error("Message validation failed")
                return []
        except Exception as e:
            logger.error(f"Error building message list: {e}")
            return []

    def _execute_agentic_loop(self, messages: List[Dict[str, Any]]) -> Generator[str, None, None]:
        """执行代理循环 (使用统一工具系统)"""
        try:
            # Get services
            llm_service = self.container.get_service('llm')
            config_service = self.container.get_service('config')

            # 使用配置中的工具最大迭代次数
            max_iterations = config_service.get_int('tool.max_iterations', 5)

            # Create agentic loop processor with unified tool executor
            agentic_processor = AgenticLoopProcessor(
                llm_client=llm_service.get_client(),
                toolbox=self.tool_executor,  # 使用统一工具执行器
                max_iterations=max_iterations,
                llm_service=llm_service  # 传递 llm_service 以支持测试模式
            )

            # Get model config
            model_config = llm_service.get_config()

            # Process agentic loop
            for chunk in agentic_processor.process_agentic_loop(messages, model_config):
                yield chunk

        except Exception as e:
            logger.error(f"Error in agentic loop: {e}")
            yield f"代理循环处理失败: {str(e)}"


    def _store_conversation(self, session_id: str, yu_input: str, xi_response: str):
        """
        Store conversation in memory.
        
        Args:
            session_id: Session identifier
            yu_input: User input
            xi_response: Xi's response
        """
        try:
            database_service = self.container.get_service('database')
            memory_provider = database_service.get_memory_provider()
            current_time = datetime.now(timezone.utc)

            # Store user message
            user_record = MemoryRecord(
                content=yu_input,
                role=MessageRole.YU,
                timestamp=current_time,
                source_session_id=session_id
            )
            memory_provider.store("conversations", user_record)

            # Store assistant response
            assistant_record = MemoryRecord(
                content=xi_response,
                role=MessageRole.XI,
                timestamp=current_time,
                source_session_id=session_id
            )
            memory_provider.store("conversations", assistant_record)

            logger.info(f"Stored conversation for session {session_id}")

            # Trigger background tasks (reflection, etc.) through event system
            self._trigger_background_tasks(session_id, memory_provider)

        except Exception as e:
            logger.error(f"Error storing conversation: {e}")

    def _trigger_background_tasks(self, session_id: str, memory_provider):
        """
        Trigger background tasks through event system.

        This method maintains the lightweight conductor principle by simply
        emitting events rather than executing complex business logic.

        Args:
            session_id: Session identifier
            memory_provider: Memory provider instance
        """
        try:
            # Get task manager from container (if available)
            task_manager = getattr(self.container, '_task_manager', None)

            if task_manager:
                # Emit conversation_stored event
                event_data = {
                    'session_id': session_id,
                    'service_container': self.container,
                    'memory_provider': memory_provider
                }

                task_manager.handle_event('conversation_stored', event_data)
                logger.debug(f"Triggered background tasks for session {session_id}")
            else:
                logger.debug("Task manager not available, skipping background tasks")

        except Exception as e:
            logger.error(f"Error triggering background tasks: {e}")
            # Don't raise - background task failures shouldn't affect main flow



    def run_sync(self, yu_input: str, session_id: str = None,
                 history: List[MemoryRecord] = None) -> str:
        """
        Process user input and generate synchronous response.

        Args:
            yu_input: User's input message
            session_id: Session identifier
            history: Recent conversation history

        Returns:
            Complete response string
        """
        try:
            response_parts = []
            for chunk in self.run_stream(yu_input, session_id, history):
                response_parts.append(chunk)

            return "".join(response_parts)

        except Exception as e:
            logger.error(f"Error in run_sync: {e}")
            return f"处理请求时发生错误: {str(e)}"

    def get_latest_history(self, limit: int = 20, session_id: str = None) -> dict:
        """
        Get latest conversation history for API consumption.

        Args:
            limit: Maximum number of messages to retrieve
            session_id: Optional session identifier to filter by

        Returns:
            Dictionary with messages list and has_more flag
        """
        try:
            database_service = self.container.get_service('database')
            memory_provider = database_service.get_memory_provider()

            # Retrieve messages with one extra to check if there are more
            records = memory_provider.retrieve_recent(
                "conversations",
                limit=limit + 1,
                session_id=session_id
            )

            # Check if there are more messages
            has_more = len(records) > limit
            if has_more:
                records = records[:limit]  # Remove the extra record

            # Convert MemoryRecord objects to API format
            messages = []
            for record in records:
                message = {
                    "id": str(record.id) if record.id else "",
                    "content": record.content,
                    "role": record.role.value if record.role else "system",
                    "timestamp": record.timestamp.isoformat() if record.timestamp else "",
                    "metadata": record.metadata or {}
                }
                messages.append(message)

            return {
                "messages": messages,
                "has_more": has_more
            }

        except Exception as e:
            logger.error(f"Error getting latest history: {e}")
            return {
                "messages": [],
                "has_more": False
            }
    
    def get_health_status(self) -> Dict[str, any]:
        """
        Get basic system health status.

        Returns:
            Basic health status dictionary
        """
        try:
            # Simple health check - just verify core components exist
            is_healthy = (
                self.container is not None and
                self.message_formatter is not None and
                self.tool_executor is not None
            )

            return {
                "status": "healthy" if is_healthy else "unhealthy",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting health status: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def get_stats(self) -> Dict[str, any]:
        """
        Get basic system statistics.

        Returns:
            Basic statistics dictionary
        """
        try:
            return {
                "architecture": "lightweight_conductor",
                "components_initialized": {
                    "container": self.container is not None,
                    "message_formatter": self.message_formatter is not None,
                    "tool_executor": self.tool_executor is not None
                },
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting stats: {e}")
            return {"error": str(e)}

    def search_memories(self, query: str, limit: int = None) -> List[Dict[str, any]]:
        """
        Search memories using semantic similarity.

        Args:
            query: Search query string
            limit: Maximum number of results to return (uses config default if None)

        Returns:
            List of memory records as dictionaries
        """
        try:
            database_service = self.container.get_service('database')
            retriever = database_service.get_retriever()

            # 如果没有指定limit，使用配置中的默认值
            if limit is None:
                config_service = self.container.get_service('config')
                limit = config_service.get_int('rag.max_results', 10)

            # Retrieve memories using the retriever
            memories = retriever.retrieve_memories(query, limit=limit)

            # Convert MemoryRecord objects to dictionaries
            memory_dicts = []
            for memory in memories:
                memory_dict = {
                    "content": memory.content,
                    "role": memory.role.value if hasattr(memory.role, 'value') else str(memory.role),
                    "timestamp": memory.timestamp.isoformat() if memory.timestamp else None,
                    "source_session_id": memory.source_session_id,
                    "metadata": memory.metadata or {}
                }
                memory_dicts.append(memory_dict)

            logger.info(f"Memory search completed: {len(memory_dicts)} results for query '{query}'")
            return memory_dicts

        except Exception as e:
            logger.error(f"Error searching memories: {e}")
            return []
