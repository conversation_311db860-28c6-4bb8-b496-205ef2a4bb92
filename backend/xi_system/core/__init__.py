"""
Core Orchestration - Lightweight Business Process Conductor

Contains <PERSON><PERSON><PERSON>, the central orchestrator that coordinates all system services
through dependency injection and delegates specialized tasks to agents.

Key Responsibilities:
- Service orchestration through dependency injection
- Business process flow management
- Agent delegation for specialized tasks
- Streaming response coordination
- Memory and conversation management

Architecture Principles:
- Lightweight conductor pattern
- Clean separation of concerns
- Dependency injection for all services
- Stateless operation (state managed by services)

Usage:
    from xi_system.core import Xi<PERSON>ore
    from xi_system.service import initialize_services

    container = initialize_services()
    xi_core = XiCore(container)

    # Process user input with streaming
    for chunk in xi_core.process_input_stream("Hello", session_id="123"):
        print(chunk, end="")
"""

from .xi_core import <PERSON><PERSON><PERSON>

__all__ = [
    'Xi<PERSON>ore'
]
