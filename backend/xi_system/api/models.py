# xi_system/api/models.py

"""
Pydantic models for Xi Intelligent Agent System API.

This module defines the data contracts for API requests and responses,
ensuring type safety and automatic validation for all API interactions.
"""

from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime





class HealthResponse(BaseModel):
    """
    Response model for health check endpoint.
    """
    status: str = Field(
        ...,
        description="Service status",
        example="healthy"
    )
    version: str = Field(
        ...,
        description="API version",
        example="0.3.0"
    )
    message: str = Field(
        ...,
        description="Status message",
        example="Xi Core Engine is running."
    )


class ErrorResponse(BaseModel):
    """
    Response model for error cases.
    """
    error: str = Field(
        ...,
        description="Error type",
        example="validation_error"
    )
    message: str = Field(
        ...,
        description="Error message",
        example="Input validation failed"
    )
    details: Optional[str] = Field(
        None,
        description="Additional error details",
        example="yu_input field is required"
    )


class HistoryMessage(BaseModel):
    """
    Single message in conversation history.
    """
    id: str = Field(
        ...,
        description="Message unique identifier",
        example="507f1f77bcf86cd799439011"
    )
    content: str = Field(
        ...,
        description="Message content",
        example="Hello, how can I help you today?"
    )
    role: str = Field(
        ...,
        description="Message role (yu or xi)",
        example="xi"
    )
    timestamp: str = Field(
        ...,
        description="Message timestamp in ISO 8601 format",
        example="2024-01-15T10:30:00Z"
    )
    metadata: dict = Field(
        default_factory=dict,
        description="Additional message metadata",
        example={}
    )


class HistoryResponse(BaseModel):
    """
    Response model for conversation history endpoint.
    """
    messages: List[HistoryMessage] = Field(
        ...,
        description="List of conversation messages",
        example=[]
    )
    has_more: bool = Field(
        ...,
        description="Whether there are more messages available",
        example=False
    )
