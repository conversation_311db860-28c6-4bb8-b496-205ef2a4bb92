# xi_system/api/connection_manager.py

"""
WebSocket连接管理器 - Xi智能体系统

本模块实现WebSocket连接的生命周期管理，为Xi系统提供稳定的双向通信能力。
ConnectionManager采用单例模式，确保全局统一的连接管理。

核心功能：
- WebSocket连接的接受和存储
- 连接断开的清理和日志记录
- 个人消息发送和错误处理
- 广播消息功能（为未来扩展准备）
- 连接状态监控和异常处理

设计原则：
- 单例模式确保全局唯一实例
- 异常安全的连接管理
- 详细的日志记录便于调试
- 为未来功能扩展预留接口

使用示例：
    from .connection_manager import manager
    
    # 接受新连接
    await manager.connect(websocket, client_id)
    
    # 发送消息
    await manager.send_personal_message("Hello", client_id)
    
    # 断开连接
    manager.disconnect(client_id)
"""

import logging
from typing import Dict, List, Optional
from fastapi import WebSocket

logger = logging.getLogger(__name__)

class ConnectionManager:
    """
    管理活跃的WebSocket连接。
    这是一个单例模式的实现，确保全局只有一个连接管理器。
    
    Attributes:
        active_connections: 存储活跃连接的字典，键为client_id，值为WebSocket对象
    """
    
    def __init__(self):
        """初始化连接管理器"""
        self.active_connections: Dict[str, WebSocket] = {}
        logger.info("ConnectionManager initialized.")

    async def connect(self, websocket: WebSocket, client_id: str):
        """
        接受并存储一个新的WebSocket连接。
        
        Args:
            websocket: WebSocket连接对象
            client_id: 客户端唯一标识符
            
        Raises:
            Exception: 当WebSocket接受失败时抛出异常
        """
        try:
            await websocket.accept()
            self.active_connections[client_id] = websocket
            logger.info(f"New WebSocket connection accepted: {client_id}")
        except Exception as e:
            logger.error(f"Failed to accept WebSocket connection for {client_id}: {e}")
            raise

    def disconnect(self, client_id: str):
        """
        断开并移除一个WebSocket连接。
        
        Args:
            client_id: 要断开的客户端标识符
        """
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"WebSocket connection disconnected: {client_id}")
        else:
            logger.warning(f"Attempted to disconnect non-existent client: {client_id}")

    async def send_personal_message(self, message: str, client_id: str):
        """
        向指定的客户端发送消息。
        
        Args:
            message: 要发送的消息内容
            client_id: 目标客户端标识符
            
        Note:
            如果发送失败，会自动断开该连接并记录错误
        """
        websocket = self.active_connections.get(client_id)
        if websocket:
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.error(f"Failed to send message to {client_id}: {e}")
                self.disconnect(client_id)
        else:
            logger.warning(f"Attempted to send message to non-existent client: {client_id}")

    async def broadcast(self, message: str):
        """
        向所有连接的客户端广播消息（未来功能）。
        
        Args:
            message: 要广播的消息内容
            
        Note:
            发送失败的连接会被自动断开
        """
        disconnected_clients = []
        
        for client_id, connection in list(self.active_connections.items()):
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Failed to broadcast to {client_id}: {e}")
                disconnected_clients.append(client_id)
        
        # 清理失败的连接
        for client_id in disconnected_clients:
            self.disconnect(client_id)
        
        if disconnected_clients:
            logger.info(f"Cleaned up {len(disconnected_clients)} failed connections during broadcast")

    def get_connection_count(self) -> int:
        """
        获取当前活跃连接数量。
        
        Returns:
            int: 活跃连接的数量
        """
        return len(self.active_connections)

    def get_connected_clients(self) -> List[str]:
        """
        获取所有连接的客户端ID列表。
        
        Returns:
            List[str]: 客户端ID列表
        """
        return list(self.active_connections.keys())

    def is_connected(self, client_id: str) -> bool:
        """
        检查指定客户端是否已连接。
        
        Args:
            client_id: 客户端标识符
            
        Returns:
            bool: 如果客户端已连接返回True，否则返回False
        """
        return client_id in self.active_connections


# 创建一个全局单例
manager = ConnectionManager()
