"""
API module for Xi Intelligent Agent System.

Contains FastAPI routes and data models for web service interface.
Provides WebSocket and RESTful endpoints for chat interactions, health checks, and system status.

Components:
- routes.py: FastAPI route handlers for all endpoints
- models.py: Pydantic data models for request/response validation
- connection_manager.py: WebSocket connection management

Main Endpoints:
- WebSocket /ws/chat: Real-time chat interaction endpoint
- GET /health: System health check
- GET /status: Comprehensive system status
- GET /memory/search: Memory search functionality

Usage:
    from xi_system.api import router, HealthResponse
    from fastapi import FastAPI

    app = FastAPI()
    app.include_router(router)

    # For advanced use cases (monitoring, management):
    from xi_system.api import ConnectionManager
    manager = ConnectionManager()
    connection_count = manager.get_connection_count()
"""

from .routes import router
from .models import (
    HealthResponse,
    ErrorResponse
)
from .connection_manager import ConnectionManager

__all__ = [
    'router',
    'HealthResponse',
    'ErrorResponse',
    # Internal components (for advanced use cases)
    'ConnectionManager'
]
