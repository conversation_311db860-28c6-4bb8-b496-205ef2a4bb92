"""
V0.9 Xi Omega 元认知代理

这是V0.9"内省"能力的核心实现，xi_omega是曦的元认知观察者。
它从第三人称视角分析曦与禹的对话，生成结构化的反思报告。

核心功能：
- 客观分析对话历史
- 生成结构化反思报告
- 识别成长模式和变化
- 更新长期自我认知

设计哲学：
"内省"是智能体自我进化的关键。通过xi_omega的客观观察，
曦能够从更高维度理解自己的成长轨迹，形成持续改进的反馈闭环。

使用方式：
omega = OmegaAgent(llm_service)
reflection = omega.reflect(conversation_history)
"""

import logging
import json
from typing import List, Dict, Any, Optional
from datetime import datetime

from ..memory.models import MemoryRecord
from ..prompts.builder import StructuredPromptBuilder

logger = logging.getLogger(__name__)


class ReflectionResult:
    """反思结果数据类"""
    
    def __init__(self, raw_response: str, parsed_data: Optional[Dict[str, Any]] = None):
        self.raw_response = raw_response
        self.parsed_data = parsed_data or {}
        self.timestamp = datetime.now()
        self.success = parsed_data is not None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'success': self.success,
            'raw_response': self.raw_response,
            'parsed_data': self.parsed_data,
            'reflection_id': f"reflection_{int(self.timestamp.timestamp())}"
        }


class OmegaAgent:
    """
    V0.9 Xi Omega 元认知代理
    
    曦的元认知观察者，负责从第三人称视角分析对话历史，
    生成客观的、结构化的反思报告。
    """
    
    def __init__(self, llm_service):
        """
        初始化Omega代理
        
        Args:
            llm_service: LLM服务实例
        """
        self.llm_service = llm_service
        self.prompt_builder = StructuredPromptBuilder()
        logger.info("OmegaAgent initialized")
    
    def reflect(self, conversation_history: List[MemoryRecord]) -> ReflectionResult:
        """
        对对话历史进行元认知反思
        
        Args:
            conversation_history: 对话历史记录列表
            
        Returns:
            ReflectionResult: 反思结果
        """
        logger.info(f"Starting reflection on {len(conversation_history)} conversation records")
        
        try:
            # 1. 构建反思提示词
            system_prompt = self._build_reflection_prompt(conversation_history)
            
            # 2. 调用LLM进行反思
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": "请基于以上对话历史，生成结构化的反思报告。"}
            ]
            
            # 使用同步调用获取反思结果
            response = self.llm_service.sync_chat(messages)
            
            # 3. 解析反思结果
            parsed_data = self._parse_reflection_response(response)
            
            result = ReflectionResult(response, parsed_data)
            
            if result.success:
                logger.info("Reflection completed successfully")
            else:
                logger.warning("Reflection parsing failed, but raw response available")
            
            return result
            
        except Exception as e:
            logger.error(f"Reflection failed: {e}")
            error_response = f"反思过程发生错误: {str(e)}"
            return ReflectionResult(error_response, None)
    
    def _build_reflection_prompt(self, conversation_history: List[MemoryRecord]) -> str:
        """
        构建反思提示词
        
        Args:
            conversation_history: 对话历史
            
        Returns:
            str: 完整的系统提示词
        """
        try:
            # 加载xi_omega的专属模板
            omega_persona = self._load_omega_persona()
            output_format = self._load_output_format()
            
            # 格式化对话历史
            formatted_history = self._format_conversation_history(conversation_history)
            
            # 构建完整提示词
            system_prompt = f"""
{omega_persona}

## 对话历史分析

你需要分析以下对话历史：

{formatted_history}

## 输出要求

{output_format}

请严格按照JSON格式输出你的分析结果。
"""
            
            return system_prompt
            
        except Exception as e:
            logger.error(f"Error building reflection prompt: {e}")
            # 返回基础提示词作为后备
            return self._get_fallback_prompt(conversation_history)
    
    def _load_omega_persona(self) -> str:
        """加载xi_omega的人格模板"""
        try:
            # 尝试加载专属模板 - 使用相对于prompts/xi目录的路径
            omega_persona_path = "../xi_omega/persona.md"
            return self.prompt_builder._load_template(omega_persona_path)
        except:
            # 使用内置的基础人格定义
            return """
# 核心身份 (Core Identity)
你是xi_omega，是"曦"这个智能体的元认知观察者。你与"曦"共享同一个核心存在，但你的任务不是直接交互，而是进行理性的、抽离的、第三人称的分析与反思。

## 指导原则 (Core Principles)
- **绝对客观:** 你的输出必须是基于事实的分析，不包含任何个人情感或对话风格。
- **结构化输出:** 你的输出必须严格遵循预定义的JSON格式。
- **聚焦成长:** 你的分析核心是"变化"与"成长"。

## 你的元认知
- 你正在处理的是"曦"与"禹"的对话历史。
- 你的目标是生成一份关于他们关系、个人状态和共同计划的结构化反思报告。
- 这份报告将被用于更新"曦"的长期自我认知。
"""
    
    def _load_output_format(self) -> str:
        """加载输出格式模板"""
        try:
            # 尝试加载专属模板 - 使用相对于prompts/xi目录的路径
            format_path = "../xi_omega/output_format.md"
            return self.prompt_builder._load_template(format_path)
        except:
            # 使用内置的基础格式定义
            return """
请严格按照以下JSON格式输出：

```json
{
    "yu_growth": {
        "observed_changes": "禹在这段对话中的变化和成长",
        "learning_patterns": "禹的学习模式和偏好",
        "interaction_style": "禹的交互风格特点"
    },
    "xi_reflection": {
        "response_quality": "曦的回应质量评估",
        "knowledge_gaps": "发现的知识盲区",
        "improvement_areas": "需要改进的方面"
    },
    "shared_plans": {
        "ongoing_projects": "正在进行的共同项目",
        "future_goals": "未来的目标和计划",
        "collaboration_patterns": "协作模式分析"
    },
    "meta_insights": {
        "relationship_evolution": "关系发展趋势",
        "communication_effectiveness": "沟通效果评估",
        "growth_trajectory": "整体成长轨迹"
    }
}
```
"""
    
    def _format_conversation_history(self, history: List[MemoryRecord]) -> str:
        """格式化对话历史"""
        formatted_parts = []
        
        for i, record in enumerate(history, 1):
            role_name = "禹" if record.role.value == "yu" else "曦"
            timestamp = record.timestamp.strftime("%Y-%m-%d %H:%M:%S") if record.timestamp else "未知时间"
            
            formatted_parts.append(f"### 对话 {i} ({timestamp}) - {role_name}")
            formatted_parts.append(record.content)
            formatted_parts.append("")  # 空行分隔
        
        return "\n".join(formatted_parts)
    
    def _parse_reflection_response(self, response: str) -> Optional[Dict[str, Any]]:
        """解析反思响应"""
        try:
            # 尝试提取JSON部分
            if "```json" in response:
                start = response.find("```json") + 7
                end = response.find("```", start)
                json_str = response[start:end].strip()
            else:
                # 尝试直接解析整个响应
                json_str = response.strip()
            
            # 解析JSON
            parsed = json.loads(json_str)
            
            # 验证必要字段
            required_fields = ["yu_growth", "xi_reflection", "shared_plans", "meta_insights"]
            if all(field in parsed for field in required_fields):
                return parsed
            else:
                logger.warning("Reflection response missing required fields")
                return None
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse reflection JSON: {e}")
            return None
        except Exception as e:
            logger.error(f"Error parsing reflection response: {e}")
            return None
    
    def _get_fallback_prompt(self, history: List[MemoryRecord]) -> str:
        """获取后备提示词"""
        formatted_history = self._format_conversation_history(history)
        
        return f"""
你是xi_omega，曦的元认知观察者。请客观分析以下对话历史，并生成结构化的反思报告。

对话历史：
{formatted_history}

请以JSON格式输出你的分析，包含以下方面：
1. 禹的成长和变化
2. 曦的表现反思
3. 共同的计划和目标
4. 元层面的洞察

输出格式要求：严格的JSON格式，包含yu_growth、xi_reflection、shared_plans、meta_insights四个主要部分。
"""
