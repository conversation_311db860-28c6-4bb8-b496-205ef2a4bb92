"""
Agent System - Intelligent Interaction Processors

Defines different LLM interaction modes and patterns for complex AI behaviors.
Each agent represents a specific interaction pattern with the language model.

Core Responsibilities:
- Define LLM interaction patterns (tool calling loops, reflection, planning)
- Handle complex multi-turn conversation logic
- Manage interaction state and context

Design Principles:
- Pattern-focused: Each agent implements a specific interaction pattern
- Modular design: Agents can be composed and extended
- State management: Handle complex conversation flows
- Extensible: Easy to add new interaction patterns

Current Interaction Patterns:
- AgenticLoopProcessor: Tool-calling loop with iterative refinement

Future Patterns (extensible):
- ReflectionLoopProcessor: Self-reflection and introspection
- PlanningLoopProcessor: Multi-step planning and execution
- CollaborativeLoopProcessor: Multi-agent collaboration

Usage:
    from xi_system.agents import AgenticLoopProcessor

    processor = AgenticLoopProcessor(container)
    result = processor.process_with_tools(messages, max_iterations=5)
"""

from .agentic_loop import AgenticLoopProcessor, AgenticLoopResult

__all__ = [
    'AgenticLoopProcessor',
    'AgenticLoopResult'
]