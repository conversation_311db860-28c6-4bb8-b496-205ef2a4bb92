"""
Agentic Loop Processor for Xi ContextOS V0.84

This module handles the complex LLM interaction logic including tool calling loops,
streaming responses, and iterative processing. Updated for V0.84 to use the unified
tool execution system.

V0.84 Updates:
- Uses unified ToolExecutor instead of multiple toolbox implementations
- Simplified tool execution interface
- Improved error handling and logging

Core functionality:
- _process_agentic_loop method
- _execute_tool_call method
- Tool calling iteration logic
- Streaming response handling
"""

import json
import logging
from typing import List, Dict, Any, Generator, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class AgenticLoopResult:
    """Result of agentic loop processing."""
    content: str
    tool_calls_made: int
    iterations: int
    success: bool
    error: Optional[str] = None


class AgenticLoopProcessor:
    """
    Handles complex LLM interaction logic with tool calling loops.
    
    This class is responsible for:
    1. Managing streaming responses from LLM
    2. Detecting and executing tool calls
    3. Iterating between LLM and tools until completion
    4. Handling errors and timeouts in the loop
    """
    
    def __init__(self, llm_client, toolbox, max_iterations: int = 5, llm_service=None):
        """
        Initialize the agentic loop processor.

        Args:
            llm_client: LLM client for API calls (can be None in test mode)
            toolbox: ToolExecutor instance for unified tool execution
            max_iterations: Maximum number of tool call iterations
            llm_service: LLMService instance for test mode support
        """
        self.llm_client = llm_client
        self.llm_service = llm_service
        self.tool_executor = toolbox  # 统一工具执行器
        self.max_iterations = max_iterations
        self.is_test_mode = llm_client is None
        logger.info(f"AgenticLoopProcessor initialized with max {max_iterations} iterations (test_mode: {self.is_test_mode})")
    
    def process_agentic_loop(
        self,
        messages: List[Dict[str, Any]],
        model_config: Dict[str, Any]
    ) -> Generator[str, None, None]:
        """
        Process the complete agentic loop with tool calling.
        
        This method handles:
        1. Initial LLM response (streaming)
        2. Tool call detection and execution
        3. Iterative processing until completion
        
        Args:
            messages: Message history for LLM
            model_config: LLM model configuration
            
        Yields:
            Response content chunks
        """
        current_messages = messages.copy()
        total_iterations = 0
        total_tool_calls = 0
        
        try:
            # 如果是测试模式，直接使用 LLMService 的同步方法
            if self.is_test_mode and self.llm_service:
                logger.info("Using test mode - delegating to LLMService sync_chat")
                response = self.llm_service.sync_chat(current_messages)
                # 模拟流式输出
                chunk_size = 3
                for i in range(0, len(response), chunk_size):
                    yield response[i:i+chunk_size]
                yield "[STREAM_END]"
                return

            for iteration in range(self.max_iterations):
                total_iterations += 1
                logger.info(f"Agentic loop iteration {iteration + 1}/{self.max_iterations}")

                # Get tools for this iteration
                tools = self.tool_executor.get_available_tools()

                # Create streaming response
                try:
                    response = self.llm_client.chat.completions.create(
                        messages=current_messages,
                        model=model_config.get("model", "gemini-2.5-flash"),
                        reasoning_effort=model_config.get("reasoning_effort", "none"),
                        stream=True,
                        tools=tools if tools else None
                    )
                except Exception as e:
                    logger.error(f"LLM API call failed: {e}")
                    yield f"LLM调用失败: {str(e)}"
                    return
                
                # Process streaming response
                accumulated_content = ""
                tool_calls = []
                
                for chunk in response:
                    try:
                        # Check for tool calls in chunk
                        if hasattr(chunk, 'choices') and chunk.choices:
                            delta = chunk.choices[0].delta

                            # Handle tool calls
                            if hasattr(delta, 'tool_calls') and delta.tool_calls:
                                for tool_call in delta.tool_calls:
                                    if tool_call not in tool_calls:
                                        tool_calls.append(tool_call)
                                        yield " [正在使用我的能力...🛠️] "

                            # Handle content
                            if hasattr(delta, 'content') and delta.content:
                                content = delta.content
                                accumulated_content += content
                                yield content
                                
                    except Exception as e:
                        logger.warning(f"Error processing chunk: {e}")
                        continue
                
                # If no tool calls, we're done
                if not tool_calls:
                    logger.info(f"Agentic loop completed after {total_iterations} iterations, {total_tool_calls} tool calls")
                    return
                
                # Execute tool calls
                logger.info(f"Executing {len(tool_calls)} tool calls")
                total_tool_calls += len(tool_calls)
                
                # Add assistant message with tool calls
                assistant_message = {
                    "role": "assistant",
                    "content": accumulated_content,
                    "tool_calls": [self._serialize_tool_call(tc) for tc in tool_calls]
                }
                current_messages.append(assistant_message)
                
                # Execute each tool call
                for tool_call in tool_calls:
                    result = self._execute_tool_call(tool_call)
                    
                    tool_result_message = {
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": result
                    }
                    current_messages.append(tool_result_message)
                
                # Continue to next iteration
                
            # Max iterations reached
            logger.warning(f"Agentic loop reached max iterations ({self.max_iterations})")
            yield f"\n\n我进行了{self.max_iterations}轮思考，已经为你提供了最佳的回答。"
            
        except Exception as e:
            logger.error(f"Error in agentic loop: {e}")
            yield f"\n\n处理过程中发生错误: {str(e)}"
    
    def _execute_tool_call(self, tool_call) -> str:
        """
        Execute a single tool call using unified tool executor.

        Args:
            tool_call: Tool call object from LLM

        Returns:
            Tool execution result as string
        """
        try:
            tool_name = tool_call.function.name
            tool_args = json.loads(tool_call.function.arguments)

            logger.info(f"Executing tool: {tool_name} with args: {tool_args}")

            # Execute using V0.84 unified tool executor
            result = self.tool_executor.execute_tool_safe(tool_name, **tool_args)

            logger.info(f"Tool {tool_name} executed successfully")
            return result
            
        except Exception as e:
            error_msg = f"工具执行失败: {str(e)}"
            logger.error(f"Tool execution failed: {e}")
            return error_msg
    
    def _serialize_tool_call(self, tool_call) -> Dict[str, Any]:
        """
        Serialize tool call for message history.
        
        Args:
            tool_call: Tool call object
            
        Returns:
            Serialized tool call dictionary
        """
        try:
            return {
                "id": tool_call.id,
                "type": "function",
                "function": {
                    "name": tool_call.function.name,
                    "arguments": tool_call.function.arguments
                }
            }
        except Exception as e:
            logger.error(f"Error serializing tool call: {e}")
            return {
                "id": getattr(tool_call, 'id', 'unknown'),
                "type": "function", 
                "function": {
                    "name": getattr(tool_call.function, 'name', 'unknown'),
                    "arguments": "{}"
                }
            }
    
    def process_sync(
        self,
        messages: List[Dict[str, Any]],
        model_config: Dict[str, Any]
    ) -> AgenticLoopResult:
        """
        Process agentic loop synchronously and return complete result.
        
        Args:
            messages: Message history for LLM
            model_config: LLM model configuration
            
        Returns:
            AgenticLoopResult with complete response
        """
        try:
            content_parts = []
            tool_calls_made = 0
            iterations = 0
            
            for chunk in self.process_agentic_loop(messages, model_config):
                content_parts.append(chunk)
                if "[正在使用我的能力...🛠️]" in chunk: 
                    tool_calls_made += 1
            
            complete_content = "".join(content_parts)
            
            return AgenticLoopResult(
                content=complete_content,
                tool_calls_made=tool_calls_made,
                iterations=iterations,
                success=True
            )
            
        except Exception as e:
            logger.error(f"Error in sync processing: {e}")
            return AgenticLoopResult(
                content=f"处理失败: {str(e)}",
                tool_calls_made=0,
                iterations=0,
                success=False,
                error=str(e)
            )
