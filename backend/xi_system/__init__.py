"""
Xi Intelligent Agent System: Core Engine

A modular AI companion system designed for <PERSON> (禹) and <PERSON> (曦).
Provides unified access to all system components through clean interfaces.

Main Components:
- XiCore: Lightweight conductor for service orchestration
- Memory System: Complete memory management with retrieval and curation
- Service Layer: Infrastructure services (LLM, Database, Config)
- Tool System: Unified tool registry and execution
- Agent System: Intelligent interaction processors
- API Layer: FastAPI web service interface

Usage:
    from xi_system import Xi<PERSON>ore, initialize_services
    from xi_system.memory import MemoryRecord, Retriever
    from xi_system.tools import get_tool_executor

    # Initialize system
    container = initialize_services()
    xi_core = XiCore(container)

    # Use components
    executor = get_tool_executor()
    result = executor.execute_tool("read_note", filename="test.md")
"""

# Core orchestration
from .core import XiCore

# Service layer
from .service import (
    initialize_services,
    cleanup_services,
    get_container,
    get_config_service,
    get_database_service,
    get_llm_service,
    health_check as service_health_check
)

# Memory system
from .memory import (
    MemoryRecord,
    MessageRole,
    YU, XI, XI_SYSTEM, TOOL, USER, ASSISTANT, SYSTEM,
    MongoProvider,
    Retriever,
    HistoryManager,
    MessageFormatter
)

# Tool system
from .tools import (
    get_tool_registry,
    get_tool_executor,
    ToolRegistry,
    ToolExecutor,
    ExecutionResult,
    ExecutionStatus
)

# Agent system
from .agents import (
    AgenticLoopProcessor,
    AgenticLoopResult
)

# Prompt system
from .prompts import StructuredPromptBuilder

# API models (for external use)
from .api.models import HealthResponse

# System metadata
__version__ = "0.9.0"
__author__ = "Augment"
__description__ = "Xi Intelligent Agent System - Core Engine"

# Main exports
__all__ = [
    # Core
    'XiCore',

    # Service layer
    'initialize_services',
    'cleanup_services',
    'get_container',
    'get_config_service',
    'get_database_service',
    'get_llm_service',
    'service_health_check',

    # Memory system
    'MemoryRecord',
    'MessageRole',
    'YU', 'XI', 'XI_SYSTEM', 'TOOL', 'USER', 'ASSISTANT', 'SYSTEM',
    'MongoProvider',
    'Retriever',
    'HistoryManager',
    'MessageFormatter',

    # Tool system
    'get_tool_registry',
    'get_tool_executor',
    'ToolRegistry',
    'ToolExecutor',
    'ExecutionResult',
    'ExecutionStatus',

    # Agent system
    'AgenticLoopProcessor',
    'AgenticLoopResult',

    # Prompt system
    'StructuredPromptBuilder',

    # API models
    'HealthResponse',

    # Metadata
    '__version__',
    '__author__',
    '__description__'
]
