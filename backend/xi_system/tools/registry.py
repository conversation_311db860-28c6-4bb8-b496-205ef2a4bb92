"""
V0.84 统一工具注册表

这是系统的唯一工具注册表，负责发现、注册和管理所有工具。
合并了原service/toolbox.py、tools/toolbox.py和tools/registry.py的功能。

核心职责：
- 自动发现tools/definition/下的所有工具
- 提取工具元数据和JSON Schema
- 提供统一的工具查询接口
- 生成LLM函数调用格式
- 工具权限和状态管理

设计原则：
- 单一职责：只负责工具的注册和发现
- 自动化：自动发现和注册工具定义
- 类型安全：完整的类型注解
- 可扩展：支持新工具类型的添加

合并来源：
- service/toolbox.py: 服务层工具管理功能
- tools/toolbox.py: 工具箱管理功能
- tools/registry.py: 工具注册表功能

使用方式：
registry = ToolRegistry()
registry.initialize()
tools = registry.get_available_tools()
"""

import logging
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
import importlib
import inspect

logger = logging.getLogger(__name__)


class ToolCategory(Enum):
    """工具分类枚举"""
    KNOWLEDGE = "knowledge"
    SYSTEM = "system"
    UTILITY = "utility"
    EXTERNAL = "external"


@dataclass
class ToolMetadata:
    """工具元数据"""
    name: str
    description: str
    category: str
    function: Callable
    parameters: Dict[str, Any]
    enabled: bool = True

    def to_llm_format(self) -> Dict[str, Any]:
        """转换为LLM函数调用格式"""
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.parameters
            }
        }


class ToolRegistry:
    """
    V0.84统一工具注册表

    系统的唯一工具注册中心，负责：
    1. 自动发现tools/definition/下的所有工具
    2. 注册工具元数据
    3. 提供工具查询接口
    4. 生成LLM格式的工具定义
    """

    def __init__(self):
        self._tools: Dict[str, ToolMetadata] = {}
        self._initialized = False
        logger.info("ToolRegistry initialized")

    def initialize(self) -> None:
        """初始化工具注册表，自动发现和注册所有工具"""
        if self._initialized:
            logger.warning("ToolRegistry already initialized")
            return

        try:
            self._discover_and_register_tools()
            self._initialized = True
            logger.info(f"ToolRegistry initialized with {len(self._tools)} tools")
        except Exception as e:
            logger.error(f"Failed to initialize ToolRegistry: {e}")
            raise

    def _discover_and_register_tools(self) -> None:
        """自动发现并注册tools/definition/下的所有工具"""
        try:
            # 导入知识工具
            from .definition.knowledge import KNOWLEDGE_TOOLS_METADATA
            for tool_meta in KNOWLEDGE_TOOLS_METADATA:
                self._register_tool_from_metadata(tool_meta)

            # 导入系统工具
            from .definition.system import SYSTEM_TOOLS_METADATA
            for tool_meta in SYSTEM_TOOLS_METADATA:
                self._register_tool_from_metadata(tool_meta)

            # V0.9: 导入网络搜索工具
            from .definition.web import WEB_TOOLS_METADATA
            for tool_meta in WEB_TOOLS_METADATA:
                self._register_tool_from_metadata(tool_meta)

            logger.info("Tool discovery completed")

        except Exception as e:
            logger.error(f"Error during tool discovery: {e}")
            raise

    def _register_tool_from_metadata(self, tool_meta: Dict[str, Any]) -> None:
        """从元数据字典注册工具"""
        try:
            metadata = ToolMetadata(
                name=tool_meta["name"],
                description=tool_meta["description"],
                category=tool_meta["category"],
                function=tool_meta["function"],
                parameters=tool_meta["parameters"]
            )
            self._tools[metadata.name] = metadata
            logger.debug(f"Registered tool: {metadata.name}")
        except Exception as e:
            logger.error(f"Failed to register tool from metadata: {e}")

    def get_tool(self, name: str) -> Optional[ToolMetadata]:
        """获取指定名称的工具"""
        return self._tools.get(name)

    def get_all_tools(self) -> Dict[str, ToolMetadata]:
        """获取所有工具"""
        return self._tools.copy()

    def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具的LLM格式列表"""
        if not self._initialized:
            self.initialize()

        available_tools = []
        for tool in self._tools.values():
            if tool.enabled:
                available_tools.append(tool.to_llm_format())

        logger.debug(f"Returning {len(available_tools)} available tools")
        return available_tools

    def get_tool_function(self, name: str) -> Optional[Callable]:
        """获取工具函数"""
        tool = self.get_tool(name)
        return tool.function if tool else None

    def list_tools_by_category(self, category: str) -> List[str]:
        """按分类列出工具"""
        return [
            name for name, tool in self._tools.items()
            if tool.category == category and tool.enabled
        ]

    def get_stats(self) -> Dict[str, Any]:
        """获取工具统计信息"""
        total_tools = len(self._tools)
        enabled_tools = len([t for t in self._tools.values() if t.enabled])

        categories = {}
        for tool in self._tools.values():
            categories[tool.category] = categories.get(tool.category, 0) + 1

        return {
            "total_tools": total_tools,
            "enabled_tools": enabled_tools,
            "disabled_tools": total_tools - enabled_tools,
            "categories": categories
        }


# 全局工具注册表实例
_global_registry: Optional[ToolRegistry] = None


def get_tool_registry() -> ToolRegistry:
    """获取全局工具注册表实例"""
    global _global_registry
    if _global_registry is None:
        _global_registry = ToolRegistry()
        _global_registry.initialize()
    return _global_registry
