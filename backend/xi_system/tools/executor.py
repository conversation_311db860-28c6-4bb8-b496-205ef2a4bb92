"""
V0.84 统一工具执行器

这是系统的唯一工具执行引擎，负责安全地执行所有工具。
合并了原agents/tool_executor.py和tools/executor.py的功能。

核心职责：
- 安全执行工具函数
- 错误处理和重试逻辑
- 超时和权限控制
- 执行监控和日志记录
- 统一的执行结果格式

设计原则：
- 单一职责：只负责工具的安全执行
- 安全第一：完整的错误处理和安全检查
- 可监控：详细的执行日志和统计
- 可扩展：支持新的执行策略

合并来源：
- agents/tool_executor.py: 工具调用业务逻辑
- tools/executor.py: 底层安全执行引擎

使用方式：
executor = ToolExecutor()
result = executor.execute_tool("read_note", filename="test.md")
"""

import logging
import time
import traceback
import json
from typing import Any, Dict, Optional, List, Union
from dataclasses import dataclass
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, TimeoutError

from .registry import get_tool_registry

logger = logging.getLogger(__name__)


class ExecutionStatus(Enum):
    """工具执行状态"""
    SUCCESS = "success"
    ERROR = "error"
    TIMEOUT = "timeout"
    TOOL_NOT_FOUND = "tool_not_found"
    INVALID_PARAMS = "invalid_params"


@dataclass
class ExecutionResult:
    """工具执行结果"""
    status: ExecutionStatus
    result: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    tool_name: str = ""

    @property
    def success(self) -> bool:
        """是否执行成功"""
        return self.status == ExecutionStatus.SUCCESS

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "status": self.status.value,
            "result": self.result,
            "error": self.error,
            "execution_time": self.execution_time,
            "tool_name": self.tool_name,
            "success": self.success
        }


class ToolExecutor:
    """
    V0.84统一工具执行器

    系统的唯一工具执行引擎，负责：
    1. 安全执行工具函数
    2. 参数验证和错误处理
    3. 执行监控和日志记录
    4. 超时控制和资源管理
    """

    def __init__(self, default_timeout: float = 30.0, config_service=None):
        # 如果提供了配置服务，使用配置中的超时时间
        if config_service:
            self.default_timeout = config_service.get_int('tool.execution_timeout', 60)
        else:
            self.default_timeout = default_timeout

        self.registry = get_tool_registry()
        self._executor = ThreadPoolExecutor(max_workers=4)
        logger.info(f"ToolExecutor initialized with timeout: {self.default_timeout}s")

    def execute_tool(
        self,
        tool_name: str,
        timeout: Optional[float] = None,
        **kwargs
    ) -> ExecutionResult:
        """
        执行指定的工具

        Args:
            tool_name: 工具名称
            timeout: 执行超时时间（秒）
            **kwargs: 工具参数

        Returns:
            ExecutionResult: 执行结果
        """
        start_time = time.time()
        timeout = timeout or self.default_timeout

        logger.info(f"Executing tool: {tool_name} with args: {kwargs}")

        try:
            # 获取工具函数
            tool_function = self.registry.get_tool_function(tool_name)
            if not tool_function:
                return ExecutionResult(
                    status=ExecutionStatus.TOOL_NOT_FOUND,
                    error=f"Tool '{tool_name}' not found",
                    execution_time=time.time() - start_time,
                    tool_name=tool_name
                )

            # 执行工具（带超时控制）
            try:
                future = self._executor.submit(tool_function, **kwargs)
                result = future.result(timeout=timeout)

                execution_time = time.time() - start_time
                logger.info(f"Tool {tool_name} executed successfully in {execution_time:.3f}s")

                return ExecutionResult(
                    status=ExecutionStatus.SUCCESS,
                    result=result,
                    execution_time=execution_time,
                    tool_name=tool_name
                )

            except TimeoutError:
                logger.error(f"Tool {tool_name} execution timeout after {timeout}s")
                return ExecutionResult(
                    status=ExecutionStatus.TIMEOUT,
                    error=f"Tool execution timeout after {timeout} seconds",
                    execution_time=timeout,
                    tool_name=tool_name
                )

            except TypeError as e:
                logger.error(f"Invalid parameters for tool {tool_name}: {e}")
                return ExecutionResult(
                    status=ExecutionStatus.INVALID_PARAMS,
                    error=f"Invalid parameters: {str(e)}",
                    execution_time=time.time() - start_time,
                    tool_name=tool_name
                )

        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"Tool execution failed: {str(e)}"
            logger.error(f"Error executing tool {tool_name}: {e}")
            logger.debug(f"Traceback: {traceback.format_exc()}")

            return ExecutionResult(
                status=ExecutionStatus.ERROR,
                error=error_msg,
                execution_time=execution_time,
                tool_name=tool_name
            )

    def execute_tool_safe(self, tool_name: str, **kwargs) -> str:
        """
        安全执行工具，返回字符串结果（用于LLM工具调用）

        Args:
            tool_name: 工具名称
            **kwargs: 工具参数

        Returns:
            str: 工具执行结果或错误信息
        """
        result = self.execute_tool(tool_name, **kwargs)

        if result.success:
            # 如果结果已经是字符串，直接返回
            if isinstance(result.result, str):
                return result.result
            # 否则转换为JSON字符串
            try:
                return json.dumps(result.result, ensure_ascii=False)
            except (TypeError, ValueError):
                return str(result.result)
        else:
            return f"工具执行失败: {result.error}"

    def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表（LLM格式）"""
        return self.registry.get_available_tools()

    def get_tool_stats(self) -> Dict[str, Any]:
        """获取工具统计信息"""
        return self.registry.get_stats()

    def cleanup(self):
        """清理资源"""
        self._executor.shutdown(wait=True)
        logger.info("ToolExecutor cleaned up")


# 全局工具执行器实例
_global_executor: Optional[ToolExecutor] = None


def get_tool_executor(config_service=None) -> ToolExecutor:
    """获取全局工具执行器实例"""
    global _global_executor
    if _global_executor is None:
        _global_executor = ToolExecutor(config_service=config_service)
    return _global_executor