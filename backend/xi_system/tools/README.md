# Xi 工具系统 (Xi Tool System)

## 概述 (Overview)

Xi 工具系统是曦智能体的"能力扩展引擎"，负责为Xi提供与外部世界交互的各种工具能力。该系统采用统一的三层架构设计，通过自动化的工具发现、注册和安全执行机制，为Xi提供了丰富的功能扩展能力，包括知识库访问、网络搜索、系统信息获取等核心工具。

### 核心理念

- **三层架构**: 清晰分离工具定义、注册管理和安全执行三个层次
- **自动发现**: 通过约定优于配置的方式自动发现和注册工具
- **安全执行**: 提供完整的错误处理、超时控制和权限管理
- **统一接口**: 为所有工具提供一致的调用接口和返回格式
- **可扩展性**: 支持快速添加新工具类型和功能

### 在整体架构中的位置

```
Xi 系统架构层次:
├── core/           # 轻量级业务流程编排器
├── service/        # 基础设施服务层
├── agents/         # AI交互处理器
├── memory/         # 记忆管理系统
├── tools/          # 工具系统 ← 本模块
│   ├── definition/ # 工具定义层
│   ├── registry    # 工具注册层
│   └── executor    # 工具执行层
├── prompts/        # 提示词系统
├── tasks/          # 后台认知任务系统
└── api/            # API接口层
```

### 设计价值

工具系统作为Xi的"手和眼"，将抽象的AI能力转化为具体的行动能力。通过标准化的工具接口和安全的执行环境，Xi能够安全、高效地与知识库、搜索引擎、文件系统等外部资源进行交互，大大扩展了其实用价值和应用场景。

## 系统架构 (Architecture)

### 核心组件

#### 1. Definition Layer (工具定义层)
```python
# tools/definition/ 目录结构
├── __init__.py          # 工具导出
├── knowledge.py         # 知识库工具
├── web.py              # 网络搜索工具
└── system.py           # 系统信息工具

# 工具函数规范
def tool_function(param1: str, param2: int = 10) -> str:
    """工具功能描述
    
    Args:
        param1: 参数1描述
        param2: 参数2描述，默认值10
        
    Returns:
        返回值描述
        
    Raises:
        ValueError: 参数错误时抛出
    """
```

#### 2. Registry Layer (工具注册层)
```python
class ToolRegistry:
    """统一工具注册表"""
    
    def initialize(self) -> None
    def get_tool(self, name: str) -> Optional[ToolMetadata]
    def get_available_tools(self) -> List[Dict[str, Any]]
    def get_tool_function(self, name: str) -> Optional[Callable]
```

#### 3. Executor Layer (工具执行层)
```python
class ToolExecutor:
    """统一工具执行器"""
    
    def execute_tool(self, tool_name: str, **kwargs) -> ExecutionResult
    def execute_tool_safe(self, tool_name: str, **kwargs) -> str
    def get_available_tools(self) -> List[Dict[str, Any]]
```

### 类层次结构

```
Tool System Architecture
├── Definition Layer (工具定义层)
│   ├── knowledge.py
│   │   ├── read_note()           # 读取笔记工具
│   │   └── write_note()          # 写入笔记工具
│   ├── web.py
│   │   └── web_search()          # 网络搜索工具
│   └── system.py                 # 系统工具(预留)
│
├── Registry Layer (工具注册层)
│   ├── ToolMetadata              # 工具元数据
│   ├── ToolCategory              # 工具分类枚举
│   └── ToolRegistry              # 工具注册表
│       ├── initialize()          # 初始化注册表
│       ├── _discover_and_register_tools()  # 自动发现工具
│       └── get_available_tools() # 获取可用工具
│
└── Executor Layer (工具执行层)
    ├── ExecutionResult           # 执行结果
    ├── ExecutionStatus           # 执行状态枚举
    └── ToolExecutor              # 工具执行器
        ├── execute_tool()        # 执行工具
        ├── execute_tool_safe()   # 安全执行
        └── cleanup()             # 资源清理
```

### 设计模式

1. **三层架构模式**: 分离关注点，定义/注册/执行各司其职
2. **注册表模式**: 统一管理所有工具的元数据和实例
3. **工厂模式**: ToolRegistry作为工具实例的工厂
4. **装饰器模式**: 为工具执行添加安全、监控等横切关注点
5. **策略模式**: 不同类型的工具使用不同的执行策略

### 数据流和控制流

```
工具调用请求 → ToolExecutor → ToolRegistry → 工具函数 → 
执行结果 → 错误处理 → 格式化输出 → 返回结果
```

## 使用示例 (Usage Examples)

### 基本工具使用

```python
from xi_system.tools import get_tool_executor

# 1. 获取工具执行器
executor = get_tool_executor()

# 2. 执行知识库工具
result = executor.execute_tool(
    "read_note",
    filename="ai-consciousness-discussion.md"
)

if result.success:
    print(f"笔记内容: {result.result}")
else:
    print(f"执行失败: {result.error}")

# 3. 执行网络搜索工具
search_result = executor.execute_tool(
    "web_search",
    query="人工智能最新发展",
    num_results=5
)

print(f"搜索结果: {search_result.result}")
```

### 安全工具执行

```python
# 使用安全执行方法（返回字符串结果）
def safe_tool_usage():
    """安全工具使用示例"""
    
    executor = get_tool_executor()
    
    # 安全执行，自动处理错误
    note_content = executor.execute_tool_safe(
        "read_note",
        filename="important-notes.md"
    )
    
    search_results = executor.execute_tool_safe(
        "web_search",
        query="量子计算突破",
        num_results=3
    )
    
    return {
        "note": note_content,
        "search": search_results
    }

results = safe_tool_usage()
print(results)
```

### 工具注册表查询

```python
from xi_system.tools import get_tool_registry

def explore_available_tools():
    """探索可用工具"""
    
    registry = get_tool_registry()
    
    # 获取所有工具
    all_tools = registry.get_all_tools()
    print(f"总共有 {len(all_tools)} 个工具")
    
    # 按分类查看工具
    knowledge_tools = registry.list_tools_by_category("knowledge")
    print(f"知识库工具: {knowledge_tools}")
    
    web_tools = registry.list_tools_by_category("external")
    print(f"外部工具: {web_tools}")
    
    # 获取工具统计信息
    stats = registry.get_stats()
    print(f"工具统计: {stats}")
    
    # 获取LLM格式的工具列表
    llm_tools = registry.get_available_tools()
    for tool in llm_tools:
        print(f"工具: {tool['function']['name']}")
        print(f"描述: {tool['function']['description']}")

explore_available_tools()
```

### 集成到对话流程

```python
from xi_system.tools import get_tool_executor
from xi_system.agents import AgenticLoopProcessor

def integrate_tools_with_conversation():
    """将工具集成到对话流程"""
    
    # 1. 获取工具执行器
    tool_executor = get_tool_executor()
    
    # 2. 创建代理循环处理器
    agentic_processor = AgenticLoopProcessor(
        llm_client=llm_client,
        toolbox=tool_executor,
        max_iterations=5
    )
    
    # 3. 处理包含工具调用的对话
    messages = [
        {"role": "system", "content": "你是Xi，可以使用工具帮助用户"},
        {"role": "user", "content": "帮我搜索一下最新的AI新闻"}
    ]
    
    # 4. 执行代理循环
    for chunk in agentic_processor.process_agentic_loop(messages, model_config):
        print(chunk, end='', flush=True)

# 使用示例
# integrate_tools_with_conversation()
```

### 批量工具执行

```python
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

def batch_tool_execution():
    """批量执行工具示例"""
    
    executor = get_tool_executor()
    
    # 定义要执行的工具任务
    tasks = [
        ("read_note", {"filename": "note1.md"}),
        ("read_note", {"filename": "note2.md"}),
        ("web_search", {"query": "AI news", "num_results": 3}),
        ("web_search", {"query": "tech trends", "num_results": 3})
    ]
    
    results = []
    
    # 使用线程池并行执行
    with ThreadPoolExecutor(max_workers=4) as thread_executor:
        # 提交所有任务
        future_to_task = {
            thread_executor.submit(executor.execute_tool, tool_name, **kwargs): (tool_name, kwargs)
            for tool_name, kwargs in tasks
        }
        
        # 收集结果
        for future in as_completed(future_to_task):
            tool_name, kwargs = future_to_task[future]
            try:
                result = future.result()
                results.append({
                    "tool": tool_name,
                    "params": kwargs,
                    "success": result.success,
                    "result": result.result if result.success else result.error
                })
            except Exception as e:
                results.append({
                    "tool": tool_name,
                    "params": kwargs,
                    "success": False,
                    "result": f"Exception: {str(e)}"
                })
    
    return results

# 执行批量任务
batch_results = batch_tool_execution()
for result in batch_results:
    print(f"工具 {result['tool']}: {'成功' if result['success'] else '失败'}")
```

### 自定义工具开发

```python
# 在 tools/definition/custom.py 中定义新工具
def calculate_fibonacci(n: int) -> str:
    """计算斐波那契数列
    
    Args:
        n: 要计算的项数
        
    Returns:
        斐波那契数列结果
        
    Raises:
        ValueError: 当n小于0时
    """
    if n < 0:
        raise ValueError("n must be non-negative")
    
    if n <= 1:
        return str(n)
    
    a, b = 0, 1
    for _ in range(2, n + 1):
        a, b = b, a + b
    
    return str(b)

def get_system_info() -> str:
    """获取系统信息
    
    Returns:
        系统信息字符串
    """
    import platform
    import psutil
    
    info = {
        "platform": platform.system(),
        "python_version": platform.python_version(),
        "cpu_count": psutil.cpu_count(),
        "memory_total": f"{psutil.virtual_memory().total / 1024**3:.1f} GB"
    }
    
    return f"系统: {info['platform']}, Python: {info['python_version']}, CPU: {info['cpu_count']}核, 内存: {info['memory_total']}"

# 定义工具元数据
CUSTOM_TOOLS_METADATA = [
    {
        "name": "calculate_fibonacci",
        "description": "计算斐波那契数列的第n项",
        "category": "utility",
        "function": calculate_fibonacci,
        "parameters": {
            "type": "object",
            "properties": {
                "n": {
                    "type": "integer",
                    "description": "要计算的项数",
                    "minimum": 0
                }
            },
            "required": ["n"]
        }
    },
    {
        "name": "get_system_info",
        "description": "获取当前系统的基本信息",
        "category": "system",
        "function": get_system_info,
        "parameters": {
            "type": "object",
            "properties": {}
        }
    }
]

# 在 tools/definition/__init__.py 中导出
from .custom import calculate_fibonacci, get_system_info, CUSTOM_TOOLS_METADATA

__all__ = [
    'read_note',
    'write_note',
    'web_search',
    'calculate_fibonacci',
    'get_system_info'
]

## API 参考 (API Reference)

### ToolExecutor 类

#### 构造函数
```python
def __init__(self, default_timeout: float = 30.0)
    """初始化工具执行器

    Args:
        default_timeout: 默认工具执行超时时间（秒）
    """
```

#### 核心方法

##### execute_tool()
```python
def execute_tool(
    self,
    tool_name: str,
    timeout: Optional[float] = None,
    **kwargs
) -> ExecutionResult
    """执行指定的工具

    Args:
        tool_name: 工具名称
        timeout: 执行超时时间（秒），None使用默认值
        **kwargs: 工具参数

    Returns:
        ExecutionResult: 详细的执行结果对象

    Raises:
        None: 所有异常都被捕获并包装在ExecutionResult中
    """
```

##### execute_tool_safe()
```python
def execute_tool_safe(self, tool_name: str, **kwargs) -> str
    """安全执行工具，返回字符串结果

    Args:
        tool_name: 工具名称
        **kwargs: 工具参数

    Returns:
        str: 工具执行结果或错误信息
    """
```

##### get_available_tools()
```python
def get_available_tools(self) -> List[Dict[str, Any]]
    """获取可用工具列表（LLM格式）

    Returns:
        List[Dict]: LLM函数调用格式的工具列表
    """
```

##### cleanup()
```python
def cleanup(self)
    """清理执行器资源"""
```

### ToolRegistry 类

#### 核心方法

##### initialize()
```python
def initialize(self) -> None
    """初始化工具注册表，自动发现和注册所有工具

    Raises:
        Exception: 当工具发现或注册失败时
    """
```

##### get_tool()
```python
def get_tool(self, name: str) -> Optional[ToolMetadata]
    """获取指定名称的工具元数据

    Args:
        name: 工具名称

    Returns:
        Optional[ToolMetadata]: 工具元数据或None
    """
```

##### get_tool_function()
```python
def get_tool_function(self, name: str) -> Optional[Callable]
    """获取工具函数

    Args:
        name: 工具名称

    Returns:
        Optional[Callable]: 工具函数或None
    """
```

##### list_tools_by_category()
```python
def list_tools_by_category(self, category: str) -> List[str]
    """按分类列出工具

    Args:
        category: 工具分类

    Returns:
        List[str]: 该分类下的工具名称列表
    """
```

##### get_stats()
```python
def get_stats(self) -> Dict[str, Any]
    """获取工具统计信息

    Returns:
        Dict: 包含工具数量、分类等统计信息
    """
```

### 数据类型定义

#### ExecutionResult (执行结果)
```python
@dataclass
class ExecutionResult:
    status: ExecutionStatus        # 执行状态
    result: Any = None            # 执行结果
    error: Optional[str] = None   # 错误信息
    execution_time: float = 0.0   # 执行时间
    tool_name: str = ""          # 工具名称

    @property
    def success(self) -> bool     # 是否执行成功

    def to_dict(self) -> Dict[str, Any]  # 转换为字典格式
```

#### ExecutionStatus (执行状态枚举)
```python
class ExecutionStatus(Enum):
    SUCCESS = "success"           # 执行成功
    ERROR = "error"              # 执行错误
    TIMEOUT = "timeout"          # 执行超时
    TOOL_NOT_FOUND = "tool_not_found"  # 工具未找到
    INVALID_PARAMS = "invalid_params"  # 参数无效
```

#### ToolMetadata (工具元数据)
```python
@dataclass
class ToolMetadata:
    name: str                    # 工具名称
    description: str             # 工具描述
    category: str               # 工具分类
    function: Callable          # 工具函数
    parameters: Dict[str, Any]  # 参数定义
    enabled: bool = True        # 是否启用

    def to_llm_format(self) -> Dict[str, Any]  # 转换为LLM格式
```

#### ToolCategory (工具分类枚举)
```python
class ToolCategory(Enum):
    KNOWLEDGE = "knowledge"      # 知识库工具
    SYSTEM = "system"           # 系统工具
    UTILITY = "utility"         # 实用工具
    EXTERNAL = "external"       # 外部工具
```

### 工具定义规范

#### 标准工具函数格式
```python
def tool_function(param1: str, param2: int = 10) -> str:
    """工具功能描述

    Args:
        param1: 参数1描述
        param2: 参数2描述，默认值10

    Returns:
        返回值描述

    Raises:
        ValueError: 参数错误时抛出
        RuntimeError: 运行时错误
    """
    # 参数验证
    if not param1:
        raise ValueError("param1 cannot be empty")

    # 工具逻辑实现
    result = f"处理 {param1} 使用参数 {param2}"

    return result
```

#### 工具元数据定义
```python
TOOL_METADATA = {
    "name": "tool_function",
    "description": "工具功能的简洁描述",
    "category": "utility",
    "function": tool_function,
    "parameters": {
        "type": "object",
        "properties": {
            "param1": {
                "type": "string",
                "description": "参数1的描述"
            },
            "param2": {
                "type": "integer",
                "description": "参数2的描述",
                "default": 10,
                "minimum": 1
            }
        },
        "required": ["param1"]
    }
}
```

## 系统集成 (Integration Guide)

### 与 AgenticLoopProcessor 的集成

工具系统通过代理循环处理器与LLM进行深度集成：

```python
from xi_system.agents import AgenticLoopProcessor
from xi_system.tools import get_tool_executor

def integrate_with_agentic_loop():
    """与代理循环处理器集成"""

    # 1. 获取工具执行器
    tool_executor = get_tool_executor()

    # 2. 创建代理循环处理器
    agentic_processor = AgenticLoopProcessor(
        llm_client=llm_client,
        toolbox=tool_executor,  # 传入工具执行器
        max_iterations=5
    )

    # 3. 处理工具调用
    messages = [
        {
            "role": "system",
            "content": "你可以使用工具来帮助用户。可用工具包括读取笔记、网络搜索等。"
        },
        {
            "role": "user",
            "content": "帮我搜索最新的人工智能发展趋势"
        }
    ]

    # 4. 执行代理循环，自动处理工具调用
    for chunk in agentic_processor.process_agentic_loop(messages, model_config):
        print(chunk, end='', flush=True)
```

### 与 Memory 系统的集成

工具系统与记忆系统集成，实现知识库访问：

```python
from xi_system.memory import MongoProvider
from xi_system.service import get_container

def integrate_with_memory_system():
    """与记忆系统集成示例"""

    # 1. 获取服务容器
    container = get_container()
    db_service = container.get_service('database')
    memory_provider = db_service.get_memory_provider()

    # 2. 知识库工具内部使用记忆系统
    def enhanced_read_note(filename: str) -> str:
        """增强的笔记读取工具"""
        try:
            # 使用记忆提供者读取笔记
            note_content = memory_provider.read_note(filename)
            return note_content
        except Exception as e:
            return f"读取笔记失败: {str(e)}"

    # 3. 注册增强工具
    from xi_system.tools.registry import get_tool_registry
    registry = get_tool_registry()

    # 动态注册新工具（实际应用中应在定义层完成）
    enhanced_metadata = {
        "name": "enhanced_read_note",
        "description": "增强的笔记读取功能",
        "category": "knowledge",
        "function": enhanced_read_note,
        "parameters": {
            "type": "object",
            "properties": {
                "filename": {"type": "string", "description": "笔记文件名"}
            },
            "required": ["filename"]
        }
    }

    registry._register_tool_from_metadata(enhanced_metadata)
```

### 与 Service Container 的集成

工具系统通过服务容器获取所需的基础设施服务：

```python
from xi_system.service import get_container

class ServiceAwareToolExecutor(ToolExecutor):
    """服务感知的工具执行器"""

    def __init__(self, default_timeout: float = 30.0):
        super().__init__(default_timeout)
        self.container = get_container()

    def execute_tool(self, tool_name: str, timeout=None, **kwargs):
        """重写执行方法，注入服务依赖"""

        # 为工具注入服务依赖
        if tool_name in ["read_note", "write_note"]:
            db_service = self.container.get_service('database')
            kwargs['memory_provider'] = db_service.get_memory_provider()

        elif tool_name == "web_search":
            config_service = self.container.get_service('config')
            kwargs['search_api_key'] = config_service.get('tool.web_search.api_key')

        # 调用父类方法执行工具
        return super().execute_tool(tool_name, timeout, **kwargs)

# 使用服务感知执行器
service_aware_executor = ServiceAwareToolExecutor()
```

### 事件系统集成

```python
from typing import Callable, Dict, List

class EventDrivenToolExecutor(ToolExecutor):
    """事件驱动的工具执行器"""

    def __init__(self, default_timeout: float = 30.0):
        super().__init__(default_timeout)
        self.event_handlers: Dict[str, List[Callable]] = {}

    def register_event_handler(self, event_type: str, handler: Callable):
        """注册事件处理器"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)

    def emit_event(self, event_type: str, event_data: Dict):
        """触发事件"""
        handlers = self.event_handlers.get(event_type, [])
        for handler in handlers:
            try:
                handler(event_data)
            except Exception as e:
                logger.error(f"Event handler failed: {e}")

    def execute_tool(self, tool_name: str, timeout=None, **kwargs):
        """重写执行方法，添加事件触发"""

        # 触发工具执行前事件
        self.emit_event('tool_execution_started', {
            'tool_name': tool_name,
            'parameters': kwargs
        })

        # 执行工具
        result = super().execute_tool(tool_name, timeout, **kwargs)

        # 触发工具执行后事件
        self.emit_event('tool_execution_completed', {
            'tool_name': tool_name,
            'result': result,
            'success': result.success
        })

        return result

# 使用事件驱动执行器
def on_tool_execution_completed(event_data):
    """工具执行完成事件处理器"""
    tool_name = event_data['tool_name']
    success = event_data['success']
    logger.info(f"Tool {tool_name} execution {'succeeded' if success else 'failed'}")

event_executor = EventDrivenToolExecutor()
event_executor.register_event_handler('tool_execution_completed', on_tool_execution_completed)
```
```
