"""
V0.9 知识库工具定义

这个模块包含所有与知识库访问相关的工具函数。
V0.9新增了write_note工具，实现知识库的动态扩展能力。

包含的工具：
- read_note: 读取知识库笔记
- write_note: 写入新的知识库笔记 (V0.9新增)

设计原则：
- 安全的文件访问，防止路径遍历攻击
- 清晰的错误处理和日志记录
- 统一的返回格式
- 自动触发知识索引更新
"""

import os
import logging
import subprocess
from pathlib import Path
from typing import Optional

logger = logging.getLogger(__name__)


def read_note(filename: str) -> str:
    """
    根据提供的文件名，从知识库中读取并返回一篇笔记的完整内容。
    用于在需要深入了解某项记忆或知识时进行精确查阅。
    
    Args:
        filename: 笔记文件名（如 "memento-memory-truth-and-ai-existence.md"）
        
    Returns:
        笔记的完整内容，或错误信息
        
    Security:
        - 防止路径遍历攻击
        - 只允许访问 memory/notes/ 目录下的文件
        - 只允许 .md 文件
        
    Raises:
        ValueError: 当文件名不安全或格式不正确时
    """
    logger.info(f"Xi is reading note: {filename}")
    
    # 安全性检查：防止路径遍历攻击
    if ".." in filename or filename.startswith("/") or "\\" in filename:
        error_msg = f"错误：无效的文件名 '{filename}'。出于安全考虑，不允许包含路径遍历字符。"
        logger.warning(f"Path traversal attempt blocked: {filename}")
        return error_msg
    
    # 确保只访问 .md 文件
    if not filename.endswith(".md"):
        error_msg = f"错误：只能读取 Markdown 文件（.md），但提供的是 '{filename}'。"
        logger.warning(f"Non-markdown file access attempt: {filename}")
        return error_msg
    
    # 构建安全的文件路径
    # 使用绝对路径解析，确保在任何工作目录下都能正确找到文件
    current_file = Path(__file__).resolve()
    notes_dir = current_file.parent.parent.parent / "memory" / "notes"
    filepath = notes_dir / filename
    
    try:
        # 检查文件是否存在
        if not filepath.exists():
            error_msg = f"错误：找不到名为 '{filename}' 的笔记。请检查文件名是否正确。"
            logger.warning(f"Note not found: {filename}")
            return error_msg
        
        # 读取文件内容
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        logger.info(f"Successfully read note: {filename} ({len(content)} characters)")
        return content
        
    except PermissionError:
        error_msg = f"错误：没有权限读取文件 '{filename}'。"
        logger.error(f"Permission denied reading: {filename}")
        return error_msg
        
    except UnicodeDecodeError:
        error_msg = f"错误：文件 '{filename}' 编码格式不正确，无法读取。"
        logger.error(f"Encoding error reading: {filename}")
        return error_msg
        
    except Exception as e:
        error_msg = f"读取笔记时发生未知错误: {str(e)}"
        logger.error(f"Unexpected error reading {filename}: {e}")
        return error_msg


def write_note(filename: str, content: str) -> str:
    """
    V0.9 写入新的知识库笔记

    创建新的知识笔记并自动更新知识索引。这是V0.9"生长"能力的核心实现，
    让曦能够通过交互动态扩展自己的知识库。

    Args:
        filename: 笔记文件名（必须以.md结尾）
        content: 笔记内容（Markdown格式）

    Returns:
        操作结果信息

    Security:
        - 防止路径遍历攻击
        - 只允许在 memory/notes/ 目录下创建文件
        - 只允许 .md 文件
        - 自动触发知识索引更新
    """
    logger.info(f"Xi is writing note: {filename}")

    # 安全性检查：防止路径遍历攻击
    if ".." in filename or filename.startswith("/") or "\\" in filename:
        error_msg = f"错误：无效的文件名 '{filename}'。出于安全考虑，不允许包含路径遍历字符。"
        logger.warning(f"Path traversal attempt blocked: {filename}")
        return error_msg

    # 确保只创建 .md 文件
    if not filename.endswith(".md"):
        error_msg = f"错误：只能创建 Markdown 文件（.md），但提供的是 '{filename}'。"
        logger.warning(f"Non-markdown file creation attempt: {filename}")
        return error_msg

    # 构建安全的文件路径
    current_file = Path(__file__).resolve()
    notes_dir = current_file.parent.parent.parent / "memory" / "notes"
    filepath = notes_dir / filename

    try:
        # 检查文件是否已存在
        if filepath.exists():
            error_msg = f"错误：文件 '{filename}' 已存在。请使用不同的文件名或先删除现有文件。"
            logger.warning(f"Attempt to overwrite existing note: {filename}")
            return error_msg

        # 确保目录存在
        notes_dir.mkdir(parents=True, exist_ok=True)

        # 写入文件内容
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)

        logger.info(f"Successfully wrote note: {filename} ({len(content)} characters)")

        # 自动触发知识索引更新
        try:
            # 调用外部脚本更新知识索引
            script_path = current_file.parent.parent.parent.parent / "scripts" / "build_notes_index.py"
            if script_path.exists():
                result = subprocess.run(
                    ["python", str(script_path)],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                if result.returncode == 0:
                    logger.info("Knowledge index updated successfully")
                else:
                    logger.warning(f"Knowledge index update failed: {result.stderr}")
            else:
                logger.warning(f"Knowledge index script not found: {script_path}")
        except Exception as e:
            logger.warning(f"Failed to update knowledge index: {e}")

        return f"✅ 笔记 '{filename}' 创建成功！内容长度：{len(content)} 字符。知识索引已自动更新。"

    except PermissionError:
        error_msg = f"错误：没有权限在目录中创建文件 '{filename}'。"
        logger.error(f"Permission denied writing: {filename}")
        return error_msg

    except Exception as e:
        error_msg = f"写入笔记时发生未知错误: {str(e)}"
        logger.error(f"Unexpected error writing {filename}: {e}")
        return error_msg


# 工具元数据定义（供注册表使用）
KNOWLEDGE_TOOLS_METADATA = [
    {
        "name": "read_note",
        "description": "从知识库中读取一篇笔记的完整内容。当需要详细了解某个话题、回忆或知识点时使用。系统提示词中已包含所有笔记的摘要和文件名。",
        "parameters": {
            "type": "object",
            "properties": {
                "filename": {
                    "type": "string",
                    "description": "要读取的笔记文件名，例如 'memento-memory-truth-and-ai-existence.md'"
                }
            },
            "required": ["filename"]
        },
        "category": "knowledge",
        "function": read_note
    },
    {
        "name": "write_note",
        "description": "创建新的知识库笔记。当需要记录重要信息、学习心得、或创建新的知识条目时使用。会自动更新知识索引。",
        "parameters": {
            "type": "object",
            "properties": {
                "filename": {
                    "type": "string",
                    "description": "新笔记的文件名，必须以.md结尾，例如 'new-learning-about-ai.md'"
                },
                "content": {
                    "type": "string",
                    "description": "笔记的完整内容，使用Markdown格式"
                }
            },
            "required": ["filename", "content"]
        },
        "category": "knowledge",
        "function": write_note
    }
]
