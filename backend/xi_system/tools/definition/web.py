"""
V0.9 网络搜索工具定义

这个模块实现了V0.9"感知"能力的核心工具，让曦能够主动从外部世界获取实时信息。
通过Tavily AI搜索API，打破信息壁垒，实现真正的世界感知能力。

包含的工具：
- web_search: 网络搜索工具

设计哲学：
"感知"是智能体与世界交互的第一步。通过网络搜索，
曦不再局限于训练数据，而是能够获取最新、最准确的信息。

使用方式：
result = web_search("最新的AI发展趋势")
print(result)  # 返回格式化的搜索结果
"""

import os
import logging
from typing import Optional, Dict, Any, List

logger = logging.getLogger(__name__)

# 延迟导入tavily，避免在没有API key时出错
_tavily_client = None


def _get_tavily_client():
    """获取Tavily客户端实例（延迟初始化）"""
    global _tavily_client
    
    if _tavily_client is None:
        try:
            from tavily import TavilyClient
            
            # 从环境变量获取API key
            api_key = os.getenv('TAVILY_API_KEY')
            if not api_key:
                raise ValueError("TAVILY_API_KEY environment variable not set")
            
            _tavily_client = TavilyClient(api_key=api_key)
            logger.info("Tavily client initialized successfully")
            
        except ImportError:
            raise ImportError("tavily-python package not installed. Run: pip install tavily-python")
        except Exception as e:
            logger.error(f"Failed to initialize Tavily client: {e}")
            raise
    
    return _tavily_client


def web_search(query: str, max_results: int = 5) -> str:
    """
    V0.9 网络搜索工具
    
    使用Tavily AI搜索API获取最新的网络信息。这是曦"感知"能力的核心实现，
    让AI能够突破训练数据的限制，获取实时、准确的世界信息。
    
    Args:
        query: 搜索查询词
        max_results: 最大结果数量（默认5个）
        
    Returns:
        格式化的搜索结果（Markdown格式）
        
    Features:
        - 高质量搜索结果
        - 自动内容摘要
        - 来源链接追踪
        - 结果去重和排序
    """
    logger.info(f"Xi is searching the web: {query}")
    
    try:
        # 获取Tavily客户端
        tavily = _get_tavily_client()
        
        # 执行搜索
        response = tavily.search(
            query=query,
            search_depth="advanced",  # 使用高级搜索
            max_results=max_results,
            include_answer=True,      # 包含AI生成的答案
            include_raw_content=False # 不包含原始内容（节省token）
        )
        
        # 格式化搜索结果
        formatted_result = _format_search_results(response, query)
        
        logger.info(f"Web search completed: {len(response.get('results', []))} results found")
        return formatted_result
        
    except ValueError as e:
        error_msg = f"❌ 搜索配置错误: {str(e)}\n\n请确保已设置 TAVILY_API_KEY 环境变量。"
        logger.error(f"Web search configuration error: {e}")
        return error_msg
        
    except ImportError as e:
        error_msg = f"❌ 搜索功能不可用: {str(e)}\n\n请安装必要的依赖包。"
        logger.error(f"Web search import error: {e}")
        return error_msg
        
    except Exception as e:
        error_msg = f"❌ 网络搜索失败: {str(e)}\n\n请检查网络连接或稍后重试。"
        logger.error(f"Web search failed: {e}")
        return error_msg


def _format_search_results(response: Dict[str, Any], query: str) -> str:
    """
    格式化搜索结果为清晰的Markdown格式
    
    Args:
        response: Tavily API响应
        query: 原始查询
        
    Returns:
        格式化的Markdown文本
    """
    try:
        results = response.get('results', [])
        answer = response.get('answer', '')
        
        if not results and not answer:
            return f"🔍 **搜索查询**: {query}\n\n❌ 未找到相关结果，请尝试其他关键词。"
        
        # 构建格式化结果
        formatted_parts = []
        
        # 添加标题
        formatted_parts.append(f"🔍 **网络搜索结果**: {query}")
        formatted_parts.append("=" * 50)
        
        # 添加AI生成的答案摘要（如果有）
        if answer:
            formatted_parts.append("## 📋 智能摘要")
            formatted_parts.append(answer)
            formatted_parts.append("")
        
        # 添加搜索结果
        if results:
            formatted_parts.append("## 🔗 详细结果")
            
            for i, result in enumerate(results[:5], 1):  # 最多显示5个结果
                title = result.get('title', '无标题')
                url = result.get('url', '')
                content = result.get('content', '')
                
                # 清理和截断内容
                if content:
                    # 移除多余的空白字符
                    content = ' '.join(content.split())
                    # 截断过长的内容
                    if len(content) > 300:
                        content = content[:300] + "..."
                
                formatted_parts.append(f"### {i}. {title}")
                if content:
                    formatted_parts.append(content)
                if url:
                    formatted_parts.append(f"🔗 **来源**: {url}")
                formatted_parts.append("")  # 空行分隔
        
        # 添加搜索信息
        formatted_parts.append("---")
        formatted_parts.append(f"📊 **搜索统计**: 找到 {len(results)} 个结果")
        formatted_parts.append(f"🕒 **搜索时间**: {_get_current_time()}")
        
        return "\n".join(formatted_parts)
        
    except Exception as e:
        logger.error(f"Error formatting search results: {e}")
        return f"🔍 **搜索查询**: {query}\n\n❌ 结果格式化失败: {str(e)}"


def _get_current_time() -> str:
    """获取当前时间字符串"""
    from datetime import datetime
    return datetime.now().strftime('%Y-%m-%d %H:%M:%S')


# 工具元数据定义（供注册表使用）
WEB_TOOLS_METADATA = [
    {
        "name": "web_search",
        "description": "搜索互联网获取最新信息。当需要了解实时新闻、最新发展、当前事件或任何超出训练数据范围的信息时使用。",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "搜索查询词，例如 '2025年AI最新发展' 或 '今天的新闻'"
                },
                "max_results": {
                    "type": "integer",
                    "description": "最大结果数量，默认5个",
                    "default": 5,
                    "minimum": 1,
                    "maximum": 10
                }
            },
            "required": ["query"]
        },
        "category": "web",
        "function": web_search
    }
]
