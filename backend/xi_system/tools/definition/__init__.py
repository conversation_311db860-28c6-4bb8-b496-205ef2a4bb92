"""
V0.84 工具定义层

这个包包含所有具体的工具实现。每个文件包含一组相关的工具函数。
工具定义遵循统一的接口规范，便于工具注册表自动发现和注册。

设计原则：
- 每个工具都是纯函数，无副作用（除了预期的功能）
- 工具函数包含完整的类型注解和文档字符串
- 工具参数使用JSON Schema兼容的类型
- 错误处理统一，返回清晰的错误信息

包含的工具模块：
- knowledge.py: 知识库访问工具
- system.py: 系统信息工具
- web.py: 网络搜索工具

工具函数规范：
def tool_function(param1: str, param2: int = 10) -> str:
    '''
    工具功能描述
    
    Args:
        param1: 参数1描述
        param2: 参数2描述，默认值10
        
    Returns:
        返回值描述
        
    Raises:
        ValueError: 参数错误时抛出
    '''
    # 实现逻辑
    return result
"""

# 导出所有工具函数
from .knowledge import read_note, write_note
from .web import web_search
# system.py 当前为空，未来可扩展

__all__ = [
    'read_note',
    'write_note',
    'web_search'
]
