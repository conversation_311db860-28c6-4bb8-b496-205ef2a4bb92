"""
Tool System - Unified Three-Layer Architecture

Contains all tool-related functionality with a clean three-layer design:
- definition/: Tool implementation layer with concrete tool functions
- registry.py: Tool registry for discovery and management
- executor.py: Tool executor for safe execution with error handling

Key Features:
- Automatic tool discovery and registration
- Safe execution with timeout and error handling
- Unified interface for all tool operations
- Extensible architecture for new tools
- Comprehensive logging and monitoring

Architecture Layers:
1. Definition Layer: Pure functions implementing tool logic
2. Registry Layer: Tool metadata and discovery system
3. Execution Layer: Safe execution with monitoring

Available Tools:
- read_note/write_note: Knowledge base access
- web_search: Internet search capabilities

Usage:
    from xi_system.tools import get_tool_registry, get_tool_executor

    # Get tool components
    registry = get_tool_registry()
    executor = get_tool_executor()

    # Execute tools
    result = executor.execute_tool("read_note", filename="test.md")
    tools = registry.get_available_tools()
"""

from .registry import get_tool_registry, ToolRegistry, ToolMetadata
from .executor import get_tool_executor, ToolExecutor, ExecutionResult, ExecutionStatus

# Backward compatibility interface
def get_toolbox():
    """Backward compatibility: get tool executor (replaces original toolbox)"""
    return get_tool_executor()

__all__ = [
    # Main interfaces
    'get_tool_registry',
    'get_tool_executor',
    'get_toolbox',  # Backward compatibility

    # Core classes
    'ToolRegistry',
    'ToolExecutor',
    'ToolMetadata',

    # Execution results
    'ExecutionResult',
    'ExecutionStatus'
]