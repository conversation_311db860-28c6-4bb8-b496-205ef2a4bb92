# Xi 后台任务系统 (Xi Background Task System)

## 概述 (Overview)

Xi 后台任务系统是曦智能体的"后台大脑"，负责处理复杂的、长时间运行的认知过程，这些过程不会阻塞实时对话流程。该系统实现了曦的元认知能力，包括反思、学习、规划和系统维护等高级认知功能。

### 核心理念

- **异步认知处理**: 在不影响对话体验的情况下进行深度思考
- **事件驱动架构**: 基于系统事件自动触发认知任务
- **可扩展设计**: 支持添加新的认知任务类型
- **状态管理**: 完整的任务生命周期管理和进度跟踪

### 在整体架构中的位置

```
Xi 系统架构层次:
├── core/           # 轻量级业务流程编排器
├── service/        # 基础设施服务层
├── agents/         # AI交互处理器
├── memory/         # 记忆管理系统
├── tools/          # 工具系统
├── prompts/        # 提示词系统
└── tasks/          # 后台认知任务系统 ← 本模块
```

## 系统架构 (Architecture)

### 核心组件

#### 1. BaseTask (任务基类)
```python
class BaseTask(ABC):
    """所有认知任务的抽象基类"""
    
    # 核心属性
    task_id: str                    # 任务唯一标识
    status: TaskStatus              # 任务状态
    priority: TaskPriority          # 执行优先级
    progress: float                 # 执行进度 (0.0-1.0)
    
    # 生命周期方法
    async def execute(context) -> TaskResult    # 执行任务
    def get_description() -> str                # 获取任务描述
    def update_progress(progress, message)      # 更新进度
```

#### 2. TaskManager (任务管理器)
```python
class TaskManager:
    """中央任务调度和执行引擎"""
    
    # 核心功能
    def submit_task(task, context) -> str       # 提交任务
    def register_trigger(event_type, trigger)  # 注册触发器
    def handle_event(event_type, data)         # 处理系统事件
    async def start()                          # 启动任务管理器
    async def stop()                           # 停止任务管理器
```

#### 3. TaskTrigger (任务触发器)
```python
class TaskTrigger(ABC):
    """任务触发器基类"""
    
    def should_trigger(event_data) -> bool      # 判断是否触发
    def create_task_context(event_data) -> TaskContext  # 创建任务上下文
```

### 类层次结构

```
BaseTask (抽象基类)
├── ReflectionTask          # 反思任务
├── LearningTask           # 学习任务 (未来)
├── PlanningTask           # 规划任务 (未来)
└── MaintenanceTask        # 维护任务 (未来)

TaskTrigger (抽象基类)
├── ConversationCountTrigger    # 对话数量触发器
├── TimeBasedTrigger           # 时间间隔触发器
└── ManualTrigger              # 手动触发器

TaskManager (单例)
├── 任务队列管理
├── 并发执行控制
├── 事件处理系统
└── 生命周期管理
```

### 设计模式

1. **事件驱动模式**: 通过系统事件触发任务执行
2. **观察者模式**: 任务进度和状态变化通知
3. **策略模式**: 不同的触发策略和执行策略
4. **依赖注入**: 通过ServiceContainer管理依赖
5. **异步处理**: 基于asyncio的并发任务执行

## 使用示例 (Usage Examples)

### 基本使用流程

```python
from xi_system.tasks import TaskManager, ReflectionTask
from xi_system.tasks.reflection import ConversationCountTrigger

# 1. 初始化任务管理器
task_manager = TaskManager(max_concurrent_tasks=3)

# 2. 注册触发器
trigger = ConversationCountTrigger(message_threshold=20)
task_manager.register_trigger('conversation_stored', trigger)

# 3. 启动任务管理器
await task_manager.start()

# 4. 处理系统事件
event_data = {
    'session_id': 'session_123',
    'service_container': container,
    'memory_provider': memory_provider
}
task_manager.handle_event('conversation_stored', event_data)
```

### 创建自定义任务

```python
from xi_system.tasks.base import BaseTask, TaskResult, TaskContext

class CustomLearningTask(BaseTask):
    """自定义学习任务示例"""
    
    def __init__(self, learning_topic: str):
        super().__init__(
            task_id=f"learning_{learning_topic}_{int(time.time())}",
            priority=TaskPriority.NORMAL
        )
        self.learning_topic = learning_topic
    
    async def execute(self, context: TaskContext) -> TaskResult:
        """执行学习任务"""
        try:
            self.update_progress(0.1, "开始学习过程")
            
            # 获取服务
            container = context.system_data['service_container']
            llm_service = container.get_service('llm')
            
            self.update_progress(0.5, f"学习主题: {self.learning_topic}")
            
            # 执行学习逻辑
            # ... 学习实现 ...
            
            self.update_progress(1.0, "学习完成")
            
            return TaskResult(
                success=True,
                data={"topic": self.learning_topic, "insights": "..."}
            )
            
        except Exception as e:
            return TaskResult(success=False, error=str(e))
    
    def get_description(self) -> str:
        return f"学习任务: {self.learning_topic}"
```

### 创建自定义触发器

```python
from xi_system.tasks.base import TaskTrigger, TaskContext

class CustomTrigger(TaskTrigger):
    """自定义触发器示例"""
    
    def __init__(self, condition_threshold: int):
        self.threshold = condition_threshold
    
    def should_trigger(self, event_data: Dict[str, Any]) -> bool:
        """检查触发条件"""
        value = event_data.get('some_metric', 0)
        return value >= self.threshold
    
    def create_task_context(self, event_data: Dict[str, Any]) -> TaskContext:
        """创建任务上下文"""
        return TaskContext(
            task_id=f"custom_{int(time.time())}",
            session_id=event_data.get('session_id'),
            system_data=event_data
        )
```

## API 参考 (API Reference)

### BaseTask 类

#### 构造函数
```python
def __init__(self, 
             task_id: str,
             priority: TaskPriority = TaskPriority.NORMAL,
             timeout_seconds: int = 300)
```

#### 核心方法
```python
async def execute(self, context: TaskContext) -> TaskResult
    """执行任务的主要逻辑
    
    Args:
        context: 任务执行上下文，包含服务容器和相关数据
        
    Returns:
        TaskResult: 执行结果，包含成功状态、数据和错误信息
        
    Raises:
        Exception: 任务执行过程中的任何异常
    """

def update_progress(self, progress: float, message: Optional[str] = None)
    """更新任务执行进度
    
    Args:
        progress: 进度百分比 (0.0 到 1.0)
        message: 可选的进度描述信息
    """

def get_description(self) -> str
    """获取任务的人类可读描述
    
    Returns:
        str: 任务描述字符串
    """
```

#### 状态管理方法
```python
def mark_started(self)          # 标记任务开始
def mark_completed(self, result: TaskResult)  # 标记任务完成
def mark_failed(self, error: str)             # 标记任务失败
def get_execution_time(self) -> Optional[float]  # 获取执行时间
def to_dict(self) -> Dict[str, Any]           # 转换为字典格式
```

### TaskManager 类

#### 构造函数
```python
def __init__(self, max_concurrent_tasks: int = 3)
    """初始化任务管理器
    
    Args:
        max_concurrent_tasks: 最大并发任务数量
    """
```

#### 任务管理方法
```python
def submit_task(self, task: BaseTask, context: TaskContext) -> str
    """提交任务执行
    
    Args:
        task: 要执行的任务实例
        context: 任务执行上下文
        
    Returns:
        str: 任务ID
    """

def get_task(self, task_id: str) -> Optional[BaseTask]
    """根据ID获取任务
    
    Args:
        task_id: 任务ID
        
    Returns:
        Optional[BaseTask]: 任务实例或None
    """

def cancel_task(self, task_id: str) -> bool
    """取消任务
    
    Args:
        task_id: 要取消的任务ID
        
    Returns:
        bool: 是否成功取消
    """
```

#### 触发器管理
```python
def register_trigger(self, event_type: str, trigger: TaskTrigger)
    """注册事件触发器
    
    Args:
        event_type: 事件类型名称
        trigger: 触发器实例
    """

def handle_event(self, event_type: str, event_data: Dict[str, Any])
    """处理系统事件
    
    Args:
        event_type: 事件类型
        event_data: 事件数据
    """
```

#### 生命周期管理
```python
async def start(self)
    """启动任务管理器"""

async def stop(self)
    """停止任务管理器，等待活动任务完成"""
```

### 数据类型定义

#### TaskStatus (任务状态枚举)
```python
class TaskStatus(Enum):
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"        # 执行失败
    CANCELLED = "cancelled"  # 已取消
```

#### TaskPriority (任务优先级枚举)
```python
class TaskPriority(Enum):
    LOW = 1       # 低优先级
    NORMAL = 2    # 普通优先级
    HIGH = 3      # 高优先级
    URGENT = 4    # 紧急优先级
```

#### TaskResult (任务结果)
```python
@dataclass
class TaskResult:
    success: bool                           # 执行是否成功
    data: Optional[Dict[str, Any]] = None   # 结果数据
    error: Optional[str] = None             # 错误信息
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
```

#### TaskContext (任务上下文)
```python
@dataclass
class TaskContext:
    task_id: str                            # 任务ID
    session_id: Optional[str] = None        # 会话ID
    user_data: Dict[str, Any] = field(default_factory=dict)    # 用户数据
    system_data: Dict[str, Any] = field(default_factory=dict)  # 系统数据
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
```
