"""
Reflection Task Module - <PERSON>'s Introspective Cognitive Process

This module implements <PERSON>'s reflection capability as a background task.
Reflection is <PERSON>'s meta-cognitive process of analyzing conversation history
to generate insights about growth, learning patterns, and relationship dynamics.

Components:
- ReflectionTask: Main reflection execution logic
- ReflectionTrigger: Determines when reflection should occur
- ReflectionProcessor: Handles reflection result processing

Design Philosophy:
Reflection is <PERSON>'s "quiet contemplation" - a background process that doesn't
interrupt conversations but provides deep insights for continuous improvement.
"""

from .task import ReflectionTask
from .trigger import ReflectionTrigger, ConversationCountTrigger

__all__ = [
    'ReflectionTask',
    'ReflectionTrigger',
    'ConversationCountTrigger'
]
