"""
Reflection Task Triggers

This module implements triggers that determine when reflection tasks should be executed.
Triggers respond to system events and evaluate conditions to decide if reflection is needed.
"""

import logging
from typing import Dict, Any
from abc import ABC, abstractmethod

from ..base import TaskTrigger, TaskContext

logger = logging.getLogger(__name__)


class ReflectionTrigger(TaskTrigger):
    """Base class for reflection triggers"""
    
    @abstractmethod
    def create_reflection_task(self, event_data: Dict[str, Any]):
        """Create a reflection task based on event data"""
        pass


class ConversationCountTrigger(ReflectionTrigger):
    """
    Trigger reflection based on conversation count.

    Triggers reflection when a session reaches a certain number of
    unreflected conversations. Uses configuration service for thresholds.
    """

    def __init__(self, config_service=None):
        """
        Initialize conversation count trigger.

        Args:
            config_service: Configuration service instance (optional, will use defaults if None)
        """
        self.config_service = config_service

        # Get configuration values
        if config_service:
            self.message_threshold = config_service.get_reflection_message_threshold()
            self.min_interval_hours = config_service.get_reflection_min_interval_hours()
        else:
            # Fallback to defaults if no config service
            self.message_threshold = 20
            self.min_interval_hours = 1.0

        # Track last reflection times to avoid too frequent reflections
        self.last_reflection_times: Dict[str, float] = {}

        logger.info(f"ConversationCountTrigger initialized (threshold: {self.message_threshold}, interval: {self.min_interval_hours}h)")
    
    def should_trigger(self, event_data: Dict[str, Any]) -> bool:
        """
        Check if reflection should be triggered based on conversation count.
        
        Args:
            event_data: Event data containing session_id and memory_provider
            
        Returns:
            bool: True if reflection should be triggered
        """
        try:
            session_id = event_data.get('session_id')
            memory_provider = event_data.get('memory_provider')
            
            if not session_id or not memory_provider:
                return False
            
            # Check minimum interval
            import time
            current_time = time.time()
            last_reflection = self.last_reflection_times.get(session_id, 0)
            
            if current_time - last_reflection < self.min_interval_hours * 3600:
                logger.debug(f"Reflection interval not met for session {session_id}")
                return False
            
            # Count unreflected messages
            unreflected_count = memory_provider.count_records(
                collection_name="conversations",
                filter_dict={
                    "source_session_id": session_id,
                    "metadata.reflected": {"$ne": True}
                }
            )
            
            should_trigger = unreflected_count >= self.message_threshold
            
            if should_trigger:
                logger.info(f"Reflection triggered for session {session_id} ({unreflected_count} unreflected messages)")
                self.last_reflection_times[session_id] = current_time
            
            return should_trigger
            
        except Exception as e:
            logger.error(f"Error checking reflection trigger: {e}")
            return False
    
    def create_task_context(self, event_data: Dict[str, Any]) -> TaskContext:
        """
        Create task context for reflection.
        
        Args:
            event_data: Event data
            
        Returns:
            TaskContext: Task execution context
        """
        session_id = event_data.get('session_id')
        
        import time

        return TaskContext(
            task_id=f"reflection_{session_id}_{int(time.time())}",
            session_id=session_id,
            system_data={
                'service_container': event_data.get('service_container'),
                'memory_provider': event_data.get('memory_provider')
            }
        )
    
    def create_reflection_task(self, event_data: Dict[str, Any]):
        """Create a reflection task based on event data"""
        from .task import ReflectionTask

        session_id = event_data.get('session_id')

        return ReflectionTask(
            session_id=session_id,
            config_service=self.config_service
        )


class TimeBasedTrigger(ReflectionTrigger):
    """
    Trigger reflection based on time intervals.

    Triggers reflection at regular intervals using configuration service.
    """

    def __init__(self, config_service=None, interval_hours: float = None):
        """
        Initialize time-based trigger.

        Args:
            config_service: Configuration service instance
            interval_hours: Override interval hours (optional)
        """
        self.config_service = config_service

        # Get interval from config or use provided value or default
        if interval_hours is not None:
            self.interval_hours = interval_hours
        elif config_service:
            self.interval_hours = config_service.get_reflection_min_interval_hours()
        else:
            self.interval_hours = 24.0  # Default to 24 hours

        self.last_trigger_times: Dict[str, float] = {}

        logger.info(f"TimeBasedTrigger initialized (interval: {self.interval_hours}h)")
    
    def should_trigger(self, event_data: Dict[str, Any]) -> bool:
        """Check if enough time has passed for reflection"""
        try:
            session_id = event_data.get('session_id')
            if not session_id:
                return False
            
            import time
            current_time = time.time()
            last_trigger = self.last_trigger_times.get(session_id, 0)
            
            should_trigger = current_time - last_trigger >= self.interval_hours * 3600
            
            if should_trigger:
                self.last_trigger_times[session_id] = current_time
                logger.info(f"Time-based reflection triggered for session {session_id}")
            
            return should_trigger
            
        except Exception as e:
            logger.error(f"Error checking time-based trigger: {e}")
            return False
    
    def create_task_context(self, event_data: Dict[str, Any]) -> TaskContext:
        """Create task context for time-based reflection"""
        import time
        session_id = event_data.get('session_id')
        
        return TaskContext(
            task_id=f"reflection_time_{session_id}_{int(time.time())}",
            session_id=session_id,
            system_data={
                'service_container': event_data.get('service_container'),
                'memory_provider': event_data.get('memory_provider')
            }
        )
    
    def create_reflection_task(self, event_data: Dict[str, Any]):
        """Create a time-based reflection task"""
        from .task import ReflectionTask
        
        session_id = event_data.get('session_id')
        
        return ReflectionTask(
            session_id=session_id,
            config_service=self.config_service
        )


class ManualTrigger(ReflectionTrigger):
    """
    Manual trigger for reflection.
    
    Allows manual triggering of reflection through API or admin interface.
    """
    
    def should_trigger(self, event_data: Dict[str, Any]) -> bool:
        """Always trigger when manually requested"""
        return event_data.get('manual_trigger', False)
    
    def create_task_context(self, event_data: Dict[str, Any]) -> TaskContext:
        """Create task context for manual reflection"""
        import time
        session_id = event_data.get('session_id')
        
        return TaskContext(
            task_id=f"reflection_manual_{session_id}_{int(time.time())}",
            session_id=session_id,
            user_data=event_data.get('user_data', {}),
            system_data={
                'service_container': event_data.get('service_container'),
                'memory_provider': event_data.get('memory_provider')
            }
        )
    
    def create_reflection_task(self, event_data: Dict[str, Any]):
        """Create a manual reflection task"""
        from .task import ReflectionTask
        
        session_id = event_data.get('session_id')
        conversation_limit = event_data.get('conversation_limit', 20)
        
        return ReflectionTask(
            session_id=session_id,
            conversation_limit=conversation_limit
        )
