"""
Reflection Task Implementation

This module implements the core reflection task that performs <PERSON>'s meta-cognitive
analysis of conversation history using the xi_omega agent.
"""

import logging
import uuid
from typing import Dict, Any, List
from datetime import datetime, timezone

from ..base import BaseTask, TaskResult, TaskContext, TaskPriority
from ...agents.xi_omega_agent import OmegaAgent
from ...memory.models import MemoryRecord

logger = logging.getLogger(__name__)


class ReflectionTask(BaseTask):
    """
    Xi's reflection task - meta-cognitive analysis of conversation history.
    
    This task uses the xi_omega agent to analyze recent conversations and
    generate structured insights about growth, learning, and relationship dynamics.
    """
    
    def __init__(self,
                 session_id: str,
                 config_service=None,
                 conversation_limit: int = None,
                 task_id: str = None,
                 priority: TaskPriority = TaskPriority.NORMAL):
        """
        Initialize reflection task.

        Args:
            session_id: Session ID to reflect on
            config_service: Configuration service instance
            conversation_limit: Override conversation limit (optional)
            task_id: Optional custom task ID
            priority: Task priority
        """
        if not task_id:
            task_id = f"reflection_{session_id}_{int(datetime.now().timestamp())}"

        # Get timeout from config or use default
        timeout_seconds = 600  # Default 10 minutes
        if config_service:
            timeout_seconds = config_service.get_task_timeout_seconds()

        super().__init__(task_id, priority, timeout_seconds=timeout_seconds)

        self.session_id = session_id
        self.config_service = config_service

        # Get conversation limit from config or use provided value or default
        if conversation_limit is not None:
            self.conversation_limit = conversation_limit
        elif config_service:
            self.conversation_limit = config_service.get_reflection_conversation_limit()
        else:
            self.conversation_limit = 20  # Default

        logger.info(f"ReflectionTask created for session {session_id} (limit: {self.conversation_limit})")
    
    async def execute(self, context: TaskContext) -> TaskResult:
        """
        Execute the reflection task.
        
        Args:
            context: Task execution context containing services
            
        Returns:
            TaskResult: Reflection execution result
        """
        try:
            self.update_progress(0.1, "Starting reflection process")
            
            # Get required services from context
            container = context.system_data.get('service_container')
            if not container:
                raise ValueError("Service container not found in task context")
            
            database_service = container.get_service('database')
            llm_service = container.get_service('llm')
            
            self.update_progress(0.2, "Loading conversation history")
            
            # Load unreflected conversation history
            memory_provider = database_service.get_memory_provider()
            unreflected_messages = memory_provider.query_records(
                collection_name="conversations",
                filter_dict={
                    "source_session_id": self.session_id,
                    "metadata.reflected": {"$ne": True}
                },
                sort=[("timestamp", 1)],
                limit=self.conversation_limit
            )
            
            if not unreflected_messages:
                return TaskResult(
                    success=True,
                    data={"message": "No unreflected messages found"},
                    metadata={"session_id": self.session_id}
                )
            
            self.update_progress(0.4, f"Analyzing {len(unreflected_messages)} conversations")
            
            # Create omega agent and perform reflection
            omega_agent = OmegaAgent(llm_service)
            reflection_result = omega_agent.reflect(unreflected_messages)
            
            self.update_progress(0.7, "Processing reflection results")
            
            if not reflection_result.success:
                return TaskResult(
                    success=False,
                    error="Reflection analysis failed",
                    metadata={
                        "session_id": self.session_id,
                        "message_count": len(unreflected_messages)
                    }
                )
            
            # Store reflection result
            await self._store_reflection_result(
                reflection_result, 
                memory_provider, 
                unreflected_messages
            )
            
            self.update_progress(0.9, "Updating reflection templates")
            
            # Trigger template update
            await self._update_reflection_templates()
            
            self.update_progress(1.0, "Reflection completed successfully")
            
            return TaskResult(
                success=True,
                data={
                    "reflection_id": reflection_result.parsed_data.get('reflection_id') if reflection_result.parsed_data else None,
                    "message_count": len(unreflected_messages),
                    "insights_generated": bool(reflection_result.parsed_data)
                },
                metadata={
                    "session_id": self.session_id,
                    "reflection_timestamp": reflection_result.timestamp.isoformat()
                }
            )
            
        except Exception as e:
            logger.error(f"Reflection task failed: {e}")
            return TaskResult(
                success=False,
                error=str(e),
                metadata={"session_id": self.session_id}
            )
    
    async def _store_reflection_result(self, 
                                     reflection_result, 
                                     memory_provider, 
                                     unreflected_messages: List[MemoryRecord]):
        """Store reflection result and mark messages as reflected"""
        try:
            from ...memory.models import MemoryRecord, MessageRole
            
            # Create reflection record
            reflection_record = MemoryRecord(
                content=reflection_result.raw_response,
                role=MessageRole.XI,
                timestamp=reflection_result.timestamp,
                source_session_id=self.session_id,
                metadata={
                    'type': 'reflection',
                    'reflection_id': str(uuid.uuid4()),
                    'parsed_data': reflection_result.parsed_data,
                    'success': reflection_result.success,
                    'message_count': len(unreflected_messages)
                }
            )
            
            # Store reflection
            memory_provider.store("reflections", reflection_record)
            
            # Mark messages as reflected
            for message in unreflected_messages:
                if not message.metadata:
                    message.metadata = {}
                message.metadata['reflected'] = True
                message.metadata['reflection_id'] = reflection_record.metadata['reflection_id']
                memory_provider.update_record(message)
            
            logger.info(f"Stored reflection result for session {self.session_id}")
            
        except Exception as e:
            logger.error(f"Error storing reflection result: {e}")
            raise
    
    async def _update_reflection_templates(self):
        """Update reflection.md template file"""
        try:
            import subprocess
            import os
            import asyncio
            
            # Get script path - 从backend目录开始
            # 当前文件: backend/xi_system/tasks/reflection/task.py
            # 目标路径: backend/scripts/build_reflections.py
            current_file = os.path.abspath(__file__)
            backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_file))))
            script_path = os.path.join(backend_dir, "scripts", "build_reflections.py")
            
            if os.path.exists(script_path):
                # Run script asynchronously
                process = await asyncio.create_subprocess_exec(
                    "python", script_path,
                    cwd=current_dir,
                    stdout=asyncio.subprocess.DEVNULL,
                    stderr=asyncio.subprocess.DEVNULL
                )
                
                await process.wait()
                logger.info("Reflection template updated successfully")
            else:
                logger.warning(f"Reflection update script not found: {script_path}")
                
        except Exception as e:
            logger.error(f"Error updating reflection template: {e}")
            # Don't raise - template update failure shouldn't fail the whole task
    
    def get_description(self) -> str:
        """Get human-readable task description"""
        return f"Meta-cognitive reflection analysis for session {self.session_id}"
