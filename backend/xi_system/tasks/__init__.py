"""
Xi Task System - Background Cognitive Task Management

This module provides <PERSON>'s background task system for handling complex,
long-running cognitive processes that enhance <PERSON>'s capabilities without
blocking real-time conversations.

Key Components:
- TaskManager: Central task orchestration and execution
- BaseTask: Base class for all cognitive tasks
- ReflectionTask: Meta-cognitive analysis and introspection
- TaskTriggers: Event-based task activation

Design Philosophy:
The task system represents <PERSON>'s "background mind" - autonomous processes
that continuously improve <PERSON>'s understanding, memory, and capabilities
through reflection, learning, and maintenance activities.

Usage:
    from xi_system.tasks import TaskManager, ReflectionTask
    from xi_system.tasks.reflection import ConversationCountTrigger
    
    # Initialize task manager
    task_manager = TaskManager()
    
    # Register reflection trigger
    trigger = ConversationCountTrigger(message_threshold=20)
    task_manager.register_trigger('conversation_stored', trigger)
    
    # Start task processing
    await task_manager.start()
    
    # Handle events
    task_manager.handle_event('conversation_stored', {
        'session_id': 'session_123',
        'service_container': container
    })
"""

from .base import (
    BaseTask, 
    TaskStatus, 
    TaskPriority, 
    TaskResult, 
    TaskContext, 
    TaskTrigger
)
from .manager import TaskManager
from .reflection import (
    ReflectionTask,
    ReflectionTrigger,
    ConversationCountTrigger
)

__all__ = [
    # Base classes
    'BaseTask',
    'TaskStatus', 
    'TaskPriority',
    'TaskResult',
    'TaskContext',
    'TaskTrigger',
    
    # Task manager
    'TaskManager',
    
    # Reflection tasks
    'ReflectionTask',
    'ReflectionTrigger', 
    'ConversationCountTrigger'
]
