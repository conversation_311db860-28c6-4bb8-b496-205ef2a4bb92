"""
Xi Tasks - Base Task System

This module defines the base classes and interfaces for <PERSON>'s background cognitive tasks.
Tasks represent complex, asynchronous business processes that run independently of
the main conversation flow.

Design Principles:
- Asynchronous execution: Tasks don't block main conversation flow
- Event-driven: Tasks respond to system events and triggers
- Stateful: Tasks maintain their own execution state
- Extensible: Easy to add new types of cognitive tasks
- Observable: Tasks can notify other components of their progress

Task Types:
- ReflectionTask: Meta-cognitive analysis and introspection
- LearningTask: Knowledge acquisition and skill development
- PlanningTask: Goal setting and strategy formulation
- MaintenanceTask: System optimization and cleanup
"""

import logging
import asyncio
from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime, timezone
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """Task execution priority"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class TaskResult:
    """Task execution result"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


@dataclass
class TaskContext:
    """Task execution context"""
    task_id: str
    session_id: Optional[str] = None
    user_data: Dict[str, Any] = field(default_factory=dict)
    system_data: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


class BaseTask(ABC):
    """
    Base class for all Xi cognitive tasks.
    
    Tasks are autonomous background processes that handle complex,
    time-consuming operations without blocking the main conversation flow.
    """
    
    def __init__(self, 
                 task_id: str,
                 priority: TaskPriority = TaskPriority.NORMAL,
                 timeout_seconds: int = 300):
        """
        Initialize base task.
        
        Args:
            task_id: Unique task identifier
            priority: Task execution priority
            timeout_seconds: Maximum execution time
        """
        self.task_id = task_id
        self.priority = priority
        self.timeout_seconds = timeout_seconds
        
        self.status = TaskStatus.PENDING
        self.created_at = datetime.now(timezone.utc)
        self.started_at: Optional[datetime] = None
        self.completed_at: Optional[datetime] = None
        
        self.result: Optional[TaskResult] = None
        self.progress: float = 0.0
        self.error_message: Optional[str] = None
        
        # Event callbacks
        self.on_progress: Optional[Callable[[float], None]] = None
        self.on_completed: Optional[Callable[[TaskResult], None]] = None
        self.on_failed: Optional[Callable[[str], None]] = None
    
    @abstractmethod
    async def execute(self, context: TaskContext) -> TaskResult:
        """
        Execute the task.
        
        Args:
            context: Task execution context
            
        Returns:
            TaskResult: Execution result
        """
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """Get human-readable task description"""
        pass
    
    def update_progress(self, progress: float, message: Optional[str] = None):
        """
        Update task progress.
        
        Args:
            progress: Progress percentage (0.0 to 1.0)
            message: Optional progress message
        """
        self.progress = max(0.0, min(1.0, progress))
        
        if message:
            logger.info(f"Task {self.task_id} progress: {progress:.1%} - {message}")
        
        if self.on_progress:
            self.on_progress(self.progress)
    
    def mark_started(self):
        """Mark task as started"""
        self.status = TaskStatus.RUNNING
        self.started_at = datetime.now(timezone.utc)
        logger.info(f"Task {self.task_id} started")
    
    def mark_completed(self, result: TaskResult):
        """Mark task as completed"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.now(timezone.utc)
        self.result = result
        self.progress = 1.0
        
        logger.info(f"Task {self.task_id} completed successfully")
        
        if self.on_completed:
            self.on_completed(result)
    
    def mark_failed(self, error: str):
        """Mark task as failed"""
        self.status = TaskStatus.FAILED
        self.completed_at = datetime.now(timezone.utc)
        self.error_message = error
        
        logger.error(f"Task {self.task_id} failed: {error}")
        
        if self.on_failed:
            self.on_failed(error)
    
    def get_execution_time(self) -> Optional[float]:
        """Get task execution time in seconds"""
        if not self.started_at:
            return None
        
        end_time = self.completed_at or datetime.now(timezone.utc)
        return (end_time - self.started_at).total_seconds()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert task to dictionary representation"""
        return {
            "task_id": self.task_id,
            "type": self.__class__.__name__,
            "status": self.status.value,
            "priority": self.priority.value,
            "progress": self.progress,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "execution_time": self.get_execution_time(),
            "error_message": self.error_message,
            "description": self.get_description()
        }


class TaskTrigger(ABC):
    """
    Base class for task triggers.
    
    Triggers determine when tasks should be executed based on
    system events, conditions, or schedules.
    """
    
    @abstractmethod
    def should_trigger(self, event_data: Dict[str, Any]) -> bool:
        """
        Check if task should be triggered.
        
        Args:
            event_data: Event data to evaluate
            
        Returns:
            bool: True if task should be triggered
        """
        pass
    
    @abstractmethod
    def create_task_context(self, event_data: Dict[str, Any]) -> TaskContext:
        """
        Create task context from event data.
        
        Args:
            event_data: Event data
            
        Returns:
            TaskContext: Task execution context
        """
        pass
