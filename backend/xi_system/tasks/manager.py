"""
Xi Task Manager - Background Task Orchestration

This module provides the central task management system for <PERSON>'s cognitive tasks.
It handles task scheduling, execution, monitoring, and lifecycle management.

Key Features:
- Asynchronous task execution with proper isolation
- Priority-based task scheduling
- Task timeout and error handling
- Progress monitoring and event notifications
- WebSocket integration for real-time updates
- Graceful shutdown and cleanup

Design Philosophy:
The TaskManager serves as <PERSON>'s "background mind" - handling long-running
cognitive processes that enhance <PERSON>'s capabilities without blocking
real-time conversations.
"""

import logging
import asyncio
import uuid
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timezone
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

from .base import BaseTask, TaskStatus, TaskPriority, TaskContext, TaskResult, TaskTrigger

logger = logging.getLogger(__name__)


@dataclass
class TaskQueueItem:
    """Task queue item with priority ordering"""
    priority: int
    created_at: datetime
    task: BaseTask
    context: TaskContext
    
    def __lt__(self, other):
        # Higher priority first, then FIFO for same priority
        if self.priority != other.priority:
            return self.priority > other.priority
        return self.created_at < other.created_at


class TaskManager:
    """
    Central manager for <PERSON>'s background cognitive tasks.
    
    Handles task scheduling, execution, monitoring, and lifecycle management
    with support for WebSocket notifications and graceful shutdown.
    """
    
    def __init__(self, config_service=None, max_concurrent_tasks: int = None):
        """
        Initialize task manager.

        Args:
            config_service: Configuration service instance
            max_concurrent_tasks: Override max concurrent tasks (optional)
        """
        self.config_service = config_service

        # Get configuration values
        if max_concurrent_tasks is not None:
            self.max_concurrent_tasks = max_concurrent_tasks
        elif config_service:
            self.max_concurrent_tasks = config_service.get_task_max_concurrent()
        else:
            self.max_concurrent_tasks = 3  # Default

        # Get other configuration values
        if config_service:
            self.task_timeout_seconds = config_service.get_task_timeout_seconds()
            self.task_retry_attempts = config_service.get_int('task.retry_attempts', 2)
            self.task_queue_max_size = config_service.get_int('task.queue_max_size', 100)
        else:
            self.task_timeout_seconds = 300
            self.task_retry_attempts = 2
            self.task_queue_max_size = 100

        # Task storage
        self.active_tasks: Dict[str, BaseTask] = {}
        self.completed_tasks: Dict[str, BaseTask] = {}
        self.task_queue = asyncio.PriorityQueue(maxsize=self.task_queue_max_size)

        # Execution control
        self.executor = ThreadPoolExecutor(max_workers=self.max_concurrent_tasks)
        self.running = False
        self.shutdown_event = asyncio.Event()
        self._process_task = None  # Keep reference to processing task

        # Event callbacks
        self.on_task_started: Optional[Callable[[BaseTask], None]] = None
        self.on_task_completed: Optional[Callable[[BaseTask, TaskResult], None]] = None
        self.on_task_failed: Optional[Callable[[BaseTask, str], None]] = None
        self.on_task_progress: Optional[Callable[[BaseTask, float], None]] = None

        # Registered triggers
        self.triggers: Dict[str, List[TaskTrigger]] = {}

        logger.info(f"TaskManager initialized (max_concurrent: {self.max_concurrent_tasks}, timeout: {self.task_timeout_seconds}s)")
    
    def register_trigger(self, event_type: str, trigger: TaskTrigger):
        """
        Register a task trigger for specific event type.
        
        Args:
            event_type: Type of event to listen for
            trigger: Task trigger instance
        """
        if event_type not in self.triggers:
            self.triggers[event_type] = []
        
        self.triggers[event_type].append(trigger)
        logger.info(f"Registered trigger for event type: {event_type}")
    
    def submit_task(self, task: BaseTask, context: TaskContext) -> str:
        """
        Submit a task for execution.

        Args:
            task: Task to execute
            context: Task execution context

        Returns:
            str: Task ID
        """
        # Auto-start task manager if not running
        if not self.running:
            logger.info("Task manager not running, auto-starting...")
            import asyncio
            try:
                # Try to start in current event loop
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # Schedule start for later
                    asyncio.create_task(self.start())
                    logger.info("Task manager start scheduled")
                else:
                    # Start immediately
                    loop.run_until_complete(self.start())
                    logger.info("Task manager started immediately")
            except RuntimeError:
                # No event loop, create one
                asyncio.run(self.start())
                logger.info("Task manager started with new event loop")

        # Set up task callbacks
        task.on_progress = lambda progress: self._on_task_progress(task, progress)
        task.on_completed = lambda result: self._on_task_completed(task, result)
        task.on_failed = lambda error: self._on_task_failed(task, error)

        # Add to queue
        queue_item = TaskQueueItem(
            priority=task.priority.value,
            created_at=task.created_at,
            task=task,
            context=context
        )

        self.task_queue.put_nowait(queue_item)
        logger.info(f"📤 Task {task.task_id} submitted to queue (queue size: {self.task_queue.qsize()})")

        return task.task_id
    
    def get_task(self, task_id: str) -> Optional[BaseTask]:
        """Get task by ID"""
        return (self.active_tasks.get(task_id) or 
                self.completed_tasks.get(task_id))
    
    def get_active_tasks(self) -> List[BaseTask]:
        """Get all active tasks"""
        return list(self.active_tasks.values())
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task status information"""
        task = self.get_task(task_id)
        if not task:
            return None
        
        return task.to_dict()
    
    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a task.
        
        Args:
            task_id: Task ID to cancel
            
        Returns:
            bool: True if task was cancelled
        """
        task = self.active_tasks.get(task_id)
        if not task:
            return False
        
        task.status = TaskStatus.CANCELLED
        task.completed_at = datetime.now(timezone.utc)
        
        # Move to completed tasks
        self.completed_tasks[task_id] = task
        del self.active_tasks[task_id]
        
        logger.info(f"Task {task_id} cancelled")
        return True
    
    async def start(self):
        """Start the task manager"""
        if self.running:
            logger.warning("TaskManager already running")
            return
        
        self.running = True
        self.shutdown_event.clear()
        
        logger.info("TaskManager started")

        # Start task processing loop and keep reference
        self._process_task = asyncio.create_task(self._process_tasks())
        logger.info("Task processing loop started")
    
    async def stop(self):
        """Stop the task manager gracefully"""
        if not self.running:
            return
        
        logger.info("Stopping TaskManager...")
        self.running = False
        self.shutdown_event.set()

        # Cancel processing task
        if self._process_task and not self._process_task.done():
            self._process_task.cancel()
            try:
                await self._process_task
            except asyncio.CancelledError:
                pass
            logger.info("Task processing loop stopped")

        # Wait for active tasks to complete (with timeout)
        timeout = 30  # seconds
        start_time = datetime.now()

        while self.active_tasks and (datetime.now() - start_time).seconds < timeout:
            await asyncio.sleep(1)

        # Force shutdown executor
        self.executor.shutdown(wait=True)

        logger.info("TaskManager stopped")
    
    def handle_event(self, event_type: str, event_data: Dict[str, Any]):
        """
        Handle system event and trigger appropriate tasks.
        
        Args:
            event_type: Type of event
            event_data: Event data
        """
        if event_type not in self.triggers:
            return
        
        for trigger in self.triggers[event_type]:
            try:
                if trigger.should_trigger(event_data):
                    # Create task context
                    context = trigger.create_task_context(event_data)

                    # Create task using trigger's create_reflection_task method
                    if hasattr(trigger, 'create_reflection_task'):
                        task = trigger.create_reflection_task(event_data)

                        # Submit the task for execution
                        task_id = self.submit_task(task, context)
                        logger.info(f"Event {event_type} triggered task creation and submission: {task_id}")
                    else:
                        logger.warning(f"Trigger {type(trigger).__name__} does not implement create_reflection_task")

            except Exception as e:
                logger.error(f"Error processing trigger for event {event_type}: {e}")
    
    async def _process_tasks(self):
        """Main task processing loop"""
        logger.info("Task processing loop started")
        loop_iteration = 0

        while self.running:
            try:
                loop_iteration += 1

                # Log periodic status for debugging
                if loop_iteration % 100 == 0:
                    queue_size = self.task_queue.qsize()
                    active_count = len(self.active_tasks)
                    logger.debug(f"Task loop iteration {loop_iteration}: queue={queue_size}, active={active_count}")

                # Check if we can start more tasks
                if len(self.active_tasks) >= self.max_concurrent_tasks:
                    logger.debug(f"Max concurrent tasks reached ({self.max_concurrent_tasks}), waiting...")
                    await asyncio.sleep(0.1)
                    continue

                # Get next task from queue (non-blocking)
                try:
                    queue_item = self.task_queue.get_nowait()
                    logger.info(f"🎯 Retrieved task from queue: {queue_item.task.task_id}")
                except asyncio.QueueEmpty:
                    # Queue is empty, wait a bit
                    await asyncio.sleep(0.1)
                    continue
                except Exception as e:
                    logger.error(f"Error getting task from queue: {e}")
                    await asyncio.sleep(0.1)
                    continue

                # Start task execution
                logger.info(f"🚀 Starting task execution: {queue_item.task.task_id}")
                await self._execute_task(queue_item.task, queue_item.context)

            except Exception as e:
                logger.error(f"Error in task processing loop: {e}")
                import traceback
                traceback.print_exc()
                await asyncio.sleep(1)

        logger.info("Task processing loop ended")
    
    async def _execute_task(self, task: BaseTask, context: TaskContext):
        """Execute a single task"""
        try:
            # Add to active tasks
            self.active_tasks[task.task_id] = task
            task.mark_started()
            
            # Notify task started
            if self.on_task_started:
                self.on_task_started(task)
            
            # Execute task in thread pool
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                self._run_task_sync,
                task,
                context
            )
            
            # Handle result
            if result.success:
                task.mark_completed(result)
            else:
                task.mark_failed(result.error or "Unknown error")
            
        except Exception as e:
            logger.error(f"Error executing task {task.task_id}: {e}")
            task.mark_failed(str(e))
        
        finally:
            # Move to completed tasks
            if task.task_id in self.active_tasks:
                self.completed_tasks[task.task_id] = task
                del self.active_tasks[task.task_id]
    
    def _run_task_sync(self, task: BaseTask, context: TaskContext) -> TaskResult:
        """Run task synchronously (for thread pool execution)"""
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Execute task
            result = loop.run_until_complete(task.execute(context))
            
            return result
            
        except Exception as e:
            logger.error(f"Task {task.task_id} execution failed: {e}")
            return TaskResult(success=False, error=str(e))
        
        finally:
            loop.close()
    
    def _on_task_progress(self, task: BaseTask, progress: float):
        """Handle task progress update"""
        if self.on_task_progress:
            self.on_task_progress(task, progress)
    
    def _on_task_completed(self, task: BaseTask, result: TaskResult):
        """Handle task completion"""
        if self.on_task_completed:
            self.on_task_completed(task, result)
    
    def _on_task_failed(self, task: BaseTask, error: str):
        """Handle task failure"""
        if self.on_task_failed:
            self.on_task_failed(task, error)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get task manager statistics"""
        return {
            "active_tasks": len(self.active_tasks),
            "completed_tasks": len(self.completed_tasks),
            "queue_size": self.task_queue.qsize(),
            "max_concurrent": self.max_concurrent_tasks,
            "running": self.running
        }
