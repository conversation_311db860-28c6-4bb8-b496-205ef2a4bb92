# xi_system/memory/retrieval/retriever.py

"""
高级记忆检索器

实现ContextOS的高级记忆检索系统，遵循"领域模型优先"原则。
专门操作MemoryRecord领域模型，确保与存储实现完全隔离。

V0.9更新：
- 集成新的记忆信号计算系统
- 自动过滤已归档的记忆
- 使用统一的信号强度评分

检索器提供智能记忆检索，使用多维记忆信号，包括语义相似度、时间相关性和重要性评分。

设计原则：
- 领域模型优先，存储无关
- 多维记忆信号融合
- 智能相关性排序
- 可配置的检索策略
- 自动过滤归档记忆
"""

import logging
import time
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any


from ..providers import MemoryProvider
from ..models import MemoryRecord
from ..curation.evaluator import calculate_signal

logger = logging.getLogger(__name__)


class Retriever:
    """
    高级记忆检索系统，支持多维记忆信号
    
    实现智能记忆检索，专门操作MemoryRecord领域模型，
    遵循"领域模型优先"原则。提供语义相似度、时间相关性和重要性评分，
    同时与存储实现细节完全隔离。
    """
    
    def __init__(self, provider: MemoryProvider, embedding_service=None, config_service=None):
        """
        初始化检索器

        Args:
            provider: 记忆提供者实例（任何实现）
            embedding_service: 嵌入服务实例
            config_service: 配置服务实例（可选）
        """
        self.provider = provider
        self.embedding_service = embedding_service
        self.config_service = config_service

        logger.info("Memory retriever initialized with domain model interface")
    
    def _generate_query_embedding(self, query: str) -> List[float]:
        """生成查询文本的嵌入向量"""
        if not self.embedding_service:
            logger.error("No embedding service available")
            return []

        try:
            return self.embedding_service.generate_embedding(query)
        except Exception as e:
            logger.error(f"Failed to generate query embedding: {e}")
            return []
    
    def retrieve_memories(
        self,
        query: str,
        collection: str = "conversations",
        limit: int = 3,
        include_context: bool = True,
        session_id: Optional[str] = None,
        min_score: float = 0.1
    ) -> List[MemoryRecord]:
        """
        使用V0.9统一信号计算检索相关记忆

        结合语义相似度、时间相关性和重要性评分，
        自动过滤已归档的记忆，提供最具上下文相关性的记忆。

        Args:
            query: 查询文本
            collection: 集合名称
            limit: 返回记录的最大数量
            include_context: 是否包含上下文记忆
            session_id: 可选的会话ID过滤器
            min_score: 最小相关性分数阈值

        Returns:
            按相关性排序的MemoryRecord列表
        """
        start_time = time.time()
        logger.debug(f"Starting memory retrieval for query: '{query[:50]}...'")
        
        try:
            # 1. 生成查询嵌入
            query_embedding = self._generate_query_embedding(query)
            if not query_embedding:
                logger.warning("Failed to generate query embedding, falling back to recent memories")
                return self.provider.retrieve_recent(collection, limit, session_id)
            
            # 2. 执行向量搜索（自动过滤已归档记忆）
            vector_results = self.provider.vector_search(
                collection_name=collection,
                query_embedding=query_embedding,
                limit=limit * 2,  # 获取更多候选结果
                min_score=0.0,  # 在这里不过滤，后面统一处理
                session_id=session_id,
                exclude_archived=True  # V0.9: 自动排除已归档记忆
            )

            # 3. 使用V0.9统一信号计算重新排序
            scored_memories = []
            for memory in vector_results:
                # 获取向量搜索的相关性分数
                relevance_score = getattr(memory, '_search_score', 0.0)

                # 使用V0.9统一信号计算
                final_signal = calculate_signal(memory, relevance_score)

                if final_signal >= min_score:
                    scored_memories.append((memory, final_signal))
            
            # 4. 按分数排序
            scored_memories.sort(key=lambda x: x[1], reverse=True)
            
            # 5. 提取最终结果
            final_memories = [memory for memory, _ in scored_memories[:limit]]
            
            # 6. 如果需要上下文且结果不足，添加最近的记忆
            if include_context and len(final_memories) < limit:
                recent_memories = self.provider.retrieve_recent(
                    collection, 
                    limit - len(final_memories),
                    session_id
                )
                
                # 避免重复
                existing_ids = {m.id for m in final_memories if m.id}
                for memory in recent_memories:
                    if memory.id not in existing_ids:
                        final_memories.append(memory)
                        if len(final_memories) >= limit:
                            break
            
            duration = time.time() - start_time
            logger.debug(f"Memory retrieval completed in {duration:.3f}s, returned {len(final_memories)} memories")
            
            return final_memories
            
        except Exception as e:
            logger.error(f"Memory retrieval failed: {e}")
            # 回退到最近记忆
            logger.warning("Falling back to recent memories")
            return self.provider.retrieve_recent(collection, limit, session_id)
    
    def retrieve_by_session(
        self,
        session_id: str,
        collection: str = "conversations",
        limit: Optional[int] = None
    ) -> List[MemoryRecord]:
        """
        检索指定会话的所有记忆
        
        Args:
            session_id: 会话ID
            collection: 集合名称
            limit: 可选的记录数量限制
            
        Returns:
            会话记忆列表，按时间排序
        """
        try:
            return self.provider.retrieve_by_session(collection, session_id, limit)
        except Exception as e:
            logger.error(f"Session memory retrieval failed: {e}")
            return []
    
    def retrieve_recent(
        self,
        collection: str = "conversations",
        limit: int = 10,
        session_id: Optional[str] = None,
        hours_back: Optional[int] = None
    ) -> List[MemoryRecord]:
        """
        检索最近的记忆
        
        Args:
            collection: 集合名称
            limit: 返回记录的最大数量
            session_id: 可选的会话ID过滤器
            hours_back: 可选的时间范围（小时）
            
        Returns:
            最近的记忆列表，按时间倒序排列
        """
        try:
            memories = self.provider.retrieve_recent(collection, limit, session_id)
            
            # 如果指定了时间范围，进行过滤
            if hours_back:
                cutoff_time = datetime.utcnow() - timedelta(hours=hours_back)
                memories = [m for m in memories if m.timestamp >= cutoff_time]
            
            return memories
            
        except Exception as e:
            logger.error(f"Recent memory retrieval failed: {e}")
            return []
    
    def search_by_content(
        self,
        content_query: str,
        collection: str = "conversations",
        limit: int = 10,
        session_id: Optional[str] = None
    ) -> List[MemoryRecord]:
        """
        基于内容的文本搜索
        
        Args:
            content_query: 内容查询字符串
            collection: 集合名称
            limit: 返回记录的最大数量
            session_id: 可选的会话ID过滤器
            
        Returns:
            匹配的记忆列表
        """
        try:
            # 获取所有相关记忆，使用配置中的扩展倍数或默认值
            expansion_factor = 5  # 默认扩展倍数
            if self.config_service:
                expansion_factor = self.config_service.get_int('rag.search_expansion_factor', 5)

            all_memories = self.provider.retrieve_recent(collection, limit * expansion_factor, session_id)
            
            # 简单的文本匹配过滤
            content_query_lower = content_query.lower()
            matching_memories = []
            
            for memory in all_memories:
                if content_query_lower in memory.content.lower():
                    matching_memories.append(memory)
                    if len(matching_memories) >= limit:
                        break
            
            return matching_memories
            
        except Exception as e:
            logger.error(f"Content search failed: {e}")
            return []
    
    def get_memory_stats(self, collection: str = "conversations") -> Dict[str, Any]:
        """
        获取记忆统计信息
        
        Args:
            collection: 集合名称
            
        Returns:
            统计信息字典
        """
        try:
            total_count = self.provider.count(collection)
            recent_count = len(self.provider.retrieve_recent(collection, 100))
            
            # 计算时间范围
            recent_memories = self.provider.retrieve_recent(collection, 10)
            if recent_memories:
                latest_time = max(m.timestamp for m in recent_memories)
                oldest_time = min(m.timestamp for m in recent_memories)
                time_span = latest_time - oldest_time
            else:
                time_span = timedelta(0)
            
            return {
                "total_memories": total_count,
                "recent_memories": recent_count,
                "time_span_hours": time_span.total_seconds() / 3600,
                "collection": collection,
                "embedding_service_available": self.embedding_service is not None
            }
            
        except Exception as e:
            logger.error(f"Failed to get memory stats: {e}")
            return {
                "error": str(e),
                "collection": collection
            }
    
    # V0.9注意: 我们不删除记忆，只归档。使用Archiver进行归档管理。
