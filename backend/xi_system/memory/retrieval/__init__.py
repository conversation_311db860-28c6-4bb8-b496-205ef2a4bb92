# xi_system/memory/retrieval/__init__.py

"""
记忆检索统一导出

提供记忆检索相关功能的统一访问接口。
V0.9更新：简化了评分系统，使用统一的信号计算函数。

使用示例:
    from xi_system.memory.retrieval import Retriever
    from xi_system.memory.retrieval.scoring import calculate_signal

    # 创建检索器
    retriever = Retriever(provider)

    # 检索记忆
    memories = retriever.retrieve_memories("查询内容")

    # 计算记忆信号
    signal = calculate_signal(memory_record)
"""

from .retriever import Retriever
from ..curation.evaluator import calculate_signal, should_archive_memory, get_signal_breakdown

# 导出所有公共接口
__all__ = [
    # 检索器
    'Retriever',

    # 信号计算系统
    'calculate_signal',
    'should_archive_memory',
    'get_signal_breakdown'
]
