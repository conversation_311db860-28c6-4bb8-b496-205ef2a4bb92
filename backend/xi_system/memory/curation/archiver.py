"""
记忆归档器

这个模块实现了V0.9"新陈代谢"系统的记忆归档功能。
通过智能分析记忆信号强度，自动归档低价值的旧记忆，保持记忆系统的健康与高效。

核心功能：
- 自动识别低信号强度的记忆
- 批量归档过期记忆
- 保护重要记忆不被误归档
- 提供归档统计和报告

设计哲学：
"遗忘"是为了更好的"记忆"。通过归档低价值记忆，
我们让重要记忆得以凸显，模拟生物记忆的自然淘汰机制。

使用方式：
archiver = Archiver(memory_provider)
result = archiver.archive_low_signal_memories()
print(f"归档了 {result['archived_count']} 条记忆")
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from ..models import MemoryRecord
from ..providers.base import MemoryProvider
from .evaluator import calculate_signal, should_archive_memory, ARCHIVE_THRESHOLD

logger = logging.getLogger(__name__)


class ArchiveResult:
    """归档操作结果"""
    
    def __init__(self):
        self.archived_count = 0
        self.processed_count = 0
        self.error_count = 0
        self.start_time = datetime.now()
        self.end_time = None
        self.errors = []
    
    def finish(self):
        """完成归档操作"""
        self.end_time = datetime.now()
    
    @property
    def duration(self) -> timedelta:
        """归档操作耗时"""
        if self.end_time:
            return self.end_time - self.start_time
        return datetime.now() - self.start_time
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'archived_count': self.archived_count,
            'processed_count': self.processed_count,
            'error_count': self.error_count,
            'duration_seconds': self.duration.total_seconds(),
            'archive_rate': self.archived_count / max(1, self.processed_count),
            'errors': self.errors[:10]  # 只保留前10个错误
        }


class Archiver:
    """
    V0.9 记忆归档器
    
    负责识别和归档低信号强度的记忆，实现记忆系统的新陈代谢。
    """
    
    def __init__(self, memory_provider: MemoryProvider, archive_days_threshold: int = 30):
        """
        初始化归档器
        
        Args:
            memory_provider: 记忆存储提供者
            archive_days_threshold: 归档天数阈值，超过此天数的记忆才考虑归档
        """
        self.provider = memory_provider
        self.archive_days_threshold = archive_days_threshold
        logger.info(f"Archiver initialized with {archive_days_threshold} days threshold")
    
    def archive_low_signal_memories(self, batch_size: int = 100) -> ArchiveResult:
        """
        归档低信号强度的记忆
        
        Args:
            batch_size: 批处理大小
            
        Returns:
            ArchiveResult: 归档操作结果
        """
        result = ArchiveResult()
        
        try:
            logger.info("Starting memory archiving process...")
            
            # 计算时间阈值
            cutoff_date = datetime.now() - timedelta(days=self.archive_days_threshold)
            
            # 分批处理记忆
            offset = 0
            while True:
                # 获取候选记忆（未归档且超过时间阈值）
                candidates = self._get_archive_candidates(cutoff_date, batch_size, offset)
                
                if not candidates:
                    break  # 没有更多候选记忆
                
                # 处理当前批次
                batch_result = self._process_batch(candidates)
                result.archived_count += batch_result['archived']
                result.processed_count += batch_result['processed']
                result.error_count += batch_result['errors']
                result.errors.extend(batch_result['error_details'])
                
                logger.info(f"Processed batch: {batch_result['processed']} memories, "
                           f"archived: {batch_result['archived']}")
                
                offset += batch_size
                
                # 如果当前批次少于batch_size，说明已经处理完所有记忆
                if len(candidates) < batch_size:
                    break
            
            result.finish()
            
            logger.info(f"Memory archiving completed: {result.archived_count} memories archived "
                       f"out of {result.processed_count} processed in {result.duration.total_seconds():.2f}s")
            
            return result
            
        except Exception as e:
            result.finish()
            result.error_count += 1
            result.errors.append(f"Archive process failed: {str(e)}")
            logger.error(f"Memory archiving failed: {e}")
            return result
    
    def _get_archive_candidates(
        self, 
        cutoff_date: datetime, 
        limit: int, 
        offset: int
    ) -> List[MemoryRecord]:
        """
        获取归档候选记忆
        
        Args:
            cutoff_date: 时间截止日期
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[MemoryRecord]: 候选记忆列表
        """
        try:
            # 查询条件：未归档且超过时间阈值
            query_filter = {
                "timestamp": {"$lt": cutoff_date},
                "metadata.is_archived": {"$ne": True}
            }
            
            # 按时间排序，优先处理最旧的记忆
            sort_criteria = [("timestamp", 1)]
            
            # 调用provider的查询方法
            candidates = self.provider.query_records(
                filter_dict=query_filter,
                sort=sort_criteria,
                limit=limit,
                skip=offset
            )
            
            logger.debug(f"Found {len(candidates)} archive candidates")
            return candidates
            
        except Exception as e:
            logger.error(f"Error getting archive candidates: {e}")
            return []
    
    def _process_batch(self, memories: List[MemoryRecord]) -> Dict[str, Any]:
        """
        处理一批记忆
        
        Args:
            memories: 记忆列表
            
        Returns:
            Dict: 处理结果统计
        """
        result = {
            'processed': 0,
            'archived': 0,
            'errors': 0,
            'error_details': []
        }
        
        for memory in memories:
            try:
                result['processed'] += 1
                
                # 计算记忆信号强度
                if should_archive_memory(memory):
                    # 归档记忆
                    success = self._archive_memory(memory)
                    if success:
                        result['archived'] += 1
                    else:
                        result['errors'] += 1
                        result['error_details'].append(f"Failed to archive memory {memory.id}")
                
            except Exception as e:
                result['errors'] += 1
                result['error_details'].append(f"Error processing memory {memory.id}: {str(e)}")
                logger.error(f"Error processing memory {memory.id}: {e}")
        
        return result
    
    def _archive_memory(self, memory: MemoryRecord) -> bool:
        """
        归档单个记忆
        
        Args:
            memory: 记忆记录
            
        Returns:
            bool: 是否成功归档
        """
        try:
            # 更新记忆的metadata，标记为已归档
            if not memory.metadata:
                memory.metadata = {}
            
            memory.metadata['is_archived'] = True
            memory.metadata['archived_at'] = datetime.now().isoformat()
            memory.metadata['archive_reason'] = 'low_signal'
            
            # 调用provider更新记录
            success = self.provider.update_record(memory)
            
            if success:
                logger.debug(f"Memory {memory.id} archived successfully")
            else:
                logger.warning(f"Failed to archive memory {memory.id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error archiving memory {memory.id}: {e}")
            return False
    
    def get_archive_statistics(self) -> Dict[str, Any]:
        """
        获取归档统计信息
        
        Returns:
            Dict: 归档统计数据
        """
        try:
            # 统计已归档的记忆数量
            archived_count = self.provider.count_records({
                "metadata.is_archived": True
            })
            
            # 统计活跃记忆数量
            active_count = self.provider.count_records({
                "metadata.is_archived": {"$ne": True}
            })
            
            # 统计总记忆数量
            total_count = archived_count + active_count
            
            return {
                'total_memories': total_count,
                'active_memories': active_count,
                'archived_memories': archived_count,
                'archive_rate': archived_count / max(1, total_count),
                'archive_threshold': ARCHIVE_THRESHOLD,
                'archive_days_threshold': self.archive_days_threshold
            }
            
        except Exception as e:
            logger.error(f"Error getting archive statistics: {e}")
            return {
                'error': str(e)
            }
