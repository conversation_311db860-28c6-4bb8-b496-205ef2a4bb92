"""
记忆评估器

这个模块是记忆策展系统的核心评估组件，负责计算记忆的信号强度。
从原scoring.py重构而来，专注于记忆价值的智能评估。

核心功能：
- calculate_signal: 计算记忆的综合信号强度
- 时间衰减函数：模拟人类记忆的遗忘曲线
- 重要性评估：基于内容特征的启发式评分
- 相关性整合：结合向量搜索的语义相关性
- 情感信号预留：为未来的情感记忆增强做准备

设计哲学：
记忆评估不是为了删除，而是为了更好的策展。
我们保留所有记忆，但通过信号强度来指导检索优先级和归档决策。

使用方式：
from xi_system.memory.curation import calculate_signal
signal = calculate_signal(memory_record, relevance_score=0.8)
"""

import math
import logging
from typing import Optional
from datetime import datetime, timedelta

from ..models import MemoryRecord, MessageRole

logger = logging.getLogger(__name__)

# 归档阈值：低于此值的记忆将被归档
ARCHIVE_THRESHOLD = 0.3

# 时间衰减参数
DECAY_HALF_LIFE_DAYS = 30  # 30天后信号强度衰减到一半
RECENT_BOOST_HOURS = 24    # 最近24小时内的记忆获得加权


def calculate_signal(memory: MemoryRecord, relevance_score: float = 0.0) -> float:
    """
    V0.9 记忆信号强度计算函数
    
    这是新陈代谢系统的核心函数，综合计算记忆的信号强度。
    
    Args:
        memory: 记忆记录
        relevance_score: 语义相关性分数（来自向量搜索，0.0-1.0）
        
    Returns:
        float: 综合信号强度 (0.0-1.0)，越高越重要
        
    计算公式：
    - 有相关性时：signal = relevance * 0.4 + recency * 0.3 + importance * 0.3
    - 无相关性时：signal = recency * 0.6 + importance * 0.4
    """
    try:
        # 1. 计算时间新近性分数（指数衰减）
        recency_score = _calculate_recency_score(memory)
        
        # 2. 计算内容重要性分数（启发式规则）
        importance_score = _calculate_importance_score(memory)
        
        # 3. 综合计算信号强度
        if relevance_score > 0.0:
            # 有查询相关性时的权重分配
            signal_strength = (
                relevance_score * 0.4 +      # 相关性权重
                recency_score * 0.3 +        # 时间权重  
                importance_score * 0.3       # 重要性权重
            )
        else:
            # 无查询相关性时的权重分配（用于归档判断）
            signal_strength = (
                recency_score * 0.6 +        # 时间权重更高
                importance_score * 0.4       # 重要性权重
            )
        
        # 确保结果在0-1范围内
        signal_strength = max(0.0, min(1.0, signal_strength))
        
        logger.debug(f"Memory signal calculated: relevance={relevance_score:.3f}, "
                    f"recency={recency_score:.3f}, importance={importance_score:.3f}, "
                    f"final_signal={signal_strength:.3f}")
        
        return signal_strength
        
    except Exception as e:
        logger.error(f"Error calculating memory signal: {e}")
        return 0.5  # 默认中等信号强度


def _calculate_recency_score(memory: MemoryRecord) -> float:
    """
    计算时间新近性分数（指数衰减函数）
    
    模拟人类记忆的遗忘曲线：
    - 最近的记忆得分最高
    - 随时间指数衰减
    - 最近24小时内有额外加权
    """
    if not memory.timestamp:
        return 0.1  # 无时间戳的记忆给予最低分
    
    now = datetime.now()
    time_diff = now - memory.timestamp
    hours_passed = time_diff.total_seconds() / 3600
    
    # 指数衰减函数：score = e^(-λt)
    # λ = ln(2) / half_life，使得在half_life时间后分数为0.5
    decay_lambda = math.log(2) / (DECAY_HALF_LIFE_DAYS * 24)
    recency_score = math.exp(-decay_lambda * hours_passed)
    
    # 最近24小时内的记忆获得额外加权
    if hours_passed < RECENT_BOOST_HOURS:
        boost_factor = 1.0 + (RECENT_BOOST_HOURS - hours_passed) / RECENT_BOOST_HOURS * 0.3
        recency_score *= boost_factor
    
    return min(1.0, recency_score)


def _calculate_importance_score(memory: MemoryRecord) -> float:
    """
    计算内容重要性分数（启发式规则）
    
    基于以下因素评估重要性：
    - 消息角色（系统消息更重要）
    - 内容长度（更长的内容可能更重要）
    - 关键词匹配（包含重要概念的内容）
    - 特殊标记（如问题、决策等）
    """
    if not memory.content:
        return 0.1
    
    importance = 0.0
    content = memory.content.lower()
    
    # 1. 角色权重
    role_weights = {
        MessageRole.XI_SYSTEM: 0.9,  # 系统消息最重要
        MessageRole.YU: 0.6,         # 用户消息较重要
        MessageRole.XI: 0.5,         # AI回复中等重要
        MessageRole.TOOL: 0.3        # 工具输出较不重要
    }
    importance += role_weights.get(memory.role, 0.3)
    
    # 2. 内容长度权重（对数缩放）
    content_length = len(memory.content)
    if content_length > 0:
        # 使用对数函数，避免过长内容权重过高
        length_score = min(0.3, math.log(content_length + 1) / math.log(1000))
        importance += length_score
    
    # 3. 关键词权重
    important_keywords = [
        '问题', '决策', '计划', '目标', '重要', '关键', '核心',
        '学习', '记住', '总结', '反思', '思考', '理解',
        'question', 'decision', 'plan', 'goal', 'important', 'key', 'core',
        'learn', 'remember', 'summary', 'reflect', 'think', 'understand'
    ]
    
    keyword_matches = sum(1 for keyword in important_keywords if keyword in content)
    keyword_score = min(0.2, keyword_matches * 0.05)
    importance += keyword_score
    
    # 4. 特殊标记权重
    special_patterns = ['?', '？', '!', '！', '决定', '计划', '目标']
    pattern_matches = sum(1 for pattern in special_patterns if pattern in content)
    pattern_score = min(0.1, pattern_matches * 0.03)
    importance += pattern_score
    
    # 确保结果在0-1范围内
    return min(1.0, importance)


def should_archive_memory(memory: MemoryRecord) -> bool:
    """
    判断记忆是否应该被归档
    
    Args:
        memory: 记忆记录
        
    Returns:
        bool: True表示应该归档，False表示保持活跃
    """
    # 计算无相关性的信号强度（纯粹基于时间和重要性）
    signal = calculate_signal(memory, relevance_score=0.0)
    
    # 低于阈值的记忆应该被归档
    should_archive = signal < ARCHIVE_THRESHOLD
    
    if should_archive:
        logger.info(f"Memory should be archived: signal={signal:.3f} < threshold={ARCHIVE_THRESHOLD}")
    
    return should_archive


def get_signal_breakdown(memory: MemoryRecord, relevance_score: float = 0.0) -> dict:
    """
    获取信号强度的详细分解（用于调试和分析）
    
    Returns:
        dict: 包含各项分数的详细信息
    """
    recency = _calculate_recency_score(memory)
    importance = _calculate_importance_score(memory)
    final_signal = calculate_signal(memory, relevance_score)
    
    return {
        'recency_score': recency,
        'importance_score': importance,
        'relevance_score': relevance_score,
        'final_signal': final_signal,
        'should_archive': should_archive_memory(memory),
        'threshold': ARCHIVE_THRESHOLD
    }
