"""
V0.9 记忆策展系统

这个包负责记忆的评估、筛选和归档管理，实现记忆系统的"新陈代谢"。
包含记忆评估器和归档器，确保记忆系统的健康与高效。

核心模块：
- evaluator.py: 记忆评估器，计算记忆信号强度
- archiver.py: 记忆归档器，管理记忆的生命周期

设计哲学：
记忆策展不是简单的删除，而是智能的筛选和归档。
我们保留所有记忆，但让重要的记忆更容易被检索到。

使用方式：
from xi_system.memory.curation import MemoryEvaluator, MemoryArchiver
evaluator = MemoryEvaluator()
archiver = MemoryArchiver(provider)
"""

from .evaluator import calculate_signal, should_archive_memory, get_signal_breakdown
from .archiver import Archiver, ArchiveResult

__all__ = [
    'calculate_signal',
    'should_archive_memory', 
    'get_signal_breakdown',
    'Archiver',
    'ArchiveResult'
]
