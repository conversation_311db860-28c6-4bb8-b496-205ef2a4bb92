# xi_system/memory/session/__init__.py

"""
V0.84 会话管理统一导出

提供会话管理相关功能的统一访问接口。
V0.84更新：消息构建功能已合并到message_formatter.py

使用示例:
    from xi_system.memory.session import HistoryManager, SessionInfo
    from xi_system.memory.session.message_formatter import get_message_formatter

    # 创建历史管理器
    history_manager = HistoryManager(provider)

    # 获取消息格式化器
    formatter = get_message_formatter()

    # 构建消息
    messages = formatter.build_messages(
        system_prompt="...",
        current_input="用户输入"
    )
"""

from .history_manager import HistoryManager, SessionInfo
from .message_formatter import MessageFormatter, get_message_formatter

# 导出所有公共接口
__all__ = [
    # 历史管理
    'HistoryManager',
    'SessionInfo',

    # 消息格式化 (V0.84)
    'MessageFormatter',
    'get_message_formatter'
]
