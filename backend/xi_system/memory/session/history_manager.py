# xi_system/memory/session/history_manager.py

"""
V0.83 会话历史管理器

管理对话会话的历史记录，提供会话创建、更新、检索和清理功能。
支持会话状态管理和历史记录的智能截断。

设计原则：
- 会话生命周期管理
- 智能历史截断
- 高效的会话检索
- 会话状态跟踪
"""

import logging
import uuid
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..providers import MemoryProvider
from ..models import MemoryRecord, MessageRole

logger = logging.getLogger(__name__)


@dataclass
class SessionInfo:
    """会话信息"""
    session_id: str
    created_at: datetime
    last_activity: datetime
    message_count: int
    status: str = "active"  # active, inactive, archived
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class HistoryManager:
    """
    会话历史管理器
    
    负责管理对话会话的历史记录，包括：
    - 会话创建和生命周期管理
    - 历史记录的存储和检索
    - 智能历史截断和清理
    - 会话状态跟踪
    """
    
    def __init__(self, provider: MemoryProvider, collection: str = "conversation_memory"):
        """
        初始化历史管理器
        
        Args:
            provider: 记忆提供者
            collection: 存储集合名称
        """
        self.provider = provider
        self.collection = collection
        self._session_cache: Dict[str, SessionInfo] = {}
        
        logger.info("History manager initialized")
    
    def create_session(self, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        创建新的会话
        
        Args:
            metadata: 可选的会话元数据
            
        Returns:
            新会话的ID
        """
        session_id = str(uuid.uuid4())
        now = datetime.utcnow()
        
        session_info = SessionInfo(
            session_id=session_id,
            created_at=now,
            last_activity=now,
            message_count=0,
            metadata=metadata or {}
        )
        
        self._session_cache[session_id] = session_info
        
        logger.info(f"Created new session: {session_id}")
        return session_id
    
    def add_message(
        self,
        session_id: str,
        content: str,
        role: MessageRole,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        向会话添加消息
        
        Args:
            session_id: 会话ID
            content: 消息内容
            role: 消息角色
            metadata: 可选的消息元数据
            
        Returns:
            消息记录ID
        """
        # 创建记忆记录
        memory_record = MemoryRecord(
            content=content,
            role=role,
            source_session_id=session_id,
            metadata=metadata or {}
        )
        
        # 存储记录
        record_id = self.provider.store(self.collection, memory_record)
        
        # 更新会话信息
        self._update_session_activity(session_id)
        
        logger.debug(f"Added message to session {session_id}: {record_id}")
        return record_id
    
    def get_session_history(
        self,
        session_id: str,
        limit: Optional[int] = None,
        include_system: bool = True
    ) -> List[MemoryRecord]:
        """
        获取会话历史记录
        
        Args:
            session_id: 会话ID
            limit: 可选的记录数量限制
            include_system: 是否包含系统消息
            
        Returns:
            会话历史记录列表，按时间正序排列
        """
        try:
            # 获取会话的所有记录
            records = self.provider.retrieve_by_session(self.collection, session_id, limit)
            
            # 过滤系统消息（如果需要）
            if not include_system:
                records = [r for r in records if not (r.role and r.role.is_system())]
            
            # 按时间正序排列（最早的在前）
            records.sort(key=lambda x: x.timestamp)
            
            return records
            
        except Exception as e:
            logger.error(f"Failed to get session history: {e}")
            return []
    
    def get_recent_history(
        self,
        session_id: str,
        limit: int = 10,
        include_system: bool = False
    ) -> List[MemoryRecord]:
        """
        获取最近的会话历史
        
        Args:
            session_id: 会话ID
            limit: 记录数量限制
            include_system: 是否包含系统消息
            
        Returns:
            最近的历史记录列表，按时间正序排列
        """
        try:
            # 获取最近的记录
            records = self.provider.retrieve_recent(self.collection, limit * 2, session_id)
            
            # 过滤系统消息（如果需要）
            if not include_system:
                records = [r for r in records if not (r.role and r.role.is_system())]
            
            # 限制数量
            records = records[:limit]
            
            # 按时间正序排列（最早的在前）
            records.sort(key=lambda x: x.timestamp)
            
            return records
            
        except Exception as e:
            logger.error(f"Failed to get recent history: {e}")
            return []
    
    def truncate_history(
        self,
        session_id: str,
        max_messages: int = 20,
        preserve_recent: int = 10
    ) -> int:
        """
        智能截断会话历史
        
        Args:
            session_id: 会话ID
            max_messages: 最大保留消息数
            preserve_recent: 必须保留的最近消息数
            
        Returns:
            删除的消息数量
        """
        try:
            # 获取所有会话记录
            all_records = self.get_session_history(session_id)
            
            if len(all_records) <= max_messages:
                return 0  # 不需要截断
            
            # 计算需要删除的记录数
            to_delete_count = len(all_records) - max_messages
            
            # 确保保留最近的消息
            if to_delete_count > len(all_records) - preserve_recent:
                to_delete_count = len(all_records) - preserve_recent
            
            if to_delete_count <= 0:
                return 0
            
            # 删除最旧的记录
            records_to_delete = all_records[:to_delete_count]
            deleted_count = 0
            
            for record in records_to_delete:
                if record.id and self.provider.delete(self.collection, record.id):
                    deleted_count += 1
            
            logger.info(f"Truncated session {session_id}: deleted {deleted_count} messages")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to truncate history: {e}")
            return 0
    
    def get_session_info(self, session_id: str) -> Optional[SessionInfo]:
        """
        获取会话信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话信息，如果不存在返回None
        """
        # 先检查缓存
        if session_id in self._session_cache:
            return self._session_cache[session_id]
        
        # 从数据库重建会话信息
        try:
            records = self.get_session_history(session_id, limit=1000)
            if not records:
                return None
            
            # 重建会话信息
            session_info = SessionInfo(
                session_id=session_id,
                created_at=min(r.timestamp for r in records),
                last_activity=max(r.timestamp for r in records),
                message_count=len(records)
            )
            
            self._session_cache[session_id] = session_info
            return session_info
            
        except Exception as e:
            logger.error(f"Failed to get session info: {e}")
            return None
    
    def list_active_sessions(self, hours_back: int = 24) -> List[SessionInfo]:
        """
        列出活跃的会话
        
        Args:
            hours_back: 活跃时间范围（小时）
            
        Returns:
            活跃会话信息列表
        """
        cutoff_time = datetime.utcnow() - timedelta(hours=hours_back)
        active_sessions = []
        
        # 从缓存中查找活跃会话
        for session_info in self._session_cache.values():
            if session_info.last_activity >= cutoff_time:
                active_sessions.append(session_info)
        
        # 按最后活动时间排序
        active_sessions.sort(key=lambda x: x.last_activity, reverse=True)
        
        return active_sessions
    
    def archive_session(self, session_id: str) -> bool:
        """
        归档会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            是否成功归档
        """
        session_info = self.get_session_info(session_id)
        if not session_info:
            return False
        
        session_info.status = "archived"
        logger.info(f"Archived session: {session_id}")
        return True
    
    def delete_session(self, session_id: str) -> int:
        """
        删除整个会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            删除的消息数量
        """
        try:
            # 获取所有会话记录
            records = self.get_session_history(session_id)
            deleted_count = 0
            
            # 删除所有记录
            for record in records:
                if record.id and self.provider.delete(self.collection, record.id):
                    deleted_count += 1
            
            # 从缓存中移除
            self._session_cache.pop(session_id, None)
            
            logger.info(f"Deleted session {session_id}: {deleted_count} messages")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to delete session: {e}")
            return 0
    
    def cleanup_old_sessions(self, days_to_keep: int = 30) -> Tuple[int, int]:
        """
        清理旧会话
        
        Args:
            days_to_keep: 保留天数
            
        Returns:
            (删除的会话数, 删除的消息数)
        """
        cutoff_time = datetime.utcnow() - timedelta(days=days_to_keep)
        deleted_sessions = 0
        deleted_messages = 0
        
        # 查找需要清理的会话
        sessions_to_delete = []
        for session_id, session_info in self._session_cache.items():
            if session_info.last_activity < cutoff_time:
                sessions_to_delete.append(session_id)
        
        # 删除旧会话
        for session_id in sessions_to_delete:
            message_count = self.delete_session(session_id)
            if message_count > 0:
                deleted_sessions += 1
                deleted_messages += message_count
        
        logger.info(f"Cleaned up {deleted_sessions} old sessions, {deleted_messages} messages")
        return deleted_sessions, deleted_messages
    
    def get_session_stats(self, session_id: str) -> Dict[str, Any]:
        """
        获取会话统计信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话统计信息
        """
        session_info = self.get_session_info(session_id)
        if not session_info:
            return {"error": "Session not found"}
        
        try:
            records = self.get_session_history(session_id)
            
            # 统计各角色的消息数
            role_counts = {}
            for record in records:
                role_name = record.role.value if record.role else "unknown"
                role_counts[role_name] = role_counts.get(role_name, 0) + 1
            
            # 计算会话时长
            if records:
                duration = (records[-1].timestamp - records[0].timestamp).total_seconds() / 3600
            else:
                duration = 0
            
            return {
                "session_id": session_id,
                "status": session_info.status,
                "created_at": session_info.created_at.isoformat(),
                "last_activity": session_info.last_activity.isoformat(),
                "message_count": len(records),
                "duration_hours": round(duration, 2),
                "role_distribution": role_counts,
                "metadata": session_info.metadata
            }
            
        except Exception as e:
            logger.error(f"Failed to get session stats: {e}")
            return {"error": str(e)}
    
    def _update_session_activity(self, session_id: str) -> None:
        """更新会话活动时间"""
        if session_id in self._session_cache:
            session_info = self._session_cache[session_id]
            session_info.last_activity = datetime.utcnow()
            session_info.message_count += 1
        else:
            # 如果缓存中没有，尝试重建
            self.get_session_info(session_id)
