"""
V0.84 统一消息格式化器

这是系统的唯一消息处理模块，负责将各种输入格式化成LLM消息。
合并了原agents/message_processor.py和memory/session/message_builder.py的功能。

核心职责：
- 构建LLM对话消息格式
- 整合System Prompt、RAG记忆和对话历史
- 角色映射和消息验证
- 消息格式化和特殊处理

设计原则：
- 单一职责：只负责消息的格式化和构建
- 领域模型优先：操作MemoryRecord对象
- 角色映射统一：内部角色到外部API格式的转换
- 可配置策略：支持不同的消息构建策略

合并来源：
- agents/message_processor.py: 消息处理业务逻辑
- memory/session/message_builder.py: 消息构建功能

使用方式：
formatter = MessageFormatter()
messages = formatter.build_messages(system_prompt, memories, history)
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from ..models import MemoryRecord, MessageRole
from .history_manager import HistoryManager

logger = logging.getLogger(__name__)


class MessageFormatter:
    """
    V0.84统一消息格式化器
    
    系统的唯一消息处理中心，负责：
    1. 构建LLM对话消息格式
    2. 整合System Prompt、RAG记忆和对话历史
    3. 角色映射和消息验证
    4. 消息格式化和特殊处理
    """
    
    def __init__(self, history_manager: Optional[HistoryManager] = None):
        """
        初始化消息格式化器
        
        Args:
            history_manager: 会话历史管理器（可选）
        """
        self.history_manager = history_manager
        
        # 角色映射：内部个性化命名 → 外部标准格式
        self.role_mapping = {
            MessageRole.YU: "user",
            MessageRole.XI: "assistant", 
            MessageRole.XI_SYSTEM: "system",
            MessageRole.TOOL: "tool"
        }
        
        logger.info("MessageFormatter initialized")
    
    def build_messages(
        self,
        system_prompt: str,
        memories: List[MemoryRecord] = None,
        history: List[MemoryRecord] = None,
        current_input: str = None
    ) -> List[Dict[str, Any]]:
        """
        构建完整的LLM消息列表
        
        Args:
            system_prompt: 系统提示词
            memories: RAG检索的记忆
            history: 对话历史
            current_input: 当前用户输入
            
        Returns:
            List[Dict[str, Any]]: LLM格式的消息列表
        """
        messages = []
        
        try:
            # 1. 构建系统消息
            system_message = self._build_system_message(system_prompt, memories)
            messages.append(system_message)
            
            # 2. 添加对话历史
            if history:
                history_messages = self._build_history_messages(history)
                messages.extend(history_messages)
            
            # 3. 添加当前用户输入
            if current_input:
                user_message = {
                    "role": "user",
                    "content": current_input
                }
                messages.append(user_message)
            
            logger.debug(f"Built {len(messages)} messages for LLM")
            return messages
            
        except Exception as e:
            logger.error(f"Error building messages: {e}")
            # 返回最基本的消息格式
            return [
                {"role": "system", "content": system_prompt or "You are Xi, an AI assistant."},
                {"role": "user", "content": current_input or "Hello"}
            ]
    
    def _build_system_message(
        self, 
        system_prompt: str, 
        memories: List[MemoryRecord] = None
    ) -> Dict[str, Any]:
        """
        构建系统消息，整合系统提示词和RAG记忆
        
        Args:
            system_prompt: 基础系统提示词
            memories: RAG检索的记忆
            
        Returns:
            Dict[str, Any]: 系统消息
        """
        content = system_prompt
        
        # 如果有RAG记忆，添加到系统消息中
        if memories:
            memory_context = self._format_memory_context(memories)
            content = f"{system_prompt}\n\n{memory_context}"
        
        return {
            "role": "system",
            "content": content
        }
    
    def _format_memory_context(self, memories: List[MemoryRecord]) -> str:
        """
        格式化记忆上下文
        
        Args:
            memories: 记忆记录列表
            
        Returns:
            str: 格式化的记忆上下文
        """
        if not memories:
            return ""
        
        context_parts = ["## 相关记忆上下文\n"]
        
        for i, memory in enumerate(memories, 1):
            # 格式化时间
            time_str = memory.timestamp.strftime("%Y-%m-%d %H:%M:%S") if memory.timestamp else "未知时间"
            
            # 格式化角色
            role_str = self._format_role_for_display(memory.role)
            
            # 添加记忆条目
            context_parts.append(f"### 记忆 {i} ({time_str}, {role_str})")
            context_parts.append(memory.content)
            context_parts.append("")  # 空行分隔
        
        return "\n".join(context_parts)
    
    def _build_history_messages(self, history: List[MemoryRecord]) -> List[Dict[str, Any]]:
        """
        构建历史消息列表
        
        Args:
            history: 历史记录列表
            
        Returns:
            List[Dict[str, Any]]: 历史消息列表
        """
        messages = []
        
        for record in history:
            # 应用角色映射
            mapped_role = self._apply_role_mapping(record.role)
            if mapped_role:  # 只处理有效的角色
                message = {
                    "role": mapped_role,
                    "content": record.content
                }
                messages.append(message)
        
        return messages
    
    def _apply_role_mapping(self, role: Optional[MessageRole]) -> Optional[str]:
        """
        应用角色映射，将内部角色转换为外部API格式
        
        Args:
            role: 内部角色
            
        Returns:
            Optional[str]: 映射后的外部角色，如果无效则返回None
        """
        if role is None:
            return None
        
        mapped_role = self.role_mapping.get(role)
        if not mapped_role:
            logger.warning(f"Unknown role for mapping: {role}")
            return None
        
        return mapped_role
    
    def _format_role_for_display(self, role: Optional[MessageRole]) -> str:
        """
        格式化角色用于显示
        
        Args:
            role: 消息角色
            
        Returns:
            str: 显示用的角色名称
        """
        if role == MessageRole.YU:
            return "禹"
        elif role == MessageRole.XI:
            return "曦"
        elif role == MessageRole.XI_SYSTEM:
            return "系统"
        elif role == MessageRole.TOOL:
            return "工具"
        else:
            return "未知"
    
    def validate_messages(self, messages: List[Dict[str, Any]]) -> bool:
        """
        验证消息格式是否正确
        
        Args:
            messages: 消息列表
            
        Returns:
            bool: 是否有效
        """
        if not messages:
            return False
        
        valid_roles = {"system", "user", "assistant", "tool"}
        
        for message in messages:
            if not isinstance(message, dict):
                return False
            
            if "role" not in message or "content" not in message:
                return False
            
            if message["role"] not in valid_roles:
                return False
            
            if not isinstance(message["content"], str):
                return False
        
        return True
    
    def format_for_llm(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        最终格式化消息用于LLM调用
        
        Args:
            messages: 原始消息列表
            
        Returns:
            List[Dict[str, Any]]: 格式化后的消息列表
        """
        if not self.validate_messages(messages):
            logger.error("Invalid messages format")
            return []
        
        # 这里可以添加更多的格式化逻辑
        # 比如消息长度限制、特殊字符处理等
        
        return messages


# 全局消息格式化器实例
_global_formatter: Optional[MessageFormatter] = None


def get_message_formatter() -> MessageFormatter:
    """获取全局消息格式化器实例"""
    global _global_formatter
    if _global_formatter is None:
        _global_formatter = MessageFormatter()
    return _global_formatter
