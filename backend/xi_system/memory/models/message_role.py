# xi_system/memory/models/message_role.py

"""
V0.83 消息角色枚举

定义系统中所有可能的消息角色类型。
支持角色转换和验证功能。

设计原则：
- 明确的角色定义
- 支持角色映射和转换
- 提供角色验证功能
- 扩展性考虑
"""

from enum import Enum
from typing import Optional, Dict, Set


class MessageRole(Enum):
    """
    系统中消息角色的枚举定义
    
    定义了Xi系统中所有可能的消息角色类型，
    支持内部个性化命名和外部标准格式的转换。
    """
    
    # 内部个性化角色命名
    YU = "yu"           # 人类用户（禹）
    XI = "xi"           # AI助手（曦）
    XI_SYSTEM = "xi_system"  # 系统消息
    TOOL = "tool"       # 工具执行结果
    
    # 标准角色（用于外部API）
    USER = "user"       # 标准用户角色
    ASSISTANT = "assistant"  # 标准助手角色
    SYSTEM = "system"   # 标准系统角色
    
    @classmethod
    def get_internal_roles(cls) -> Set['MessageRole']:
        """
        获取所有内部角色
        
        Returns:
            内部角色集合
        """
        return {cls.YU, cls.XI, cls.XI_SYSTEM, cls.TOOL}
    
    @classmethod
    def get_external_roles(cls) -> Set['MessageRole']:
        """
        获取所有外部标准角色
        
        Returns:
            外部角色集合
        """
        return {cls.USER, cls.ASSISTANT, cls.SYSTEM}
    
    @classmethod
    def get_role_mapping(cls) -> Dict['MessageRole', 'MessageRole']:
        """
        获取内部角色到外部角色的映射
        
        Returns:
            角色映射字典
        """
        return {
            cls.YU: cls.USER,
            cls.XI: cls.ASSISTANT,
            cls.XI_SYSTEM: cls.SYSTEM,
            cls.TOOL: cls.TOOL,  # 工具角色保持不变
        }
    
    @classmethod
    def get_reverse_mapping(cls) -> Dict['MessageRole', 'MessageRole']:
        """
        获取外部角色到内部角色的映射
        
        Returns:
            反向角色映射字典
        """
        return {
            cls.USER: cls.YU,
            cls.ASSISTANT: cls.XI,
            cls.SYSTEM: cls.XI_SYSTEM,
            cls.TOOL: cls.TOOL,  # 工具角色保持不变
        }
    
    def to_external(self) -> 'MessageRole':
        """
        转换为外部标准角色
        
        Returns:
            对应的外部角色
        """
        mapping = self.get_role_mapping()
        return mapping.get(self, self)
    
    def to_internal(self) -> 'MessageRole':
        """
        转换为内部个性化角色
        
        Returns:
            对应的内部角色
        """
        reverse_mapping = self.get_reverse_mapping()
        return reverse_mapping.get(self, self)
    
    @classmethod
    def from_string(cls, role_str: str) -> Optional['MessageRole']:
        """
        从字符串创建角色枚举
        
        Args:
            role_str: 角色字符串
            
        Returns:
            对应的角色枚举，如果无效返回None
        """
        if not role_str:
            return None
        
        role_str = role_str.lower().strip()
        
        # 尝试直接匹配
        for role in cls:
            if role.value.lower() == role_str:
                return role
        
        # 尝试常见别名
        aliases = {
            'human': cls.YU,
            'user': cls.USER,
            'ai': cls.XI,
            'assistant': cls.ASSISTANT,
            'system': cls.SYSTEM,
            'xi_system': cls.XI_SYSTEM,
            'tool': cls.TOOL,
            '禹': cls.YU,
            '曦': cls.XI,
            '系统': cls.SYSTEM,
            '工具': cls.TOOL,
        }
        
        return aliases.get(role_str)
    
    def is_internal(self) -> bool:
        """
        检查是否为内部角色
        
        Returns:
            是否为内部角色
        """
        return self in self.get_internal_roles()
    
    def is_external(self) -> bool:
        """
        检查是否为外部标准角色
        
        Returns:
            是否为外部角色
        """
        return self in self.get_external_roles()
    
    def is_human(self) -> bool:
        """
        检查是否为人类角色
        
        Returns:
            是否为人类角色
        """
        return self in {self.YU, self.USER}
    
    def is_ai(self) -> bool:
        """
        检查是否为AI角色
        
        Returns:
            是否为AI角色
        """
        return self in {self.XI, self.ASSISTANT}
    
    def is_system(self) -> bool:
        """
        检查是否为系统角色
        
        Returns:
            是否为系统角色
        """
        return self in {self.XI_SYSTEM, self.SYSTEM}
    
    def is_tool(self) -> bool:
        """
        检查是否为工具角色
        
        Returns:
            是否为工具角色
        """
        return self == self.TOOL
    
    def get_display_name(self, language: str = 'zh') -> str:
        """
        获取角色的显示名称
        
        Args:
            language: 语言代码（'zh'中文，'en'英文）
            
        Returns:
            角色显示名称
        """
        if language == 'zh':
            display_names = {
                self.YU: '禹',
                self.XI: '曦',
                self.XI_SYSTEM: '系统',
                self.TOOL: '工具',
                self.USER: '用户',
                self.ASSISTANT: '助手',
                self.SYSTEM: '系统',
            }
        else:  # 默认英文
            display_names = {
                self.YU: 'Yu',
                self.XI: 'Xi',
                self.XI_SYSTEM: 'System',
                self.TOOL: 'Tool',
                self.USER: 'User',
                self.ASSISTANT: 'Assistant',
                self.SYSTEM: 'System',
            }
        
        return display_names.get(self, self.value.title())
    
    def get_color_code(self) -> str:
        """
        获取角色的颜色代码（用于UI显示）
        
        Returns:
            颜色代码
        """
        color_codes = {
            self.YU: '#4A90E2',      # 蓝色
            self.XI: '#7ED321',      # 绿色
            self.XI_SYSTEM: '#9013FE', # 紫色
            self.TOOL: '#FF6B35',    # 橙色
            self.USER: '#4A90E2',    # 蓝色
            self.ASSISTANT: '#7ED321', # 绿色
            self.SYSTEM: '#9013FE',  # 紫色
        }
        
        return color_codes.get(self, '#666666')  # 默认灰色
    
    def __str__(self) -> str:
        """字符串表示"""
        return self.value
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"MessageRole.{self.name}"


# 便捷的角色创建函数
def create_role(role_input: str) -> Optional[MessageRole]:
    """
    便捷的角色创建函数
    
    Args:
        role_input: 角色输入（字符串）
        
    Returns:
        对应的角色枚举，如果无效返回None
    """
    return MessageRole.from_string(role_input)


# 常用角色常量
YU = MessageRole.YU
XI = MessageRole.XI
XI_SYSTEM = MessageRole.XI_SYSTEM
TOOL = MessageRole.TOOL
USER = MessageRole.USER
ASSISTANT = MessageRole.ASSISTANT
SYSTEM = MessageRole.SYSTEM
