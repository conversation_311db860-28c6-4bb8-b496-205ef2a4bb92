# xi_system/memory/models/__init__.py

"""
V0.83 记忆模型统一导出

提供记忆相关领域模型的统一访问接口。
包含记忆记录和消息角色的核心定义。

使用示例:
    from xi_system.memory.models import MemoryRecord, MessageRole, YU, XI
    
    # 创建记忆记录
    record = MemoryRecord(
        content="Hello world",
        role=YU,
        source_session_id="session_123"
    )
    
    # 角色转换
    external_role = YU.to_external()  # 转换为USER
"""

from .memory_record import MemoryRecord
from .message_role import (
    MessageRole,
    create_role,
    # 常用角色常量
    YU,
    XI, 
    XI_SYSTEM,
    TOOL,
    USER,
    ASSISTANT,
    SYSTEM
)

# 导出所有公共接口
__all__ = [
    # 核心模型类
    'MemoryRecord',
    'MessageRole',
    
    # 便捷函数
    'create_role',
    
    # 角色常量
    'YU',
    'XI',
    'XI_SYSTEM', 
    'TOOL',
    'USER',
    'ASSISTANT',
    'SYSTEM'
]
