# xi_system/memory/models/memory_record.py

"""
V0.83 记忆记录领域模型

定义系统中记忆记录的核心数据结构。
这是一个纯粹的领域模型，完全独立于任何存储实现。

设计原则：
- 领域模型优先，存储实现其次
- 纯Python数据类，无外部依赖
- 包含业务逻辑方法
- 支持多维度评分计算
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional, Dict, Any
import math


@dataclass
class MemoryRecord:
    """
    记忆记录领域模型
    
    封装系统中单个记忆/对话条目的所有信息。
    完全独立于数据库实现，是系统内记忆数据的规范表示。
    
    Attributes:
        id: 存储层分配的唯一标识符（新记录为None）
        content: 记忆的实际文本内容
        timestamp: 记忆创建时间
        role: 生成此记忆的角色（yu, xi, system, tool）
        embedding: 语义搜索的向量表示（384维）
        source_session_id: 会话标识符，用于对话分组
        metadata: 额外的灵活数据（评分、标签等）
    """
    
    # 核心必需字段
    content: str
    
    # 可选字段，带默认值
    id: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.utcnow)
    role: Optional['MessageRole'] = None
    embedding: Optional[List[float]] = None
    source_session_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """后初始化处理"""
        # 确保content不为空
        if not self.content or not self.content.strip():
            raise ValueError("Memory content cannot be empty")
        
        # 确保metadata是字典
        if self.metadata is None:
            self.metadata = {}
    
    def get_semantic_score(self, query_embedding: List[float]) -> float:
        """
        计算与查询向量的语义相似度分数
        
        Args:
            query_embedding: 查询向量
            
        Returns:
            语义相似度分数 (0.0-1.0)
        """
        if not self.embedding or not query_embedding:
            return 0.0
        
        if len(self.embedding) != len(query_embedding):
            return 0.0
        
        try:
            # 计算余弦相似度
            dot_product = sum(a * b for a, b in zip(self.embedding, query_embedding))
            norm_a = math.sqrt(sum(a * a for a in self.embedding))
            norm_b = math.sqrt(sum(b * b for b in query_embedding))
            
            if norm_a == 0 or norm_b == 0:
                return 0.0
            
            similarity = dot_product / (norm_a * norm_b)
            
            # 将相似度从[-1, 1]映射到[0, 1]
            return max(0.0, (similarity + 1) / 2)
            
        except Exception:
            return 0.0
    
    def get_temporal_score(self, decay_hours: float = 168.0) -> float:
        """
        计算基于时间的相关性分数
        
        Args:
            decay_hours: 衰减时间（小时），默认168小时（7天）
            
        Returns:
            时间相关性分数 (0.0-1.0)
        """
        try:
            now = datetime.utcnow()
            time_diff = (now - self.timestamp).total_seconds() / 3600  # 转换为小时
            
            # 指数衰减函数
            score = math.exp(-time_diff / decay_hours)
            return max(0.0, min(1.0, score))
            
        except Exception:
            return 0.0
    
    def get_importance_score(self) -> float:
        """
        计算内容重要性分数
        
        基于内容长度、关键词、角色等因素计算重要性。
        
        Returns:
            重要性分数 (0.0-1.0)
        """
        try:
            score = 0.0
            
            # 基础分数：内容长度
            content_length = len(self.content.strip())
            if content_length > 0:
                # 长度分数，使用对数函数避免过度偏向长文本
                length_score = min(1.0, math.log(content_length + 1) / math.log(1000))
                score += length_score * 0.3
            
            # 角色权重
            role_weights = {
                'xi': 0.8,      # AI回复通常重要
                'yu': 0.9,      # 用户输入很重要
                'system': 0.5,  # 系统消息中等重要
                'tool': 0.6     # 工具结果较重要
            }
            
            if self.role:
                role_name = self.role.value if hasattr(self.role, 'value') else str(self.role)
                role_score = role_weights.get(role_name, 0.5)
                score += role_score * 0.4
            
            # 关键词权重
            important_keywords = [
                '重要', '关键', '核心', '主要', '必须', '需要',
                'important', 'key', 'core', 'main', 'must', 'need',
                '问题', '解决', '方案', '建议', '想法',
                'problem', 'solution', 'suggestion', 'idea'
            ]
            
            content_lower = self.content.lower()
            keyword_count = sum(1 for keyword in important_keywords if keyword in content_lower)
            keyword_score = min(1.0, keyword_count / 5)  # 最多5个关键词得满分
            score += keyword_score * 0.3
            
            # 确保分数在[0, 1]范围内
            return max(0.0, min(1.0, score))
            
        except Exception:
            return 0.5  # 默认中等重要性
    
    def get_final_score(
        self,
        query_embedding: Optional[List[float]] = None,
        semantic_weight: float = 0.6,
        temporal_weight: float = 0.3,
        importance_weight: float = 0.1
    ) -> float:
        """
        计算最终综合分数
        
        Args:
            query_embedding: 查询向量（用于语义相似度）
            semantic_weight: 语义相似度权重
            temporal_weight: 时间相关性权重
            importance_weight: 重要性权重
            
        Returns:
            最终综合分数 (0.0-1.0)
        """
        # 确保权重总和为1
        total_weight = semantic_weight + temporal_weight + importance_weight
        if total_weight == 0:
            return 0.0
        
        semantic_weight /= total_weight
        temporal_weight /= total_weight
        importance_weight /= total_weight
        
        # 计算各项分数
        semantic_score = self.get_semantic_score(query_embedding) if query_embedding else 0.0
        temporal_score = self.get_temporal_score()
        importance_score = self.get_importance_score()
        
        # 加权计算最终分数
        final_score = (
            semantic_score * semantic_weight +
            temporal_score * temporal_weight +
            importance_score * importance_weight
        )
        
        return max(0.0, min(1.0, final_score))
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            字典表示
        """
        return {
            'id': self.id,
            'content': self.content,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'role': self.role.value if self.role else None,
            'embedding': self.embedding,
            'source_session_id': self.source_session_id,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryRecord':
        """
        从字典创建MemoryRecord实例
        
        Args:
            data: 字典数据
            
        Returns:
            MemoryRecord实例
        """
        # 处理时间戳
        timestamp = data.get('timestamp')
        if isinstance(timestamp, str):
            timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        elif timestamp is None:
            timestamp = datetime.utcnow()
        
        # 处理角色
        role = data.get('role')
        if role and isinstance(role, str):
            from .message_role import MessageRole
            try:
                role = MessageRole(role)
            except ValueError:
                role = None
        
        return cls(
            id=data.get('id'),
            content=data.get('content', ''),
            timestamp=timestamp,
            role=role,
            embedding=data.get('embedding'),
            source_session_id=data.get('source_session_id'),
            metadata=data.get('metadata', {})
        )
    
    def __str__(self) -> str:
        """字符串表示"""
        role_str = self.role.value if self.role else 'unknown'
        content_preview = self.content[:50] + '...' if len(self.content) > 50 else self.content
        return f"MemoryRecord(role={role_str}, content='{content_preview}')"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"MemoryRecord(id={self.id}, role={self.role}, "
                f"timestamp={self.timestamp}, content_length={len(self.content)})")
