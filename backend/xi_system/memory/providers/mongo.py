# xi_system/memory/providers/mongo.py

"""
MongoDB记忆提供者

MongoDB特定的记忆存储提供者实现，作为领域模型与MongoDB文档之间的"防腐层"。
遵循"领域模型优先，存储实现其次"原则，提供MemoryRecord对象与MongoDB文档的转换。

特性：
- MongoDB Atlas向量搜索集成
- 通过EmbeddingService统一嵌入生成
- 领域模型与存储实现完全隔离
- 自动嵌入生成和向量搜索
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from pymongo import MongoClient, DESCENDING
from pymongo.errors import PyMongoError

from bson import ObjectId

from .base import MemoryProvider, StorageError
from ..models import MemoryRecord, MessageRole
from ...service.database import DatabaseProvider

logger = logging.getLogger(__name__)


class MongoProvider(MemoryProvider, DatabaseProvider):
    """
    MongoDB记忆提供者实现

    作为领域模型（MemoryRecord）与MongoDB文档之间的"防腐层"。
    处理纯领域对象与数据库特定表示之间的所有转换。

    特性：
    - 通过EmbeddingService统一嵌入生成
    - MongoDB Atlas向量搜索语义相似度
    - 领域模型与存储实现完全隔离
    - 健壮的错误处理和连接管理
    """
    
    def __init__(self, uri: str, db_name: str, timeout: int = 30, embedding_service=None):
        """
        初始化MongoDB提供者

        Args:
            uri: MongoDB连接URI
            db_name: 数据库名称
            timeout: 连接超时时间（秒）
            embedding_service: 嵌入服务实例
        """
        self.uri = uri
        self.db_name = db_name
        self.timeout = timeout
        self.embedding_service = embedding_service

        # 连接相关
        self.client: Optional[MongoClient] = None
        self.db = None
        self._connected = False
    
    def connect(self) -> None:
        """建立MongoDB连接"""
        if self._connected:
            return
        
        try:
            logger.info(f"Connecting to MongoDB: {self.db_name}")
            
            self.client = MongoClient(
                self.uri,
                serverSelectionTimeoutMS=self.timeout * 1000,
                connectTimeoutMS=self.timeout * 1000,
                socketTimeoutMS=self.timeout * 1000
            )
            
            # 测试连接
            self.client.admin.command('ping')
            
            self.db = self.client[self.db_name]
            self._connected = True
            
            logger.info("MongoDB connection established successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise StorageError(f"MongoDB connection failed: {e}")
    
    def disconnect(self) -> None:
        """断开MongoDB连接"""
        if self.client:
            try:
                self.client.close()
                logger.info("MongoDB connection closed")
            except Exception as e:
                logger.error(f"Error closing MongoDB connection: {e}")
            finally:
                self.client = None
                self.db = None
                self._connected = False
    
    def is_connected(self) -> bool:
        """检查MongoDB连接状态"""
        if not self._connected or not self.client:
            return False
        
        try:
            # 发送ping命令测试连接
            self.client.admin.command('ping')
            return True
        except Exception:
            self._connected = False
            return False
    
    def _ensure_connected(self) -> None:
        """确保已连接，如果未连接则自动连接"""
        if not self.is_connected():
            self.connect()
    

    
    def _to_document(self, record: MemoryRecord) -> Dict[str, Any]:
        """将MemoryRecord转换为MongoDB文档"""
        doc = {
            "content": record.content,
            "timestamp": record.timestamp,
            "role": record.role.value if record.role else None,
            "source_session_id": record.source_session_id,
            "metadata": record.metadata or {}
        }
        
        # 处理嵌入向量
        if record.embedding:
            doc["embedding"] = record.embedding
        else:
            # 使用嵌入服务自动生成嵌入
            if self.embedding_service:
                try:
                    doc["embedding"] = self.embedding_service.generate_embedding(record.content)
                except Exception as e:
                    logger.error(f"Failed to generate embedding using EmbeddingService: {e}")
                    doc["embedding"] = []
            else:
                logger.warning("No embedding service available, storing empty embedding")
                doc["embedding"] = []
        
        # 如果有ID，添加到文档中
        if record.id:
            try:
                doc["_id"] = ObjectId(record.id)
            except Exception:
                # 如果ID不是有效的ObjectId，让MongoDB自动生成
                pass
        
        return doc
    
    def _to_record(self, document: Dict[str, Any]) -> MemoryRecord:
        """将MongoDB文档转换为MemoryRecord"""
        # 处理角色
        role = None
        if document.get("role"):
            role = MessageRole.from_string(document["role"])
        
        # 处理时间戳
        timestamp = document.get("timestamp", datetime.utcnow())
        if isinstance(timestamp, str):
            timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        
        return MemoryRecord(
            id=str(document["_id"]) if document.get("_id") else None,
            content=document.get("content", ""),
            timestamp=timestamp,
            role=role,
            embedding=document.get("embedding"),
            source_session_id=document.get("source_session_id"),
            metadata=document.get("metadata", {})
        )
    
    def store(self, collection_name: str, record: MemoryRecord) -> str:
        """存储MemoryRecord到指定集合"""
        self._ensure_connected()
        
        try:
            collection = self.db[collection_name]
            document = self._to_document(record)
            
            result = collection.insert_one(document)
            record_id = str(result.inserted_id)
            
            logger.debug(f"Stored record in {collection_name}: {record_id}")
            return record_id
            
        except Exception as e:
            logger.error(f"Failed to store record: {e}")
            raise StorageError(f"Store operation failed: {e}")
    
    def retrieve_by_id(self, collection_name: str, record_id: str) -> Optional[MemoryRecord]:
        """根据ID检索MemoryRecord"""
        self._ensure_connected()
        
        try:
            collection = self.db[collection_name]
            
            # 尝试转换为ObjectId
            try:
                object_id = ObjectId(record_id)
            except Exception:
                logger.warning(f"Invalid ObjectId: {record_id}")
                return None
            
            document = collection.find_one({"_id": object_id})
            
            if document:
                return self._to_record(document)
            return None
            
        except Exception as e:
            logger.error(f"Failed to retrieve record by ID: {e}")
            raise StorageError(f"Retrieve by ID failed: {e}")
    
    def retrieve_recent(
        self,
        collection_name: str,
        limit: int = 10,
        session_id: Optional[str] = None
    ) -> List[MemoryRecord]:
        """检索最近的记录"""
        self._ensure_connected()
        
        try:
            collection = self.db[collection_name]
            
            # 构建查询条件
            query = {}
            if session_id:
                query["source_session_id"] = session_id
            
            # 按时间戳倒序查询
            cursor = collection.find(query).sort("timestamp", DESCENDING).limit(limit)
            
            records = []
            for document in cursor:
                records.append(self._to_record(document))
            
            logger.debug(f"Retrieved {len(records)} recent records from {collection_name}")
            return records
            
        except Exception as e:
            logger.error(f"Failed to retrieve recent records: {e}")
            raise StorageError(f"Retrieve recent failed: {e}")
    
    def vector_search(
        self,
        collection_name: str,
        query_embedding: List[float],
        limit: int = 10,
        min_score: float = 0.0,
        session_id: Optional[str] = None,
        exclude_archived: bool = True
    ) -> List[MemoryRecord]:
        """执行向量相似度搜索"""
        self._ensure_connected()
        
        try:
            collection = self.db[collection_name]
            
            # 构建向量搜索管道
            pipeline = [
                {
                    "$vectorSearch": {
                        "index": "vector_index",
                        "path": "embedding",
                        "queryVector": query_embedding,
                        "numCandidates": limit * 10,  # 候选数量
                        "limit": limit
                    }
                }
            ]
            
            # 构建过滤条件
            match_conditions = {}

            # 添加会话过滤
            if session_id:
                match_conditions["source_session_id"] = session_id

            # V0.9: 添加归档过滤
            if exclude_archived:
                match_conditions["metadata.is_archived"] = {"$ne": True}

            # 添加分数过滤
            if min_score > 0:
                match_conditions["score"] = {"$gte": min_score}

            # 如果有过滤条件，添加到管道
            if match_conditions:
                pipeline.append({
                    "$match": match_conditions
                })
            
            # 执行聚合查询
            cursor = collection.aggregate(pipeline)
            
            records = []
            for document in cursor:
                records.append(self._to_record(document))
            
            logger.debug(f"Vector search returned {len(records)} records from {collection_name}")
            return records
            
        except Exception as e:
            logger.error(f"Vector search failed: {e}")
            # 如果向量搜索失败，回退到普通查询
            logger.warning("Falling back to regular query")
            return self.retrieve_recent(collection_name, limit, session_id)
    
    def update(self, collection_name: str, record: MemoryRecord) -> bool:
        """更新现有的MemoryRecord"""
        self._ensure_connected()
        
        if not record.id:
            raise StorageError("Record ID is required for update operation")
        
        try:
            collection = self.db[collection_name]
            
            # 转换为文档格式
            document = self._to_document(record)
            object_id = ObjectId(record.id)
            
            # 移除_id字段，因为它不能被更新
            document.pop("_id", None)
            
            result = collection.update_one(
                {"_id": object_id},
                {"$set": document}
            )
            
            success = result.modified_count > 0
            logger.debug(f"Update record {record.id}: {'success' if success else 'no changes'}")
            return success
            
        except Exception as e:
            logger.error(f"Failed to update record: {e}")
            raise StorageError(f"Update operation failed: {e}")
    
    def delete(self, collection_name: str, record_id: str) -> bool:
        """删除指定的记录"""
        self._ensure_connected()
        
        try:
            collection = self.db[collection_name]
            object_id = ObjectId(record_id)
            
            result = collection.delete_one({"_id": object_id})
            
            success = result.deleted_count > 0
            logger.debug(f"Delete record {record_id}: {'success' if success else 'not found'}")
            return success
            
        except Exception as e:
            logger.error(f"Failed to delete record: {e}")
            raise StorageError(f"Delete operation failed: {e}")
    
    def count(self, collection_name: str, session_id: Optional[str] = None) -> int:
        """计算集合中的记录数量"""
        self._ensure_connected()
        
        try:
            collection = self.db[collection_name]
            
            query = {}
            if session_id:
                query["source_session_id"] = session_id
            
            count = collection.count_documents(query)
            logger.debug(f"Count records in {collection_name}: {count}")
            return count
            
        except Exception as e:
            logger.error(f"Failed to count records: {e}")
            raise StorageError(f"Count operation failed: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取存储系统统计信息"""
        self._ensure_connected()
        
        try:
            stats = self.db.command("dbStats")
            
            # 获取主要集合的统计信息
            collections_stats = {}
            for collection_name in self.db.list_collection_names():
                try:
                    coll_stats = self.db.command("collStats", collection_name)
                    collections_stats[collection_name] = {
                        "count": coll_stats.get("count", 0),
                        "size": coll_stats.get("size", 0),
                        "avgObjSize": coll_stats.get("avgObjSize", 0)
                    }
                except Exception:
                    # 忽略单个集合的统计错误
                    pass
            
            return {
                "total_memories": sum(c.get("count", 0) for c in collections_stats.values()),
                "knowledge_entries": collections_stats.get("knowledge", {}).get("count", 0),
                "db_size_mb": round(stats.get("dataSize", 0) / (1024 * 1024), 2),
                "collections": len(collections_stats),
                "collections_stats": collections_stats
            }
            
        except Exception as e:
            logger.error(f"Failed to get stats: {e}")
            raise StorageError(f"Stats operation failed: {e}")
    
    def health_check(self) -> Dict[str, Any]:
        """执行健康检查"""
        try:
            if not self.is_connected():
                return {
                    "status": "error",
                    "connected": False,
                    "message": "Not connected to MongoDB"
                }
            
            # 执行ping命令
            self.client.admin.command('ping')
            
            # 获取基本信息
            server_info = self.client.server_info()
            
            return {
                "status": "healthy",
                "connected": True,
                "database": self.db_name,
                "server_version": server_info.get("version", "unknown"),
                "embedding_service_available": self.embedding_service is not None
            }
            
        except Exception as e:
            return {
                "status": "error",
                "connected": False,
                "error": str(e)
            }

    def query_records(
        self,
        collection_name: str = "conversation_memory",
        filter_dict: Optional[Dict[str, Any]] = None,
        sort: Optional[List[tuple]] = None,
        limit: Optional[int] = None,
        skip: Optional[int] = None
    ) -> List[MemoryRecord]:
        """
        V0.9: 通用查询方法，供Archiver等模块使用

        Args:
            collection_name: 集合名称
            filter_dict: 查询过滤条件
            sort: 排序条件 [(field, direction), ...]
            limit: 限制数量
            skip: 跳过数量

        Returns:
            List[MemoryRecord]: 查询结果
        """
        self._ensure_connected()

        try:
            collection = self.db[collection_name]

            # 构建查询
            query = filter_dict or {}

            # 执行查询
            cursor = collection.find(query)

            # 应用排序
            if sort:
                cursor = cursor.sort(sort)

            # 应用跳过和限制
            if skip:
                cursor = cursor.skip(skip)
            if limit:
                cursor = cursor.limit(limit)

            # 转换为MemoryRecord
            records = []
            for document in cursor:
                records.append(self._to_record(document))

            logger.debug(f"Query returned {len(records)} records")
            return records

        except Exception as e:
            logger.error(f"Query failed: {e}")
            raise StorageError(f"Query failed: {e}")

    def count_records(
        self,
        collection_name: str = "conversation_memory",
        filter_dict: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        V0.9: 计数查询方法

        Args:
            collection_name: 集合名称
            filter_dict: 查询过滤条件

        Returns:
            int: 记录数量
        """
        self._ensure_connected()

        try:
            collection = self.db[collection_name]
            query = filter_dict or {}
            count = collection.count_documents(query)

            logger.debug(f"Count query returned {count} records")
            return count

        except Exception as e:
            logger.error(f"Count query failed: {e}")
            raise StorageError(f"Count query failed: {e}")

    def update_record(self, record: MemoryRecord) -> bool:
        """
        V0.9: 更新记录方法，供Archiver使用

        Args:
            record: 要更新的记忆记录

        Returns:
            bool: 是否更新成功
        """
        self._ensure_connected()

        try:
            collection = self.db["conversation_memory"]

            # 转换为文档格式
            document = self._to_document(record)

            # 更新记录
            result = collection.replace_one(
                {"_id": record.id},
                document
            )

            success = result.modified_count > 0
            if success:
                logger.debug(f"Updated record {record.id}")
            else:
                logger.warning(f"No record updated for {record.id}")

            return success

        except Exception as e:
            logger.error(f"Update record failed: {e}")
            return False
