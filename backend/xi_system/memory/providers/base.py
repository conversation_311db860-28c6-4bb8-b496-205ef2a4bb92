# xi_system/memory/providers/base.py

"""
V0.83 记忆提供者抽象基类

定义记忆存储提供者的抽象接口，实现"领域模型优先，存储实现其次"的原则。
所有提供者方法都专门操作领域模型（MemoryRecord对象），而不是原始数据库文档。

设计原则：
- 领域模型优先，存储实现其次
- 完全的业务逻辑与存储实现分离
- 可插拔的存储后端支持
- 统一的错误处理和接口规范
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..models import MemoryRecord


class StorageError(Exception):
    """存储操作异常"""
    pass


class MemoryProvider(ABC):
    """
    记忆存储提供者抽象基类
    
    定义所有记忆存储提供者必须实现的接口，以便与Xi ContextOS记忆管理系统兼容。
    所有方法都操作领域模型（MemoryRecord），确保与具体存储实现完全解耦。
    
    遵循"领域模型优先"原则，提供者充当纯领域模型与底层存储系统之间的翻译层。
    """
    
    @abstractmethod
    def connect(self) -> None:
        """
        建立与存储系统的连接
        
        Raises:
            StorageError: 如果连接失败
        """
        pass
    
    @abstractmethod
    def disconnect(self) -> None:
        """
        断开与存储系统的连接
        """
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """
        检查是否已连接到存储系统
        
        Returns:
            是否已连接
        """
        pass
    
    @abstractmethod
    def store(self, collection_name: str, record: MemoryRecord) -> str:
        """
        在指定集合中存储MemoryRecord
        
        Args:
            collection_name: 要存储记录的集合名称
            record: 要存储的MemoryRecord领域模型
            
        Returns:
            存储系统分配的唯一标识符
            
        Raises:
            StorageError: 如果存储操作失败
        """
        pass
    
    @abstractmethod
    def retrieve_by_id(self, collection_name: str, record_id: str) -> Optional[MemoryRecord]:
        """
        根据ID检索单个MemoryRecord
        
        Args:
            collection_name: 集合名称
            record_id: 记录的唯一标识符
            
        Returns:
            MemoryRecord领域模型，如果未找到返回None
            
        Raises:
            StorageError: 如果检索操作失败
        """
        pass
    
    @abstractmethod
    def retrieve_recent(
        self,
        collection_name: str,
        limit: int = 10,
        session_id: Optional[str] = None
    ) -> List[MemoryRecord]:
        """
        检索最近的记录
        
        Args:
            collection_name: 集合名称
            limit: 返回记录的最大数量
            session_id: 可选的会话ID过滤器
            
        Returns:
            MemoryRecord列表，按时间倒序排列
            
        Raises:
            StorageError: 如果检索操作失败
        """
        pass
    
    @abstractmethod
    def vector_search(
        self,
        collection_name: str,
        query_embedding: List[float],
        limit: int = 10,
        min_score: float = 0.0,
        session_id: Optional[str] = None
    ) -> List[MemoryRecord]:
        """
        执行向量相似度搜索
        
        Args:
            collection_name: 集合名称
            query_embedding: 查询向量
            limit: 返回记录的最大数量
            min_score: 最小相似度分数阈值
            session_id: 可选的会话ID过滤器
            
        Returns:
            MemoryRecord列表，按相似度分数排序
            
        Raises:
            StorageError: 如果搜索操作失败
        """
        pass
    
    @abstractmethod
    def update(self, collection_name: str, record: MemoryRecord) -> bool:
        """
        更新现有的MemoryRecord
        
        Args:
            collection_name: 集合名称
            record: 要更新的MemoryRecord（必须包含有效的ID）
            
        Returns:
            是否成功更新
            
        Raises:
            StorageError: 如果更新操作失败
        """
        pass
    
    @abstractmethod
    def delete(self, collection_name: str, record_id: str) -> bool:
        """
        删除指定的记录
        
        Args:
            collection_name: 集合名称
            record_id: 要删除的记录ID
            
        Returns:
            是否成功删除
            
        Raises:
            StorageError: 如果删除操作失败
        """
        pass
    
    @abstractmethod
    def count(self, collection_name: str, session_id: Optional[str] = None) -> int:
        """
        计算集合中的记录数量
        
        Args:
            collection_name: 集合名称
            session_id: 可选的会话ID过滤器
            
        Returns:
            记录数量
            
        Raises:
            StorageError: 如果计数操作失败
        """
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        """
        获取存储系统统计信息
        
        Returns:
            包含统计信息的字典
            
        Raises:
            StorageError: 如果获取统计信息失败
        """
        pass
    
    @abstractmethod
    def health_check(self) -> Dict[str, Any]:
        """
        执行健康检查
        
        Returns:
            包含健康状态信息的字典
        """
        pass
    
    # 便捷方法（可选实现）
    
    def store_batch(self, collection_name: str, records: List[MemoryRecord]) -> List[str]:
        """
        批量存储记录（默认实现）
        
        Args:
            collection_name: 集合名称
            records: 要存储的MemoryRecord列表
            
        Returns:
            存储系统分配的ID列表
            
        Raises:
            StorageError: 如果批量存储失败
        """
        ids = []
        for record in records:
            record_id = self.store(collection_name, record)
            ids.append(record_id)
        return ids
    
    def retrieve_by_session(
        self,
        collection_name: str,
        session_id: str,
        limit: Optional[int] = None
    ) -> List[MemoryRecord]:
        """
        检索指定会话的所有记录（默认实现）
        
        Args:
            collection_name: 集合名称
            session_id: 会话ID
            limit: 可选的记录数量限制
            
        Returns:
            MemoryRecord列表
        """
        return self.retrieve_recent(
            collection_name=collection_name,
            limit=limit or 1000,  # 默认最多1000条
            session_id=session_id
        )
    
    def cleanup_old_records(
        self,
        collection_name: str,
        older_than: datetime,
        session_id: Optional[str] = None
    ) -> int:
        """
        清理旧记录（默认实现 - 子类应该重写以提高效率）
        
        Args:
            collection_name: 集合名称
            older_than: 删除早于此时间的记录
            session_id: 可选的会话ID过滤器
            
        Returns:
            删除的记录数量
        """
        # 默认实现：检索所有记录并逐个检查删除
        # 子类应该重写此方法以提供更高效的批量删除
        records = self.retrieve_recent(collection_name, limit=10000, session_id=session_id)
        deleted_count = 0
        
        for record in records:
            if record.timestamp < older_than:
                if self.delete(collection_name, record.id):
                    deleted_count += 1
        
        return deleted_count
