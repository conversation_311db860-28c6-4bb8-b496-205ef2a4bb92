# xi_system/memory/providers/__init__.py

"""
V0.83 记忆提供者统一导出

提供记忆存储提供者的统一访问接口。
包含抽象基类和具体实现。

使用示例:
    from xi_system.memory.providers import MemoryProvider, MongoProvider
    
    # 创建MongoDB Atlas提供者
    provider = MongoProvider(
        uri="mongodb+srv://username:<EMAIL>/xi_db",
        db_name="xi_db"
    )
    
    # 连接并使用
    provider.connect()
    record_id = provider.store("memories", memory_record)
"""

from .base import MemoryProvider, StorageError
from .mongo import MongoProvider

# 导出所有公共接口
__all__ = [
    # 抽象基类
    'MemoryProvider',
    
    # 异常类
    'StorageError',
    
    # 具体实现
    'MongoProvider'
]
