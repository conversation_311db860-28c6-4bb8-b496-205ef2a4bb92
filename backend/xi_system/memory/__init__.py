"""
Memory System - Unified Interface

Provides unified access to the complete memory management system.
Follows "domain model first" principle with all interfaces operating on pure Python objects.

Components:
- models/: Domain models (MemoryRecord, MessageRole)
- providers/: Storage providers with anti-corruption layer
- retrieval/: Semantic memory retrieval and scoring
- curation/: Memory evaluation and archiving system
- session/: Conversation history and message formatting

Key Features:
- Semantic memory retrieval with vector search
- Intelligent memory curation and archiving
- Role-based message handling (yu/xi internal naming)
- Session-based conversation management
- MongoDB storage with embedding support

Usage:
    from xi_system.memory import (
        MemoryRecord, MessageRole, YU, XI,
        MongoProvider, Retriever, HistoryManager, MessageFormatter
    )

    # Create memory record
    record = MemoryRecord(content="Hello", role=YU)

    # Use provider for storage
    provider = MongoProvider(uri="...", db_name="xi_db")
    record_id = provider.store("conversations", record)

    # Retrieve memories
    retriever = Retriever(provider)
    results = retriever.retrieve_memories("query")

    # Session management
    history_manager = HistoryManager(provider)
    formatter = MessageFormatter(history_manager)
"""

# 领域模型
from .models import (
    Memory<PERSON><PERSON>ord, MessageR<PERSON>, create_role,
    YU, XI, XI_SYSTEM, TOOL, USER, ASSISTANT, SYSTEM
)

# Storage providers
from .providers import MemoryProvider, MongoProvider, StorageError

# Retrieval system
from .retrieval import Retriever

# Curation system
from .curation import calculate_signal, should_archive_memory, Archiver

# Session management
from .session import HistoryManager, SessionInfo, MessageFormatter, get_message_formatter



# Export all public interfaces
__all__ = [
    # Domain models
    'MemoryRecord',
    'MessageRole',
    'create_role',

    # Role constants
    'YU', 'XI', 'XI_SYSTEM', 'TOOL',
    'USER', 'ASSISTANT', 'SYSTEM',

    # Storage providers
    'MemoryProvider',
    'MongoProvider',
    'StorageError',

    # Retrieval system
    'Retriever',
    'calculate_signal',
    'should_archive_memory',

    # Curation system
    'Archiver',

    # Session management
    'HistoryManager',
    'SessionInfo',
    'MessageFormatter',
    'get_message_formatter'
]
