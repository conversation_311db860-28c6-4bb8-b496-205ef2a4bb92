# Xi 记忆管理系统 (Xi Memory Management System)

## 概述 (Overview)

Xi 记忆管理系统是曦智能体的"记忆大脑"，负责存储、检索、整理和管理所有对话历史、知识笔记和上下文信息。该系统实现了曦的长期记忆能力，支持语义搜索、智能归档和上下文感知的记忆检索。

### 核心理念

- **领域模型优先**: 所有接口操作纯Python对象，完全独立于存储实现
- **语义记忆检索**: 基于向量搜索的智能记忆匹配和相关性评分
- **智能记忆整理**: 自动评估记忆重要性，执行归档和清理策略
- **角色感知系统**: 支持内部命名（yu/xi）与外部系统的无缝转换
- **会话上下文管理**: 完整的对话历史跟踪和上下文维护

### 在整体架构中的位置

```
Xi 系统架构层次:
├── core/           # 轻量级业务流程编排器
├── service/        # 基础设施服务层
├── agents/         # AI交互处理器
├── memory/         # 记忆管理系统 ← 本模块
├── tools/          # 工具系统
├── prompts/        # 提示词系统
└── tasks/          # 后台认知任务系统
```

### 核心价值

- **持久化对话上下文**: 保持跨会话的连续性和一致性
- **智能知识检索**: 基于语义相似度的精准记忆匹配
- **自动记忆管理**: 智能评估和归档，优化存储效率
- **多维度记忆评分**: 结合时间、重要性和相关性的综合评分系统

## 系统架构 (Architecture)

### 核心组件层次结构

```
memory/
├── models/                 # 领域模型层
│   ├── memory_record.py   # 记忆记录核心模型
│   └── message_role.py    # 消息角色系统
├── providers/             # 存储提供者层
│   ├── base.py           # 抽象存储接口
│   └── mongo.py          # MongoDB实现
├── retrieval/            # 检索系统层
│   └── retriever.py      # 语义记忆检索器
├── curation/             # 整理系统层
│   ├── evaluator.py      # 记忆评估和信号计算
│   └── archiver.py       # 记忆归档管理
├── session/              # 会话管理层
│   ├── history_manager.py # 会话历史管理
│   └── message_formatter.py # 消息格式化
└── notes/                # 知识笔记存储
```

### 核心类和组件

#### 1. MemoryRecord (记忆记录模型)
```python
@dataclass
class MemoryRecord:
    """记忆记录领域模型"""
    
    # 核心字段
    content: str                        # 记忆内容
    id: Optional[str] = None           # 存储标识符
    timestamp: datetime                 # 创建时间
    role: Optional[MessageRole] = None  # 消息角色
    embedding: Optional[List[float]]    # 语义向量(384维)
    source_session_id: Optional[str]    # 会话标识
    metadata: Dict[str, Any]           # 扩展元数据
    
    # 业务方法
    def calculate_age_hours() -> float          # 计算记忆年龄
    def get_importance_score() -> float         # 获取重要性评分
    def should_be_archived() -> bool           # 判断是否应归档
```

#### 2. MemoryProvider (存储提供者抽象)
```python
class MemoryProvider(ABC):
    """记忆存储提供者抽象基类"""
    
    # 连接管理
    def connect() -> None                       # 建立连接
    def disconnect() -> None                    # 断开连接
    def is_connected() -> bool                  # 检查连接状态
    
    # 基础CRUD操作
    def store(collection, record) -> str        # 存储记录
    def retrieve(collection, record_id) -> MemoryRecord  # 检索记录
    def update(collection, record_id, record) -> bool    # 更新记录
    def delete(collection, record_id) -> bool           # 删除记录
    
    # 高级查询操作
    def retrieve_recent(collection, limit, session_id) -> List[MemoryRecord]
    def search_by_embedding(collection, embedding, limit) -> List[MemoryRecord]
    def retrieve_by_session(collection, session_id) -> List[MemoryRecord]
```

#### 3. Retriever (记忆检索器)
```python
class Retriever:
    """语义记忆检索器"""
    
    def __init__(provider: MemoryProvider):
        self.provider = provider
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
    
    def retrieve_memories(
        query: str,
        collection: str = "conversations",
        limit: int = 3,
        include_context: bool = True,
        session_id: Optional[str] = None,
        min_score: float = 0.1
    ) -> List[MemoryRecord]:
        """使用统一信号计算检索相关记忆"""
```

#### 4. HistoryManager (会话历史管理器)
```python
class HistoryManager:
    """会话历史管理器"""
    
    def create_session(session_id: str) -> SessionInfo     # 创建会话
    def add_message(session_id, content, role) -> str      # 添加消息
    def get_session_history(session_id, limit) -> List[MemoryRecord]  # 获取历史
    def cleanup_session(session_id: str) -> int            # 清理会话
```

### 设计模式应用

- **抽象工厂模式**: MemoryProvider提供统一的存储接口抽象
- **策略模式**: 不同的记忆评估和归档策略
- **观察者模式**: 记忆事件的监听和处理
- **防腐层模式**: Provider作为领域模型与存储系统的隔离层

### 数据流和控制流

```
用户输入 → HistoryManager → MemoryRecord → MongoProvider → MongoDB
    ↓
语义检索 ← Retriever ← 嵌入生成 ← 查询处理
    ↓
记忆评估 → Evaluator → 信号计算 → 归档决策 → Archiver
```

## 使用示例 (Usage Examples)

### 基本记忆操作

```python
from xi_system.memory import (
    MemoryRecord, MessageRole, YU, XI,
    MongoProvider, Retriever, HistoryManager
)

# 1. 初始化存储提供者
provider = MongoProvider(
    uri="mongodb+srv://username:<EMAIL>/xi_memory",
    db_name="xi_memory"
)
provider.connect()

# 2. 创建和存储记忆
memory = MemoryRecord(
    content="今天学习了Python装饰器的高级用法",
    role=YU,
    source_session_id="session_001",
    metadata={"topic": "programming", "importance": 0.8}
)

record_id = provider.store("conversations", memory)
print(f"记忆已存储: {record_id}")

# 3. 检索相关记忆
retriever = Retriever(provider)
related_memories = retriever.retrieve_memories(
    query="Python编程学习",
    limit=5,
    min_score=0.3
)

for memory in related_memories:
    print(f"相关记忆: {memory.content[:50]}...")
```

### 会话历史管理

```python
# 1. 创建历史管理器
history_manager = HistoryManager(provider)

# 2. 管理会话对话
session_id = "chat_20241215_001"

# 添加用户消息
history_manager.add_message(
    session_id=session_id,
    content="请解释什么是机器学习？",
    role=YU
)

# 添加AI回复
history_manager.add_message(
    session_id=session_id,
    content="机器学习是人工智能的一个分支...",
    role=XI,
    metadata={"response_time": 1.2, "model": "gemini-2.5-flash"}
)

# 3. 获取会话历史
history = history_manager.get_session_history(
    session_id=session_id,
    limit=10
)

print(f"会话包含 {len(history)} 条消息")
```

### 智能记忆整理

```python
from xi_system.memory.curation import calculate_signal, Archiver

# 1. 评估记忆重要性
memory_record = provider.retrieve("conversations", record_id)
signal_score = calculate_signal(memory_record)

print(f"记忆信号强度: {signal_score:.3f}")

# 2. 执行记忆归档
archiver = Archiver(provider)
archived_count = archiver.archive_old_memories(
    collection="conversations",
    days_threshold=30,
    signal_threshold=0.1
)

print(f"已归档 {archived_count} 条记忆")
```

### 自定义存储提供者

```python
from xi_system.memory.providers import MemoryProvider, StorageError

class CustomProvider(MemoryProvider):
    """自定义存储提供者示例"""
    
    def __init__(self, config: dict):
        self.config = config
        self._connected = False
    
    def connect(self) -> None:
        # 实现连接逻辑
        try:
            # 连接到自定义存储系统
            self._connected = True
        except Exception as e:
            raise StorageError(f"连接失败: {e}")
    
    def store(self, collection_name: str, record: MemoryRecord) -> str:
        # 实现存储逻辑
        if not self._connected:
            raise StorageError("未连接到存储系统")
        
        # 转换记录格式并存储
        document = self._convert_to_storage_format(record)
        storage_id = self._save_to_storage(collection_name, document)
        
        return storage_id
    
    # 实现其他必需方法...
```

### 错误处理最佳实践

```python
from xi_system.memory.providers import StorageError
import logging

logger = logging.getLogger(__name__)

def safe_memory_operation():
    """安全的记忆操作示例"""
    provider = None
    try:
        # 1. 建立连接
        provider = MongoProvider(uri="mongodb+srv://username:<EMAIL>/xi_db", db_name="xi_db")
        provider.connect()
        
        # 2. 执行操作
        memory = MemoryRecord(content="测试记忆", role=YU)
        record_id = provider.store("test_collection", memory)
        
        # 3. 验证结果
        retrieved = provider.retrieve("test_collection", record_id)
        assert retrieved.content == memory.content
        
        logger.info(f"记忆操作成功: {record_id}")
        return record_id
        
    except StorageError as e:
        logger.error(f"存储错误: {e}")
        # 实现重试逻辑或降级处理
        return None
        
    except Exception as e:
        logger.error(f"未预期错误: {e}")
        raise
        
    finally:
        # 4. 清理资源
        if provider and provider.is_connected():
            provider.disconnect()

## API 参考 (API Reference)

### 核心模型类

#### MemoryRecord

```python
@dataclass
class MemoryRecord:
    """记忆记录领域模型"""

    # 构造函数
    def __init__(
        self,
        content: str,
        id: Optional[str] = None,
        timestamp: datetime = field(default_factory=datetime.utcnow),
        role: Optional[MessageRole] = None,
        embedding: Optional[List[float]] = None,
        source_session_id: Optional[str] = None,
        metadata: Dict[str, Any] = field(default_factory=dict)
    ) -> None

    # 业务方法
    def calculate_age_hours(self) -> float:
        """
        计算记忆的年龄（小时）

        Returns:
            float: 记忆年龄，单位为小时
        """

    def get_importance_score(self) -> float:
        """
        获取记忆重要性评分

        Returns:
            float: 重要性评分 (0.0-1.0)
        """

    def should_be_archived(self, threshold: float = 0.1) -> bool:
        """
        判断记忆是否应该被归档

        Args:
            threshold: 归档阈值

        Returns:
            bool: 是否应该归档
        """

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式

        Returns:
            Dict[str, Any]: 记忆记录的字典表示
        """

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryRecord':
        """
        从字典创建记忆记录

        Args:
            data: 包含记忆数据的字典

        Returns:
            MemoryRecord: 创建的记忆记录实例

        Raises:
            ValueError: 如果数据格式无效
        """
```

#### MessageRole

```python
@dataclass(frozen=True)
class MessageRole:
    """消息角色模型"""

    def __init__(self, internal_name: str, external_name: str) -> None:
        """
        创建消息角色

        Args:
            internal_name: 内部系统使用的角色名称
            external_name: 外部API使用的角色名称
        """

    def to_external(self) -> str:
        """
        转换为外部角色名称

        Returns:
            str: 外部角色名称
        """

    def is_system(self) -> bool:
        """
        判断是否为系统角色

        Returns:
            bool: 是否为系统角色
        """

    def is_user(self) -> bool:
        """
        判断是否为用户角色

        Returns:
            bool: 是否为用户角色
        """

    def is_assistant(self) -> bool:
        """
        判断是否为助手角色

        Returns:
            bool: 是否为助手角色
        """

# 预定义角色常量
YU: MessageRole          # 用户角色 (internal: yu, external: user)
XI: MessageRole          # 助手角色 (internal: xi, external: assistant)
XI_SYSTEM: MessageRole   # 系统角色 (internal: xi_system, external: system)
TOOL: MessageRole        # 工具角色 (internal: tool, external: tool)
```

### 存储提供者接口

#### MemoryProvider (抽象基类)

```python
class MemoryProvider(ABC):
    """记忆存储提供者抽象基类"""

    # 连接管理
    @abstractmethod
    def connect(self) -> None:
        """
        建立与存储系统的连接

        Raises:
            StorageError: 如果连接失败
        """

    @abstractmethod
    def disconnect(self) -> None:
        """断开与存储系统的连接"""

    @abstractmethod
    def is_connected(self) -> bool:
        """
        检查是否已连接到存储系统

        Returns:
            bool: 连接状态
        """

    # 基础CRUD操作
    @abstractmethod
    def store(self, collection_name: str, record: MemoryRecord) -> str:
        """
        存储记忆记录

        Args:
            collection_name: 集合名称
            record: 要存储的记忆记录

        Returns:
            str: 存储系统分配的唯一标识符

        Raises:
            StorageError: 如果存储操作失败
        """

    @abstractmethod
    def retrieve(self, collection_name: str, record_id: str) -> Optional[MemoryRecord]:
        """
        检索指定记录

        Args:
            collection_name: 集合名称
            record_id: 记录标识符

        Returns:
            Optional[MemoryRecord]: 检索到的记录，如果不存在则返回None

        Raises:
            StorageError: 如果检索操作失败
        """

    @abstractmethod
    def update(self, collection_name: str, record_id: str, record: MemoryRecord) -> bool:
        """
        更新记录

        Args:
            collection_name: 集合名称
            record_id: 记录标识符
            record: 更新后的记录

        Returns:
            bool: 更新是否成功

        Raises:
            StorageError: 如果更新操作失败
        """

    @abstractmethod
    def delete(self, collection_name: str, record_id: str) -> bool:
        """
        删除记录

        Args:
            collection_name: 集合名称
            record_id: 记录标识符

        Returns:
            bool: 删除是否成功

        Raises:
            StorageError: 如果删除操作失败
        """

    # 高级查询操作
    @abstractmethod
    def retrieve_recent(
        self,
        collection_name: str,
        limit: int = 10,
        session_id: Optional[str] = None
    ) -> List[MemoryRecord]:
        """
        检索最近的记录

        Args:
            collection_name: 集合名称
            limit: 返回记录的最大数量
            session_id: 可选的会话ID过滤器

        Returns:
            List[MemoryRecord]: 按时间倒序排列的记录列表

        Raises:
            StorageError: 如果检索操作失败
        """

    @abstractmethod
    def search_by_embedding(
        self,
        collection_name: str,
        embedding: List[float],
        limit: int = 10,
        min_score: float = 0.0
    ) -> List[MemoryRecord]:
        """
        基于嵌入向量的语义搜索

        Args:
            collection_name: 集合名称
            embedding: 查询向量
            limit: 返回记录的最大数量
            min_score: 最小相似度分数

        Returns:
            List[MemoryRecord]: 按相似度排序的记录列表

        Raises:
            StorageError: 如果搜索操作失败
        """

#### MongoProvider (MongoDB实现)

```python
class MongoProvider(MemoryProvider):
    """MongoDB记忆提供者实现"""

    def __init__(
        self,
        uri: str,
        db_name: str,
        timeout: int = 30,
        embedding_service=None
    ) -> None:
        """
        初始化MongoDB提供者

        Args:
            uri: MongoDB连接URI
            db_name: 数据库名称
            timeout: 连接超时时间（秒）
            embedding_service: 嵌入服务实例
        """

    def create_vector_index(self, collection_name: str) -> None:
        """
        为集合创建向量搜索索引

        Args:
            collection_name: 集合名称

        Raises:
            StorageError: 如果索引创建失败
        """
```

### 检索系统

#### Retriever

```python
class Retriever:
    """语义记忆检索器"""

    def __init__(self, provider: MemoryProvider) -> None:
        """
        初始化检索器

        Args:
            provider: 记忆存储提供者
        """

    def retrieve_memories(
        self,
        query: str,
        collection: str = "conversation_memory",
        limit: int = 3,
        include_context: bool = True,
        session_id: Optional[str] = None,
        min_score: float = 0.1
    ) -> List[MemoryRecord]:
        """
        检索相关记忆

        Args:
            query: 查询文本
            collection: 集合名称
            limit: 返回记录的最大数量
            include_context: 是否包含上下文记忆
            session_id: 可选的会话ID过滤器
            min_score: 最小相关性分数阈值

        Returns:
            List[MemoryRecord]: 按相关性排序的记忆记录列表

        Raises:
            StorageError: 如果检索操作失败
        """

    def get_context_memories(
        self,
        target_memory: MemoryRecord,
        collection: str,
        context_window: int = 2
    ) -> List[MemoryRecord]:
        """
        获取目标记忆的上下文记忆

        Args:
            target_memory: 目标记忆记录
            collection: 集合名称
            context_window: 上下文窗口大小

        Returns:
            List[MemoryRecord]: 上下文记忆列表
        """
```

### 会话管理

#### HistoryManager

```python
class HistoryManager:
    """会话历史管理器"""

    def __init__(
        self,
        provider: MemoryProvider,
        collection: str = "conversation_memory"
    ) -> None:
        """
        初始化历史管理器

        Args:
            provider: 记忆提供者
            collection: 存储集合名称
        """

    def create_session(self, session_id: str) -> SessionInfo:
        """
        创建新会话

        Args:
            session_id: 会话标识符

        Returns:
            SessionInfo: 会话信息对象
        """

    def add_message(
        self,
        session_id: str,
        content: str,
        role: MessageRole,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        向会话添加消息

        Args:
            session_id: 会话ID
            content: 消息内容
            role: 消息角色
            metadata: 可选的消息元数据

        Returns:
            str: 消息记录ID

        Raises:
            StorageError: 如果存储失败
        """

    def get_session_history(
        self,
        session_id: str,
        limit: Optional[int] = None,
        include_system: bool = True
    ) -> List[MemoryRecord]:
        """
        获取会话历史记录

        Args:
            session_id: 会话ID
            limit: 可选的记录数量限制
            include_system: 是否包含系统消息

        Returns:
            List[MemoryRecord]: 会话历史记录列表，按时间正序排列
        """

    def cleanup_session(self, session_id: str) -> int:
        """
        清理会话数据

        Args:
            session_id: 会话ID

        Returns:
            int: 清理的记录数量
        """

    def get_session_stats(self, session_id: str) -> Dict[str, Any]:
        """
        获取会话统计信息

        Args:
            session_id: 会话ID

        Returns:
            Dict[str, Any]: 包含消息数量、时间范围等统计信息
        """

#### MessageFormatter

```python
class MessageFormatter:
    """消息格式化器"""

    def __init__(self, history_manager: HistoryManager) -> None:
        """
        初始化消息格式化器

        Args:
            history_manager: 历史管理器实例
        """

    def format_for_llm(
        self,
        session_id: str,
        include_system: bool = True,
        max_messages: int = 20
    ) -> List[Dict[str, str]]:
        """
        格式化消息用于LLM API调用

        Args:
            session_id: 会话ID
            include_system: 是否包含系统消息
            max_messages: 最大消息数量

        Returns:
            List[Dict[str, str]]: 格式化的消息列表
        """

    def format_for_display(
        self,
        session_id: str,
        limit: Optional[int] = None
    ) -> str:
        """
        格式化消息用于显示

        Args:
            session_id: 会话ID
            limit: 可选的消息数量限制

        Returns:
            str: 格式化的消息文本
        """

def get_message_formatter(provider: MemoryProvider) -> MessageFormatter:
    """
    获取消息格式化器实例

    Args:
        provider: 记忆提供者

    Returns:
        MessageFormatter: 消息格式化器实例
    """
```

### 记忆整理系统

#### 评估函数

```python
def calculate_signal(memory: MemoryRecord) -> float:
    """
    计算记忆的信号强度

    结合时间衰减、重要性评分和内容质量，计算记忆的综合信号强度。

    Args:
        memory: 记忆记录

    Returns:
        float: 信号强度 (0.0-1.0)
    """

def should_archive_memory(memory: MemoryRecord, threshold: float = 0.1) -> bool:
    """
    判断记忆是否应该归档

    Args:
        memory: 记忆记录
        threshold: 归档阈值

    Returns:
        bool: 是否应该归档
    """

def get_signal_breakdown(memory: MemoryRecord) -> Dict[str, float]:
    """
    获取信号计算的详细分解

    Args:
        memory: 记忆记录

    Returns:
        Dict[str, float]: 包含各项评分的详细分解
    """
```

#### Archiver

```python
class Archiver:
    """记忆归档器"""

    def __init__(self, provider: MemoryProvider) -> None:
        """
        初始化归档器

        Args:
            provider: 记忆存储提供者
        """

    def archive_old_memories(
        self,
        collection: str = "conversation_memory",
        days_threshold: int = 30,
        signal_threshold: float = 0.1
    ) -> int:
        """
        归档旧记忆

        Args:
            collection: 集合名称
            days_threshold: 天数阈值
            signal_threshold: 信号强度阈值

        Returns:
            int: 归档的记录数量
        """

    def restore_memory(self, memory_id: str) -> bool:
        """
        恢复已归档的记忆

        Args:
            memory_id: 记忆ID

        Returns:
            bool: 恢复是否成功
        """

### 异常类

```python
class StorageError(Exception):
    """存储操作异常"""

    def __init__(self, message: str, cause: Optional[Exception] = None) -> None:
        """
        初始化存储异常

        Args:
            message: 错误消息
            cause: 原始异常（可选）
        """
        super().__init__(message)
        self.cause = cause
```

## 系统集成 (Integration Guide)

### 与 ServiceContainer 的集成

Xi 记忆系统通过 ServiceContainer 进行依赖注入和生命周期管理：

```python
from xi_system.service import ServiceContainer
from xi_system.memory import MongoProvider, Retriever, HistoryManager

# 1. 在服务容器中注册记忆组件
def setup_memory_services(container: ServiceContainer) -> None:
    """设置记忆系统服务"""

    # 注册存储提供者
    container.register_singleton(
        'memory_provider',
        lambda: MongoProvider(
            uri=container.get_config('MONGODB_URI'),
            db_name=container.get_config('MONGODB_DB_NAME', 'xi_memory')
        )
    )

    # 注册检索器
    container.register_singleton(
        'memory_retriever',
        lambda: Retriever(container.get('memory_provider'))
    )

    # 注册历史管理器
    container.register_singleton(
        'history_manager',
        lambda: HistoryManager(container.get('memory_provider'))
    )

    # 注册消息格式化器
    container.register_singleton(
        'message_formatter',
        lambda: MessageFormatter(container.get('history_manager'))
    )

# 2. 在应用启动时初始化
async def initialize_memory_system(container: ServiceContainer) -> None:
    """初始化记忆系统"""

    # 获取提供者并建立连接
    provider = container.get('memory_provider')
    provider.connect()

    # 创建必要的索引
    if isinstance(provider, MongoProvider):
        provider.create_vector_index('conversation_memory')
        provider.create_vector_index('archived_memories')

    # 验证系统状态
    assert provider.is_connected(), "记忆系统连接失败"

    print("记忆系统初始化完成")

### 与其他 Xi 模块的协作

#### 与 Agents 模块的集成

```python
from xi_system.agents import AgenticLoopProcessor
from xi_system.memory import HistoryManager, Retriever

class MemoryAwareAgent(AgenticLoopProcessor):
    """集成记忆功能的智能体"""

    def __init__(
        self,
        history_manager: HistoryManager,
        retriever: Retriever,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.history_manager = history_manager
        self.retriever = retriever

    async def process_with_memory(
        self,
        session_id: str,
        user_input: str
    ) -> str:
        """带记忆的处理流程"""

        # 1. 检索相关记忆
        relevant_memories = self.retriever.retrieve_memories(
            query=user_input,
            session_id=session_id,
            limit=5
        )

        # 2. 构建包含记忆的上下文
        context = self._build_context_with_memories(
            user_input, relevant_memories
        )

        # 3. 处理请求
        response = await self.process(context)

        # 4. 存储对话历史
        self.history_manager.add_message(session_id, user_input, YU)
        self.history_manager.add_message(session_id, response, XI)

        return response
```

#### 与 Tools 模块的集成

```python
from xi_system.tools import ToolRegistry, ToolExecutor
from xi_system.memory import MemoryRecord, MongoProvider

# 注册记忆相关工具
def register_memory_tools(
    registry: ToolRegistry,
    provider: MongoProvider
) -> None:
    """注册记忆管理工具"""

    @registry.register_tool("search_memory")
    async def search_memory_tool(query: str, limit: int = 5) -> List[Dict]:
        """搜索记忆工具"""
        retriever = Retriever(provider)
        memories = retriever.retrieve_memories(query, limit=limit)

        return [
            {
                "content": mem.content[:200],
                "timestamp": mem.timestamp.isoformat(),
                "role": mem.role.internal_name if mem.role else "unknown"
            }
            for mem in memories
        ]

    @registry.register_tool("save_note")
    async def save_note_tool(title: str, content: str) -> str:
        """保存笔记工具"""
        note_record = MemoryRecord(
            content=f"# {title}\n\n{content}",
            role=XI_SYSTEM,
            metadata={"type": "note", "title": title}
        )

        record_id = provider.store("notes", note_record)
        return f"笔记已保存: {record_id}"
```

#### 与 Prompts 模块的集成

```python
from xi_system.prompts import StructuredPromptBuilder
from xi_system.memory import MessageFormatter

class MemoryEnhancedPromptBuilder(StructuredPromptBuilder):
    """集成记忆的提示词构建器"""

    def __init__(self, formatter: MessageFormatter):
        super().__init__()
        self.formatter = formatter

    def build_with_memory(
        self,
        session_id: str,
        system_prompt: str,
        include_history: bool = True
    ) -> str:
        """构建包含记忆的提示词"""

        prompt_parts = [system_prompt]

        if include_history:
            # 获取格式化的历史消息
            history_text = self.formatter.format_for_display(
                session_id=session_id,
                limit=10
            )

            if history_text:
                prompt_parts.append(f"\n## 对话历史\n{history_text}")

        return "\n".join(prompt_parts)
```

### 事件系统集成

```python
from typing import Protocol
from xi_system.memory import MemoryRecord

class MemoryEventListener(Protocol):
    """记忆事件监听器协议"""

    def on_memory_stored(self, record: MemoryRecord) -> None:
        """记忆存储事件"""
        ...

    def on_memory_retrieved(self, query: str, results: List[MemoryRecord]) -> None:
        """记忆检索事件"""
        ...

    def on_memory_archived(self, record: MemoryRecord) -> None:
        """记忆归档事件"""
        ...

class EventAwareMemoryProvider(MongoProvider):
    """支持事件的记忆提供者"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.listeners: List[MemoryEventListener] = []

    def add_listener(self, listener: MemoryEventListener) -> None:
        """添加事件监听器"""
        self.listeners.append(listener)

    def store(self, collection_name: str, record: MemoryRecord) -> str:
        """存储记录并触发事件"""
        record_id = super().store(collection_name, record)

        # 触发存储事件
        for listener in self.listeners:
            listener.on_memory_stored(record)

        return record_id
```

### 依赖关系和初始化顺序

```python
# 推荐的初始化顺序
async def initialize_xi_system() -> ServiceContainer:
    """初始化Xi系统的推荐顺序"""

    container = ServiceContainer()

    # 1. 基础设施层
    setup_database_services(container)

    # 2. 记忆系统（依赖数据库）
    setup_memory_services(container)
    await initialize_memory_system(container)

    # 3. 工具系统
    setup_tool_services(container)
    register_memory_tools(
        container.get('tool_registry'),
        container.get('memory_provider')
    )

    # 4. 智能体系统（依赖记忆和工具）
    setup_agent_services(container)

    # 5. 核心业务层
    setup_core_services(container)

    return container
```

## 扩展指南 (Extension Guide)

### 自定义存储提供者

创建自定义存储提供者需要继承 `MemoryProvider` 抽象基类：

```python
from xi_system.memory.providers import MemoryProvider, StorageError
import redis
import json
from typing import List, Optional

class RedisMemoryProvider(MemoryProvider):
    """Redis记忆提供者示例"""

    def __init__(self, host: str = 'localhost', port: int = 6379, db: int = 0):
        self.host = host
        self.port = port
        self.db = db
        self.client: Optional[redis.Redis] = None
        self._connected = False

    def connect(self) -> None:
        """建立Redis连接"""
        try:
            self.client = redis.Redis(
                host=self.host,
                port=self.port,
                db=self.db,
                decode_responses=True
            )
            # 测试连接
            self.client.ping()
            self._connected = True
        except Exception as e:
            raise StorageError(f"Redis连接失败: {e}")

    def disconnect(self) -> None:
        """断开连接"""
        if self.client:
            self.client.close()
        self._connected = False

    def is_connected(self) -> bool:
        """检查连接状态"""
        return self._connected and self.client is not None

    def store(self, collection_name: str, record: MemoryRecord) -> str:
        """存储记录到Redis"""
        if not self.is_connected():
            raise StorageError("未连接到Redis")

        # 生成唯一ID
        record_id = f"{collection_name}:{uuid.uuid4()}"

        # 序列化记录
        record_data = {
            'id': record_id,
            'content': record.content,
            'timestamp': record.timestamp.isoformat(),
            'role': record.role.internal_name if record.role else None,
            'source_session_id': record.source_session_id,
            'metadata': record.metadata
        }

        # 存储到Redis
        key = f"memory:{record_id}"
        self.client.hset(key, mapping=record_data)

        # 添加到集合索引
        self.client.sadd(f"collection:{collection_name}", record_id)

        # 添加时间索引
        self.client.zadd(
            f"timeline:{collection_name}",
            {record_id: record.timestamp.timestamp()}
        )

        return record_id

    def retrieve(self, collection_name: str, record_id: str) -> Optional[MemoryRecord]:
        """从Redis检索记录"""
        if not self.is_connected():
            raise StorageError("未连接到Redis")

        key = f"memory:{record_id}"
        data = self.client.hgetall(key)

        if not data:
            return None

        return self._deserialize_record(data)

    def retrieve_recent(
        self,
        collection_name: str,
        limit: int = 10,
        session_id: Optional[str] = None
    ) -> List[MemoryRecord]:
        """检索最近记录"""
        if not self.is_connected():
            raise StorageError("未连接到Redis")

        # 从时间索引获取最近的记录ID
        timeline_key = f"timeline:{collection_name}"
        record_ids = self.client.zrevrange(timeline_key, 0, limit - 1)

        records = []
        for record_id in record_ids:
            record = self.retrieve(collection_name, record_id)
            if record and (not session_id or record.source_session_id == session_id):
                records.append(record)

        return records

    def _deserialize_record(self, data: dict) -> MemoryRecord:
        """反序列化记录"""
        from datetime import datetime
        from xi_system.memory.models import create_role

        role = None
        if data.get('role'):
            role = create_role(data['role'])

        return MemoryRecord(
            id=data['id'],
            content=data['content'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            role=role,
            source_session_id=data.get('source_session_id'),
            metadata=json.loads(data.get('metadata', '{}'))
        )

### 自定义记忆评估策略

扩展记忆评估系统以支持自定义评估策略：

```python
from abc import ABC, abstractmethod
from xi_system.memory import MemoryRecord

class MemoryEvaluationStrategy(ABC):
    """记忆评估策略抽象基类"""

    @abstractmethod
    def calculate_importance(self, memory: MemoryRecord) -> float:
        """
        计算记忆重要性

        Args:
            memory: 记忆记录

        Returns:
            float: 重要性评分 (0.0-1.0)
        """
        pass

    @abstractmethod
    def calculate_decay(self, memory: MemoryRecord) -> float:
        """
        计算时间衰减因子

        Args:
            memory: 记忆记录

        Returns:
            float: 衰减因子 (0.0-1.0)
        """
        pass

class ContentBasedEvaluationStrategy(MemoryEvaluationStrategy):
    """基于内容的评估策略"""

    def __init__(self, keywords: List[str], weights: Dict[str, float]):
        self.keywords = keywords
        self.weights = weights

    def calculate_importance(self, memory: MemoryRecord) -> float:
        """基于关键词匹配计算重要性"""
        content_lower = memory.content.lower()
        score = 0.0

        for keyword in self.keywords:
            if keyword.lower() in content_lower:
                score += self.weights.get(keyword, 0.1)

        # 考虑内容长度
        length_factor = min(len(memory.content) / 1000, 1.0)
        score *= (0.5 + 0.5 * length_factor)

        return min(score, 1.0)

    def calculate_decay(self, memory: MemoryRecord) -> float:
        """计算时间衰减"""
        age_hours = memory.calculate_age_hours()

        # 指数衰减，半衰期为7天
        half_life_hours = 7 * 24
        decay_factor = 0.5 ** (age_hours / half_life_hours)

        return decay_factor

class InteractionBasedEvaluationStrategy(MemoryEvaluationStrategy):
    """基于交互的评估策略"""

    def calculate_importance(self, memory: MemoryRecord) -> float:
        """基于交互频率计算重要性"""
        metadata = memory.metadata

        # 获取交互统计
        view_count = metadata.get('view_count', 0)
        reference_count = metadata.get('reference_count', 0)
        user_rating = metadata.get('user_rating', 0.5)

        # 综合评分
        interaction_score = (
            0.3 * min(view_count / 10, 1.0) +
            0.4 * min(reference_count / 5, 1.0) +
            0.3 * user_rating
        )

        return interaction_score

    def calculate_decay(self, memory: MemoryRecord) -> float:
        """基于最后访问时间的衰减"""
        last_accessed = memory.metadata.get('last_accessed')
        if not last_accessed:
            return self._default_decay(memory)

        from datetime import datetime
        last_access_time = datetime.fromisoformat(last_accessed)
        hours_since_access = (datetime.utcnow() - last_access_time).total_seconds() / 3600

        # 最近访问的记忆衰减更慢
        decay_factor = 0.5 ** (hours_since_access / (14 * 24))  # 14天半衰期
        return decay_factor

# 使用自定义评估策略
class CustomEvaluator:
    """自定义记忆评估器"""

    def __init__(self, strategy: MemoryEvaluationStrategy):
        self.strategy = strategy

    def evaluate_memory(self, memory: MemoryRecord) -> float:
        """评估记忆信号强度"""
        importance = self.strategy.calculate_importance(memory)
        decay = self.strategy.calculate_decay(memory)

        # 综合信号强度
        signal = importance * decay

        # 更新记忆元数据
        memory.metadata['last_evaluated'] = datetime.utcnow().isoformat()
        memory.metadata['signal_score'] = signal
        memory.metadata['importance_score'] = importance
        memory.metadata['decay_factor'] = decay

        return signal
```

### 自定义检索算法

扩展检索系统以支持自定义检索算法：

```python
from typing import Callable, Tuple
import numpy as np

class CustomRetriever(Retriever):
    """支持自定义算法的检索器"""

    def __init__(
        self,
        provider: MemoryProvider,
        similarity_function: Optional[Callable] = None,
        ranking_function: Optional[Callable] = None
    ):
        super().__init__(provider)
        self.similarity_function = similarity_function or self._cosine_similarity
        self.ranking_function = ranking_function or self._default_ranking

    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """余弦相似度计算"""
        a = np.array(vec1)
        b = np.array(vec2)
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))

    def _jaccard_similarity(self, text1: str, text2: str) -> float:
        """Jaccard相似度计算"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0

    def _default_ranking(
        self,
        memories: List[Tuple[MemoryRecord, float]],
        query: str
    ) -> List[Tuple[MemoryRecord, float]]:
        """默认排序算法"""
        return sorted(memories, key=lambda x: x[1], reverse=True)

    def _time_aware_ranking(
        self,
        memories: List[Tuple[MemoryRecord, float]],
        query: str
    ) -> List[Tuple[MemoryRecord, float]]:
        """时间感知排序算法"""
        def combined_score(item):
            memory, similarity = item
            age_hours = memory.calculate_age_hours()

            # 时间权重：最近的记忆获得更高权重
            time_weight = 1.0 / (1.0 + age_hours / 24)  # 24小时为基准

            return similarity * 0.7 + time_weight * 0.3

        return sorted(memories, key=combined_score, reverse=True)

    def retrieve_with_custom_algorithm(
        self,
        query: str,
        collection: str = "conversations",
        limit: int = 3,
        algorithm: str = "default"
    ) -> List[MemoryRecord]:
        """使用自定义算法检索记忆"""

        # 获取候选记忆
        candidates = self.provider.retrieve_recent(collection, limit=100)

        # 计算相似度
        scored_memories = []
        for memory in candidates:
            if memory.embedding:
                query_embedding = self.embedding_model.encode([query])[0]
                similarity = self.similarity_function(query_embedding, memory.embedding)
            else:
                # 回退到文本相似度
                similarity = self._jaccard_similarity(query, memory.content)

            scored_memories.append((memory, similarity))

        # 选择排序算法
        if algorithm == "time_aware":
            ranked_memories = self._time_aware_ranking(scored_memories, query)
        else:
            ranked_memories = self.ranking_function(scored_memories, query)

        # 返回前N个结果
        return [memory for memory, score in ranked_memories[:limit]]
```

### 插件机制

实现插件系统以支持动态扩展：

```python
from typing import Dict, Type
import importlib
import inspect

class MemoryPlugin(ABC):
    """记忆系统插件基类"""

    @property
    @abstractmethod
    def name(self) -> str:
        """插件名称"""
        pass

    @property
    @abstractmethod
    def version(self) -> str:
        """插件版本"""
        pass

    @abstractmethod
    def initialize(self, memory_system: 'MemorySystem') -> None:
        """初始化插件"""
        pass

    @abstractmethod
    def cleanup(self) -> None:
        """清理插件资源"""
        pass

class MemoryPluginManager:
    """记忆系统插件管理器"""

    def __init__(self):
        self.plugins: Dict[str, MemoryPlugin] = {}
        self.plugin_registry: Dict[str, Type[MemoryPlugin]] = {}

    def register_plugin(self, plugin_class: Type[MemoryPlugin]) -> None:
        """注册插件类"""
        plugin_name = plugin_class.name
        self.plugin_registry[plugin_name] = plugin_class

    def load_plugin(self, plugin_name: str, memory_system: 'MemorySystem') -> None:
        """加载插件"""
        if plugin_name in self.plugins:
            return  # 已加载

        if plugin_name not in self.plugin_registry:
            raise ValueError(f"未知插件: {plugin_name}")

        plugin_class = self.plugin_registry[plugin_name]
        plugin_instance = plugin_class()

        plugin_instance.initialize(memory_system)
        self.plugins[plugin_name] = plugin_instance

    def unload_plugin(self, plugin_name: str) -> None:
        """卸载插件"""
        if plugin_name in self.plugins:
            plugin = self.plugins[plugin_name]
            plugin.cleanup()
            del self.plugins[plugin_name]

    def load_plugins_from_directory(self, directory: str) -> None:
        """从目录加载插件"""
        import os
        import sys

        for filename in os.listdir(directory):
            if filename.endswith('.py') and not filename.startswith('_'):
                module_name = filename[:-3]
                module_path = os.path.join(directory, filename)

                spec = importlib.util.spec_from_file_location(module_name, module_path)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)

                # 查找插件类
                for name, obj in inspect.getmembers(module):
                    if (inspect.isclass(obj) and
                        issubclass(obj, MemoryPlugin) and
                        obj != MemoryPlugin):
                        self.register_plugin(obj)

# 示例插件
class StatisticsPlugin(MemoryPlugin):
    """记忆统计插件"""

    @property
    def name(self) -> str:
        return "statistics"

    @property
    def version(self) -> str:
        return "1.0.0"

    def initialize(self, memory_system: 'MemorySystem') -> None:
        """初始化统计功能"""
        self.memory_system = memory_system
        self.stats = {
            'total_memories': 0,
            'memories_by_role': {},
            'average_signal_score': 0.0
        }

        # 注册事件监听器
        if hasattr(memory_system.provider, 'add_listener'):
            memory_system.provider.add_listener(self)

    def on_memory_stored(self, record: MemoryRecord) -> None:
        """更新存储统计"""
        self.stats['total_memories'] += 1

        role_name = record.role.internal_name if record.role else 'unknown'
        self.stats['memories_by_role'][role_name] = (
            self.stats['memories_by_role'].get(role_name, 0) + 1
        )

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()

    def cleanup(self) -> None:
        """清理资源"""
        self.stats.clear()

## 配置选项 (Configuration Options)

### 环境变量配置

Xi 记忆系统支持通过环境变量进行配置：

```bash
# MongoDB Atlas 配置
MONGODB_URI=mongodb+srv://username:<EMAIL>/xi_memory  # MongoDB Atlas连接URI
MONGODB_DB_NAME=xi_memory                      # 数据库名称
MONGODB_TIMEOUT=30                             # 连接超时时间（秒）

# 嵌入模型配置
EMBEDDING_MODEL=all-MiniLM-L6-v2              # 嵌入模型名称
EMBEDDING_CACHE_SIZE=1000                     # 嵌入缓存大小
EMBEDDING_BATCH_SIZE=32                       # 批处理大小

# 记忆管理配置
MEMORY_COLLECTION=conversations                # 默认记忆集合
ARCHIVE_COLLECTION=archived_memories          # 归档集合名称
NOTES_COLLECTION=notes                        # 笔记集合名称

# 检索配置
DEFAULT_RETRIEVAL_LIMIT=3                     # 默认检索数量限制
MIN_SIMILARITY_SCORE=0.1                     # 最小相似度阈值
CONTEXT_WINDOW_SIZE=2                        # 上下文窗口大小

# 归档配置
ARCHIVE_THRESHOLD_DAYS=30                     # 归档阈值天数
ARCHIVE_SIGNAL_THRESHOLD=0.1                 # 归档信号阈值
AUTO_ARCHIVE_ENABLED=true                    # 是否启用自动归档

# 性能配置
MAX_MEMORY_CACHE_SIZE=10000                  # 最大内存缓存大小
BATCH_OPERATION_SIZE=100                     # 批量操作大小
CONNECTION_POOL_SIZE=10                      # 连接池大小

# 日志配置
MEMORY_LOG_LEVEL=INFO                        # 日志级别
MEMORY_LOG_FILE=logs/memory.log              # 日志文件路径
```

### 代码配置示例

#### 基础配置

```python
from xi_system.memory import MongoProvider, Retriever, HistoryManager
import os

# 1. 基础MongoDB配置
def create_memory_provider() -> MongoProvider:
    """创建配置好的记忆提供者"""

    config = {
        'uri': os.getenv('MONGODB_URI', 'mongodb+srv://username:<EMAIL>/xi_memory'),
        'db_name': os.getenv('MONGODB_DB_NAME', 'xi_memory'),
        'embedding_model': os.getenv('EMBEDDING_MODEL', 'all-MiniLM-L6-v2'),
        'timeout': int(os.getenv('MONGODB_TIMEOUT', '30'))
    }

    provider = MongoProvider(**config)
    return provider

# 2. 检索器配置
def create_retriever(provider: MongoProvider) -> Retriever:
    """创建配置好的检索器"""

    retriever = Retriever(provider)

    # 配置检索参数
    retriever.default_limit = int(os.getenv('DEFAULT_RETRIEVAL_LIMIT', '3'))
    retriever.min_score = float(os.getenv('MIN_SIMILARITY_SCORE', '0.1'))
    retriever.context_window = int(os.getenv('CONTEXT_WINDOW_SIZE', '2'))

    return retriever

# 3. 历史管理器配置
def create_history_manager(provider: MongoProvider) -> HistoryManager:
    """创建配置好的历史管理器"""

    collection = os.getenv('MEMORY_COLLECTION', 'conversation_memory')
    return HistoryManager(provider, collection)
```

#### 高级配置

```python
from dataclasses import dataclass
from typing import Optional

@dataclass
class MemorySystemConfig:
    """记忆系统配置类"""

    # MongoDB Atlas配置
    mongodb_uri: str = "mongodb+srv://username:<EMAIL>/xi_memory"
    mongodb_db_name: str = "xi_memory"
    mongodb_timeout: int = 30
    connection_pool_size: int = 10

    # 嵌入配置
    embedding_model: str = "all-MiniLM-L6-v2"
    embedding_cache_size: int = 1000
    embedding_batch_size: int = 32

    # 集合配置
    memory_collection: str = "conversations"
    archive_collection: str = "archived_memories"
    notes_collection: str = "notes"

    # 检索配置
    default_retrieval_limit: int = 3
    min_similarity_score: float = 0.1
    context_window_size: int = 2
    max_search_results: int = 100

    # 归档配置
    archive_threshold_days: int = 30
    archive_signal_threshold: float = 0.1
    auto_archive_enabled: bool = True
    archive_batch_size: int = 100

    # 性能配置
    max_memory_cache_size: int = 10000
    batch_operation_size: int = 100
    enable_async_operations: bool = True

    # 安全配置
    enable_encryption: bool = False
    encryption_key: Optional[str] = None

    @classmethod
    def from_env(cls) -> 'MemorySystemConfig':
        """从环境变量创建配置"""
        return cls(
            mongodb_uri=os.getenv('MONGODB_URI', cls.mongodb_uri),
            mongodb_db_name=os.getenv('MONGODB_DB_NAME', cls.mongodb_db_name),
            mongodb_timeout=int(os.getenv('MONGODB_TIMEOUT', str(cls.mongodb_timeout))),
            embedding_model=os.getenv('EMBEDDING_MODEL', cls.embedding_model),
            memory_collection=os.getenv('MEMORY_COLLECTION', cls.memory_collection),
            # ... 其他配置项
        )

    def validate(self) -> None:
        """验证配置有效性"""
        if not self.mongodb_uri:
            raise ValueError("MongoDB URI不能为空")

        if self.mongodb_timeout <= 0:
            raise ValueError("MongoDB超时时间必须大于0")

        if self.default_retrieval_limit <= 0:
            raise ValueError("检索限制必须大于0")

        if not 0 <= self.min_similarity_score <= 1:
            raise ValueError("相似度阈值必须在0-1之间")

# 使用配置类
def initialize_memory_system_with_config() -> tuple:
    """使用配置类初始化记忆系统"""

    # 加载和验证配置
    config = MemorySystemConfig.from_env()
    config.validate()

    # 创建组件
    provider = MongoProvider(
        uri=config.mongodb_uri,
        db_name=config.mongodb_db_name,
        embedding_model=config.embedding_model,
        timeout=config.mongodb_timeout
    )

    retriever = Retriever(provider)
    history_manager = HistoryManager(provider, config.memory_collection)

    return provider, retriever, history_manager, config
```

### 开发环境配置

```python
# development.py
DEVELOPMENT_CONFIG = {
    'mongodb_uri': 'mongodb+srv://username:<EMAIL>/xi_memory_dev',
    'mongodb_db_name': 'xi_memory_dev',
    'embedding_model': 'all-MiniLM-L6-v2',
    'log_level': 'DEBUG',
    'enable_debug_logging': True,
    'auto_archive_enabled': False,  # 开发时禁用自动归档
    'max_memory_cache_size': 1000,  # 较小的缓存
}
```

### 测试环境配置

```python
# testing.py
TESTING_CONFIG = {
    'mongodb_uri': 'mongodb+srv://username:<EMAIL>/xi_memory_test',
    'mongodb_db_name': 'xi_memory_test',
    'embedding_model': 'all-MiniLM-L6-v2',
    'log_level': 'WARNING',
    'enable_debug_logging': False,
    'auto_archive_enabled': False,
    'use_in_memory_cache': True,  # 测试时使用内存缓存
    'cleanup_after_test': True,   # 测试后清理数据
}
```

### 生产环境配置

```python
# production.py
PRODUCTION_CONFIG = {
    'mongodb_uri': 'mongodb+srv://user:<EMAIL>/',
    'mongodb_db_name': 'xi_memory_prod',
    'embedding_model': 'all-MiniLM-L6-v2',
    'mongodb_timeout': 60,
    'connection_pool_size': 20,
    'log_level': 'INFO',
    'enable_debug_logging': False,
    'auto_archive_enabled': True,
    'archive_threshold_days': 90,  # 生产环境更长的保留期
    'max_memory_cache_size': 50000,
    'enable_encryption': True,
    'backup_enabled': True,
    'monitoring_enabled': True,
}
```

### 配置验证和错误处理

```python
class ConfigurationError(Exception):
    """配置错误异常"""
    pass

def validate_memory_configuration(config: dict) -> None:
    """验证记忆系统配置"""

    required_fields = ['mongodb_uri', 'mongodb_db_name']
    for field in required_fields:
        if not config.get(field):
            raise ConfigurationError(f"必需配置项缺失: {field}")

    # 验证数值范围
    if config.get('mongodb_timeout', 30) <= 0:
        raise ConfigurationError("MongoDB超时时间必须大于0")

    if config.get('default_retrieval_limit', 3) <= 0:
        raise ConfigurationError("检索限制必须大于0")

    # 验证嵌入服务配置
    embedding_provider = config.get('embedding_provider', 'local')
    if embedding_provider not in ['local', 'openai']:
        raise ConfigurationError(f"不支持的嵌入提供商: {embedding_provider}")

    if embedding_provider == 'openai' and not config.get('openai_api_key'):
        raise ConfigurationError("使用OpenAI提供商时必须配置OPENAI_API_KEY")

    print("配置验证通过")

# 使用示例
try:
    config = MemorySystemConfig.from_env()
    validate_memory_configuration(config.__dict__)
    print("记忆系统配置有效")
except ConfigurationError as e:
    print(f"配置错误: {e}")
    sys.exit(1)

## 故障排除 (Troubleshooting)

### 常见错误及解决方案

#### 1. MongoDB连接问题

**错误信息**:
```
StorageError: MongoDB connection failed: [Errno 111] Connection refused
```

**可能原因和解决方案**:

```python
# 诊断脚本
def diagnose_mongodb_connection():
    """诊断MongoDB连接问题"""

    import pymongo
    from pymongo.errors import ServerSelectionTimeoutError, ConfigurationError

    uri = os.getenv('MONGODB_URI', 'mongodb+srv://username:<EMAIL>/xi_memory')

    try:
        # 1. 检查URI格式
        client = pymongo.MongoClient(uri, serverSelectionTimeoutMS=5000)

        # 2. 测试连接
        client.admin.command('ping')
        print("✓ MongoDB连接正常")

        # 3. 检查数据库权限
        db_name = os.getenv('MONGODB_DB_NAME', 'xi_memory')
        db = client[db_name]
        db.test_collection.insert_one({'test': 'data'})
        db.test_collection.delete_one({'test': 'data'})
        print("✓ 数据库权限正常")

    except ServerSelectionTimeoutError:
        print("✗ MongoDB服务器无法访问")
        print("解决方案:")
        print("1. 检查MongoDB服务是否运行: sudo systemctl status mongod")
        print("2. 检查防火墙设置")
        print("3. 验证连接URI格式")

    except ConfigurationError as e:
        print(f"✗ 配置错误: {e}")
        print("解决方案:")
        print("1. 检查MONGODB_URI环境变量")
        print("2. 验证用户名密码")

    except Exception as e:
        print(f"✗ 未知错误: {e}")

# 运行诊断
diagnose_mongodb_connection()
```

#### 2. 嵌入模型加载失败

**错误信息**:
```
OSError: Can't load tokenizer for 'all-MiniLM-L6-v2'
```

**解决方案**:

```python
def fix_embedding_service_issues():
    """修复嵌入服务问题"""

    import os
    from xi_system.service import get_embedding_service

    try:
        # 1. 尝试获取嵌入服务
        embedding_service = get_embedding_service()
        health = embedding_service.health_check()

        if health['status'] == 'healthy':
            print("✓ 嵌入服务运行正常")
            print(f"   提供商: {health.get('provider_info', {}).get('provider_type')}")
            print(f"   模型: {health.get('provider_info', {}).get('model_name')}")
        else:
            print(f"✗ 嵌入服务异常: {health['message']}")

    except Exception as e:
        print(f"✗ 嵌入服务初始化失败: {e}")
        print("解决方案:")
        print("1. 检查EMBEDDING_PROVIDER配置")
        print("2. 如果使用local提供商，检查网络连接")
        print("3. 如果使用openai提供商，检查OPENAI_API_KEY")
        print("4. 查看服务日志获取详细错误信息")
```

#### 3. 内存使用过高

**症状**: 系统内存占用持续增长，可能导致OOM

**诊断和解决**:

```python
import psutil
import gc
from typing import Dict, Any

def monitor_memory_usage() -> Dict[str, Any]:
    """监控内存使用情况"""

    process = psutil.Process()
    memory_info = process.memory_info()

    stats = {
        'rss_mb': memory_info.rss / 1024 / 1024,  # 物理内存
        'vms_mb': memory_info.vms / 1024 / 1024,  # 虚拟内存
        'percent': process.memory_percent(),       # 内存占用百分比
        'gc_stats': {
            'generation_0': gc.get_count()[0],
            'generation_1': gc.get_count()[1],
            'generation_2': gc.get_count()[2],
        }
    }

    return stats

def optimize_memory_usage(provider: MongoProvider):
    """优化内存使用"""

    # 1. 清理嵌入模型缓存
    if hasattr(provider, '_embedding_model'):
        provider._embedding_model = None

    # 2. 强制垃圾回收
    gc.collect()

    # 3. 限制批处理大小
    provider.batch_size = min(provider.batch_size, 32)

    # 4. 清理连接池
    if provider.client:
        provider.client.close()
        provider.connect()

    print("内存优化完成")

# 内存监控装饰器
def memory_monitor(func):
    """内存监控装饰器"""
    def wrapper(*args, **kwargs):
        before = monitor_memory_usage()
        result = func(*args, **kwargs)
        after = monitor_memory_usage()

        memory_diff = after['rss_mb'] - before['rss_mb']
        if memory_diff > 100:  # 超过100MB增长
            print(f"警告: {func.__name__} 内存增长 {memory_diff:.1f}MB")

        return result
    return wrapper
```

#### 4. 检索性能问题

**症状**: 记忆检索速度慢，响应时间长

**性能优化**:

```python
import time
from functools import wraps

def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()

        execution_time = end_time - start_time
        if execution_time > 1.0:  # 超过1秒
            print(f"性能警告: {func.__name__} 执行时间 {execution_time:.2f}s")

        return result
    return wrapper

class OptimizedRetriever(Retriever):
    """优化的检索器"""

    def __init__(self, provider: MemoryProvider):
        super().__init__(provider)
        self.embedding_cache = {}  # 嵌入缓存
        self.query_cache = {}      # 查询缓存

    @performance_monitor
    def retrieve_memories_optimized(
        self,
        query: str,
        collection: str = "conversations",
        limit: int = 3,
        use_cache: bool = True
    ) -> List[MemoryRecord]:
        """优化的记忆检索"""

        # 1. 检查查询缓存
        cache_key = f"{query}:{collection}:{limit}"
        if use_cache and cache_key in self.query_cache:
            return self.query_cache[cache_key]

        # 2. 生成或获取缓存的嵌入
        if query in self.embedding_cache:
            query_embedding = self.embedding_cache[query]
        else:
            query_embedding = self.embedding_model.encode([query])[0]
            self.embedding_cache[query] = query_embedding

        # 3. 执行向量搜索
        results = self.provider.search_by_embedding(
            collection_name=collection,
            embedding=query_embedding.tolist(),
            limit=limit * 2  # 获取更多候选，后续过滤
        )

        # 4. 后处理和排序
        filtered_results = self._post_process_results(results, query)[:limit]

        # 5. 缓存结果
        if use_cache:
            self.query_cache[cache_key] = filtered_results

        return filtered_results

    def clear_cache(self):
        """清理缓存"""
        self.embedding_cache.clear()
        self.query_cache.clear()

# 性能测试工具
def benchmark_retrieval_performance():
    """基准测试检索性能"""

    provider = MongoProvider(uri="mongodb+srv://username:<EMAIL>/xi_memory", db_name="xi_memory")
    provider.connect()

    retriever = OptimizedRetriever(provider)

    test_queries = [
        "Python编程",
        "机器学习算法",
        "数据库设计",
        "系统架构",
        "性能优化"
    ]

    # 预热
    for query in test_queries:
        retriever.retrieve_memories_optimized(query, limit=3)

    # 基准测试
    start_time = time.time()
    for _ in range(10):
        for query in test_queries:
            retriever.retrieve_memories_optimized(query, limit=3)

    total_time = time.time() - start_time
    avg_time = total_time / (10 * len(test_queries))

    print(f"平均检索时间: {avg_time:.3f}s")
    print(f"每秒查询数: {1/avg_time:.1f}")
```

### 调试技巧和日志分析

#### 启用详细日志

```python
import logging

def setup_memory_logging():
    """设置记忆系统日志"""

    # 创建记忆系统专用logger
    memory_logger = logging.getLogger('xi_system.memory')
    memory_logger.setLevel(logging.DEBUG)

    # 创建文件处理器
    file_handler = logging.FileHandler('logs/memory_debug.log')
    file_handler.setLevel(logging.DEBUG)

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 设置格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器
    memory_logger.addHandler(file_handler)
    memory_logger.addHandler(console_handler)

    return memory_logger

# 使用示例
logger = setup_memory_logging()

class DebuggableMongoProvider(MongoProvider):
    """支持调试的MongoDB提供者"""

    def store(self, collection_name: str, record: MemoryRecord) -> str:
        logger.debug(f"存储记录到 {collection_name}: {record.content[:50]}...")

        start_time = time.time()
        result = super().store(collection_name, record)
        execution_time = time.time() - start_time

        logger.debug(f"存储完成，耗时 {execution_time:.3f}s，ID: {result}")
        return result

    def retrieve(self, collection_name: str, record_id: str) -> Optional[MemoryRecord]:
        logger.debug(f"检索记录 {record_id} 从 {collection_name}")

        result = super().retrieve(collection_name, record_id)

        if result:
            logger.debug(f"检索成功: {result.content[:50]}...")
        else:
            logger.warning(f"记录不存在: {record_id}")

        return result
```

### 性能优化建议

#### 1. 数据库索引优化

```python
def create_optimized_indexes(provider: MongoProvider):
    """创建优化的数据库索引"""

    db = provider.db

    # 1. 时间戳索引（用于时间范围查询）
    db.conversation_memory.create_index([("timestamp", -1)])

    # 2. 会话ID索引（用于会话查询）
    db.conversation_memory.create_index([("source_session_id", 1)])

    # 3. 复合索引（会话+时间）
    db.conversation_memory.create_index([
        ("source_session_id", 1),
        ("timestamp", -1)
    ])

    # 4. 角色索引（用于角色过滤）
    db.conversation_memory.create_index([("role.internal_name", 1)])

    # 5. 元数据索引（用于标签查询）
    db.conversation_memory.create_index([("metadata.tags", 1)])

    # 6. 向量搜索索引（Atlas Vector Search）
    vector_index = {
        "name": "vector_index",
        "definition": {
            "fields": [
                {
                    "type": "vector",
                    "path": "embedding",
                    "numDimensions": 384,
                    "similarity": "cosine"
                }
            ]
        }
    }

    try:
        db.conversation_memory.create_search_index(vector_index)
        print("向量搜索索引创建成功")
    except Exception as e:
        print(f"向量索引创建失败: {e}")

    print("数据库索引优化完成")
```

#### 2. 连接池优化

```python
from pymongo import MongoClient
from pymongo.pool import Pool

def create_optimized_mongo_client(uri: str) -> MongoClient:
    """创建优化的MongoDB客户端"""

    return MongoClient(
        uri,
        # 连接池设置
        maxPoolSize=20,           # 最大连接数
        minPoolSize=5,            # 最小连接数
        maxIdleTimeMS=30000,      # 最大空闲时间

        # 超时设置
        serverSelectionTimeoutMS=5000,   # 服务器选择超时
        connectTimeoutMS=10000,          # 连接超时
        socketTimeoutMS=20000,           # 套接字超时

        # 重试设置
        retryWrites=True,         # 启用写重试
        retryReads=True,          # 启用读重试

        # 压缩设置
        compressors=['zstd', 'zlib', 'snappy'],

        # 读写设置
        readPreference='secondaryPreferred',  # 优先从副本读取
        writeConcern={'w': 1, 'j': True}      # 写确认设置
    )
```

#### 3. 批量操作优化

```python
class BatchOptimizedProvider(MongoProvider):
    """批量优化的提供者"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.batch_buffer = []
        self.batch_size = 100

    def store_batch_optimized(
        self,
        collection_name: str,
        records: List[MemoryRecord]
    ) -> List[str]:
        """优化的批量存储"""

        if not records:
            return []

        collection = self.db[collection_name]
        documents = []

        # 批量生成嵌入
        contents = [record.content for record in records]
        embeddings = self.embedding_model.encode(contents, batch_size=32)

        # 准备文档
        for record, embedding in zip(records, embeddings):
            document = self._to_document(record)
            document['embedding'] = embedding.tolist()
            documents.append(document)

        # 批量插入
        result = collection.insert_many(documents, ordered=False)

        return [str(id) for id in result.inserted_ids]
```

### 监控指标和告警

```python
from dataclasses import dataclass
from typing import Dict, List
import time

@dataclass
class MemorySystemMetrics:
    """记忆系统监控指标"""

    # 性能指标
    avg_retrieval_time: float = 0.0
    avg_storage_time: float = 0.0
    cache_hit_rate: float = 0.0

    # 容量指标
    total_memories: int = 0
    archived_memories: int = 0
    memory_usage_mb: float = 0.0

    # 错误指标
    connection_errors: int = 0
    storage_errors: int = 0
    retrieval_errors: int = 0

    def to_dict(self) -> Dict[str, float]:
        """转换为字典格式"""
        return {
            'avg_retrieval_time': self.avg_retrieval_time,
            'avg_storage_time': self.avg_storage_time,
            'cache_hit_rate': self.cache_hit_rate,
            'total_memories': self.total_memories,
            'archived_memories': self.archived_memories,
            'memory_usage_mb': self.memory_usage_mb,
            'connection_errors': self.connection_errors,
            'storage_errors': self.storage_errors,
            'retrieval_errors': self.retrieval_errors
        }

class MemorySystemMonitor:
    """记忆系统监控器"""

    def __init__(self, provider: MemoryProvider):
        self.provider = provider
        self.metrics = MemorySystemMetrics()
        self.start_time = time.time()

    def collect_metrics(self) -> MemorySystemMetrics:
        """收集系统指标"""

        # 收集容量指标
        try:
            if isinstance(self.provider, MongoProvider):
                db = self.provider.db
                self.metrics.total_memories = db.conversation_memory.count_documents({})
                self.metrics.archived_memories = db.archived_memories.count_documents({})
        except Exception as e:
            logger.error(f"指标收集失败: {e}")

        # 收集内存使用
        self.metrics.memory_usage_mb = monitor_memory_usage()['rss_mb']

        return self.metrics

    def check_alerts(self) -> List[str]:
        """检查告警条件"""
        alerts = []

        if self.metrics.avg_retrieval_time > 2.0:
            alerts.append(f"检索时间过长: {self.metrics.avg_retrieval_time:.2f}s")

        if self.metrics.memory_usage_mb > 1000:
            alerts.append(f"内存使用过高: {self.metrics.memory_usage_mb:.1f}MB")

        if self.metrics.connection_errors > 10:
            alerts.append(f"连接错误过多: {self.metrics.connection_errors}")

        if self.metrics.cache_hit_rate < 0.5:
            alerts.append(f"缓存命中率过低: {self.metrics.cache_hit_rate:.2f}")

        return alerts

# 使用示例
monitor = MemorySystemMonitor(provider)
metrics = monitor.collect_metrics()
alerts = monitor.check_alerts()

if alerts:
    for alert in alerts:
        logger.warning(f"告警: {alert}")
```

通过以上全面的故障排除指南，开发者可以快速诊断和解决Xi记忆系统中的常见问题，确保系统稳定高效运行。

---

## 总结

Xi 记忆管理系统作为曦智能体的核心组件，提供了完整的记忆存储、检索、整理和管理能力。

记忆系统的设计遵循"领域模型优先"原则，确保了代码的可维护性和可扩展性。随着Xi系统的不断发展，记忆管理系统将继续演进，为智能体提供更强大的认知能力支持。
