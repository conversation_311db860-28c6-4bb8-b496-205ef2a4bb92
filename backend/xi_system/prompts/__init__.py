"""
Prompt System - Dynamic XML-Structured Prompt Generation

Provides dynamic XML-structured prompt generation for optimal LLM performance.
Combines persona definitions with contextual information for rich system prompts.

Components:
- builder.py: StructuredPromptBuilder for XML-based prompt construction

Key Features:
- XML-structured prompts for better LLM comprehension
- Dynamic context integration (memory, notes, tools)
- Persona-based prompt customization
- Template-based prompt construction
- Modular prompt components

Usage:
    from xi_system.prompts import StructuredPromptBuilder

    builder = StructuredPromptBuilder()
    prompt = builder.build_system_prompt(
        user_input="Hello",
        retrieved_memories=[...],
        available_tools=[...]
    )
"""

from .builder import StructuredPromptBuilder

__all__ = [
    'StructuredPromptBuilder'
]
