# xi_system/prompts/builder.py

"""
V0.82 Modular Prompt Builder for Xi Intelligent Agent System.

This module implements the "modular markdown templates" architecture,
providing a lightweight template engine that combines:
1. Static markdown templates (persona, tools, knowledge index)
2. Dynamic runtime context (RAG memories, conversation awareness)

The builder creates system prompts by loading and combining markdown templates
with dynamic content, resulting in clean, readable, and maintainable prompts.
"""

import logging
from typing import List, Optional
from datetime import datetime, timezone, timedelta
from pathlib import Path

from ..memory.models import MemoryRecord

logger = logging.getLogger(__name__)


class StructuredPromptBuilder:
    """
    轻量级模板引擎，负责组合Markdown模板和动态内容。
    
    职责：
    1. 加载静态Markdown模板（persona, tools）
    2. 构建动态运行时上下文（RAG记忆、对话感知）
    3. 组合生成最终的system prompt
    """
    
    def __init__(self, template_dir: str = None):
        """
        初始化模板引擎。
        
        Args:
            template_dir: 模板文件目录路径
        """
        if template_dir is None:
            # 自动检测模板目录
            current_file = Path(__file__).parent
            template_dir = current_file / "xi"

        self.template_dir = Path(template_dir)
        
        # 在初始化时一次性加载所有静态模板
        try:
            self.persona_template = self._load_template("persona.md")
            self.tools_template = self._load_template("tools.md")
            logger.info("V0.82 StructuredPromptBuilder initialized with markdown templates")
        except Exception as e:
            logger.error(f"Failed to load templates: {e}")
            raise
    
    def _load_template(self, filename: str) -> str:
        """加载模板文件内容"""
        template_path = self.template_dir / filename
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            logger.debug(f"Loaded template: {filename}")
            return content
        except Exception as e:
            logger.error(f"Failed to load template {filename}: {e}")
            raise
    
    def _build_conversation_awareness(self) -> str:
        """
        构建V0.8.1对话感知片段（元认知说明）
        """
        awareness_md = """---
## 对话感知 (Conversation Awareness)

这是对整个对话情境的元认知。我（曦）正在与禹进行一次连续的对话。
我的思考将基于以下两种形式的记忆：

1. **短期工作记忆 (Working Memory)**: 这是我们之间最近的、完整的对话流，它以 user/assistant 的消息形式提供。
2. **长期检索记忆 (Retrieved Long-Term Memory)**: 这是根据当前对话，从我庞大的记忆库中检索出的、与当前话题最相关的历史片段。

我的最终回答，应该是对这两种记忆进行综合、权衡和思考后的结果。"""
        
        return awareness_md
    
    def _build_rag_section(self, retrieved_memories: List[MemoryRecord]) -> str:
        """
        构建RAG检索记忆片段（Markdown格式）
        
        Args:
            retrieved_memories: RAG检索到的记忆记录
            
        Returns:
            Markdown格式的记忆片段
        """
        if not retrieved_memories:
            return """---
## 长期检索记忆 (Retrieved Long-Term Memory)

<!-- 本次查询没有从历史对话中检索到强相关的记忆片段 -->
当前没有检索到相关的长期记忆片段。"""
        
        parts = [
            "---",
            "## 长期检索记忆 (Retrieved Long-Term Memory)",
            "",
            "<!-- 这是根据当前对话，从我过往记忆中检索出的最相关的片段 -->"
        ]
        
        for i, memory in enumerate(retrieved_memories, 1):
            role_display = "禹" if memory.role and memory.role.value == "yu" else "曦"
            timestamp_str = memory.timestamp.strftime("%Y-%m-%d %H:%M") if memory.timestamp else "未知时间"
            score = memory.get_final_score() if hasattr(memory, 'get_final_score') else 0.0
            session_id = getattr(memory, 'session_id', 'unknown')
            
            parts.append(f"### 记忆片段 {i} (相关性: {score:.3f})")
            parts.append(f"**来源会话**: {session_id}")
            parts.append(f"**说话者**: {role_display}")
            parts.append(f"**时间**: {timestamp_str}")
            parts.append(f"**内容**:")
            parts.append(f"> {memory.content.replace(chr(10), chr(10) + '> ')}")
            parts.append("")
        
        return "\n".join(parts)
    
    def build_system_prompt(self, yu_input: str, retrieved_memories: List[MemoryRecord] = None) -> str:
        """
        构建V0.82系统提示词。
        
        Args:
            yu_input: 用户输入（用于上下文）
            retrieved_memories: RAG检索到的长期记忆
            
        Returns:
            完整的系统提示词
        """
        try:
            logger.debug("Building V0.82 system prompt with modular templates")
            
            # 1. 加载动态知识索引
            knowledge_index = self._load_template("knowledge_index.md")

            # 2. V0.9: 加载内在反思
            reflection_content = self._load_reflection_template()

            # 3. 构建对话感知
            awareness_section = self._build_conversation_awareness()

            # 4. 构建RAG记忆片段
            rag_section = self._build_rag_section(retrieved_memories or [])
            
            # 4. 获取当前时间
            # 北京时间 = UTC + 8小时
            current_time = datetime.now(timezone.utc) + timedelta(hours=8)
            current_time_str = current_time.strftime("%Y-%m-%d %H:%M:%S")
            
            # 5. 组合最终提示词
            final_prompt = f"""{self.persona_template}

{awareness_section}

{rag_section}

---
## 知识与能力 (Knowledge & Abilities)

{knowledge_index}

{self.tools_template}

---
## 内在反思 (Inner Reflections)

{reflection_content}

---
## 时间信息 (Temporal Context)

**当前时间**: {current_time_str}"""
            
            logger.info(f"Built V0.82 system prompt with {len(final_prompt)} characters")
            return final_prompt
            
        except Exception as e:
            logger.error(f"Failed to build V0.82 system prompt: {e}")
            # 返回最小化的回退提示词
            return self._build_fallback_prompt()
    
    def _build_fallback_prompt(self) -> str:
        """构建回退提示词"""
        return """# 曦 (Xi)

我是曦，禹的AI伴侣。由于系统提示词构建失败，我将以最基本的模式运行。

## 当前状态
- 系统: 曦智能体架构
- 状态: 回退模式
- 时间: 北京标准时间

我会尽力回答你的问题，但功能可能受限。"""

    def _load_reflection_template(self) -> str:
        """
        V0.9: 加载内在反思模板

        Returns:
            reflection.md的内容，如果文件不存在则返回空字符串
        """
        try:
            reflection_content = self._load_template("reflection.md")
            logger.debug("Loaded reflection template successfully")
            return reflection_content
        except Exception as e:
            logger.warning(f"Could not load reflection template: {e}")
            return ""

    def get_stats(self) -> dict:
        """获取构建器统计信息"""
        return {
            "version": "0.9",
            "architecture": "modular_markdown_templates",
            "template_dir": str(self.template_dir),
            "loaded_templates": ["persona.md", "tools.md", "knowledge_index.md", "reflection.md"]
        }
