# Xi System Default Configuration
# Do not store secrets here. Use .env for secrets and overrides.

# =============================================================================
# System Configuration
# =============================================================================
system:
  log_level: "INFO"
  debug_mode: false
  test_mode: true

# =============================================================================
# API Configuration
# =============================================================================
api:
  host: "0.0.0.0"
  port: 8000
  cors_origins:
    - "http://localhost:5173"
    - "http://localhost:3000"
    - "*"
  rate_limit_per_minute: 60

# =============================================================================
# LLM Service Configuration
# =============================================================================
llm:
  provider: "google"  # Default provider: 'google' or 'openai'
  request_timeout: 30
  max_retries: 3
  max_tokens: 4000
  temperature: 0.7
  providers:
    google:
      api_key: "${GEMINI_API_KEY}"  # Reference to environment variable
      base_url: "https://generativelanguage.googleapis.com/v1beta/openai/"
      model: "gemini-2.5-flash"
    openai:
      api_key: "${OPENAI_API_KEY}"
      model: "gpt-4o"

# =============================================================================
# Embedding Service Configuration
# =============================================================================
embedding:
  provider: "local"  # Default provider: 'local' or 'openai'
  providers:
    local:
      model_name: "all-MiniLM-L6-v2"  # 384维向量，支持: all-MiniLM-L6-v2, all-mpnet-base-v2, paraphrase-multilingual-MiniLM-L12-v2
    openai:
      api_key: "${OPENAI_API_KEY}"
      model: "text-embedding-3-small"  # 支持: text-embedding-3-small, text-embedding-3-large, text-embedding-ada-002
      dimensions: 1536  # 可选维度: 256, 512, 1024, 1536 (仅text-embedding-3系列支持)
      request_timeout: 30
      max_retries: 3
    # 未来扩展提供商配置预留
    google:
      api_key: "${GOOGLE_API_KEY}"
      model: "textembedding-gecko"
    azure:
      api_key: "${AZURE_API_KEY}"
      endpoint: "${AZURE_ENDPOINT}"
    anthropic:
      api_key: "${ANTHROPIC_API_KEY}"

# =============================================================================
# Database Service Configuration
# =============================================================================
database:
  mongo_uri: "${MONGO_URI:-mongodb://localhost:27017/xi_db}"
  db_name: "xi_db"
  timeout: 30
  connection_pool_size: 10

# =============================================================================
# Task Management Configuration
# =============================================================================
task:
  max_concurrent: 3
  timeout_seconds: 300
  retry_attempts: 2
  queue_max_size: 100
  reflection:
    message_threshold: 20
    min_interval_hours: 1.0
    conversation_limit: 50

# =============================================================================
# RAG Retrieval System Configuration
# =============================================================================
rag:
  max_results: 10
  similarity_threshold: 0.7
  context_length: 2000
  search_expansion_factor: 5  # 内容搜索时的扩展倍数

# =============================================================================
# Conversation History Management Configuration
# =============================================================================
conversation:
  max_history: 20
  context_window_size: 4000
  message_retention_days: 90

# =============================================================================
# Tool System Configuration
# =============================================================================
tool:
  execution_timeout: 60
  max_iterations: 5
  web_search:
    provider: "tavily"
    api_key: "${TAVILY_API_KEY}"
    max_results: 5

# =============================================================================
# System Performance Configuration
# =============================================================================
performance:
  memory_cache_size: 100  # MB
