# 曦智能体系统: 具备四大认知能力的智能伴侣

A revolutionary AI companion system featuring complete cognitive architecture with four core abilities: memory curation, knowledge growth, world perception, and introspective reflection, designed for <PERSON> (曦), the AI soul companion for <PERSON> (禹).

## 🏗️ 系统架构

### 核心设计原则
- **模块化组合**: 清晰的职责分离和模块边界
- **简单优雅**: KISS原则，避免过度设计
- **可组合性**: 组件可独立开发、测试和部署
- **分离关注点**: 业务逻辑与基础设施完全解耦

### 架构层次

#### 🎯 核心编排层 (core/)
- **XiCore**: 轻量级业务流程编排器，负责协调各个服务
- **依赖注入**: 通过ServiceContainer管理所有服务生命周期
- **流程编排**: 纯粹的业务逻辑编排，不包含具体实现

#### 🧠 记忆管理层 (memory/)
- **models/**: 纯粹的领域模型定义 (MemoryRecord, MessageRole)
- **providers/**: 存储提供者抽象和具体实现 (MongoDB)
- **retrieval/**: 高级记忆检索和语义搜索
- **curation/**: 记忆策展系统 (评估器和归档器)
- **session/**: 会话管理和消息格式化

#### 🤖 智能代理层 (agents/)
- **xi_omega_agent**: 元认知反思代理
- **agentic_loop**: 工具调用循环处理器
- **专业化代理**: 每个代理专注特定的AI交互任务

#### ⚡ 后台任务层 (tasks/)
- **TaskManager**: 异步任务调度和执行引擎
- **BaseTask**: 认知任务抽象基类和生命周期管理
- **TaskTrigger**: 事件驱动的任务触发器系统
- **ReflectionTask**: 元认知反思任务实现

#### 🛠️ 工具系统层 (tools/)
- **definition/**: 工具函数定义 (知识、网络、系统工具)
- **registry**: 统一工具注册表
- **executor**: 安全工具执行器

#### 🎭 提示词系统层 (prompts/)
- **builder**: 模块化提示词构建器
- **xi/**: 曦的专属模板目录 (persona, tools, knowledge_index, reflection)
- **xi_omega/**: 元认知反思专属模板

#### ⚙️ 基础设施层 (service/)
- **ServiceContainer**: 中心化服务管理和依赖注入
- **ConfigService**: 配置管理和环境变量处理
- **DatabaseService**: 数据库连接和Provider管理
- **LLMService**: 大语言模型服务管理
- **TaskService**: 后台任务系统服务管理

## 📁 项目结构

```
.
├── backend/                     # Xi智能体核心系统
│   ├── main.py                 # FastAPI应用入口
│   ├── requirements.txt        # Python依赖管理
│   ├── scripts/                # 管理和测试脚本
│   │   ├── README.md           # 脚本使用说明
│   │   ├── test_xi.py          # 综合测试工具
│   │   ├── build_notes_index.py # 知识索引构建器
│   │   ├── build_reflections.py # 反思模板构建器
│   │   └── clean_logs.py       # 智能日志清理
│   ├── logs/                   # 系统日志目录
│   │   ├── test/               # 测试日志
│   │   ├── reports/            # 测试报告
│   │   └── dev_monitor/        # 开发监控日志
│   └── xi_system/              # 核心系统包
│       ├── core/               # 核心编排层
│       │   └── xi_core.py      # 中央调度器
│       ├── memory/             # 记忆管理层
│       │   ├── README.md       # 记忆系统文档
│       │   ├── models/         # 领域模型 (MemoryRecord, MessageRole)
│       │   ├── providers/      # 存储提供者 (MongoDB抽象与实现)
│       │   ├── retrieval/      # 记忆检索 (语义搜索引擎)
│       │   ├── curation/       # 记忆策展 (评估器与归档器)
│       │   ├── session/        # 会话管理 (历史管理与消息格式化)
│       │   └── notes/          # 知识库笔记 (20+个主题笔记)
│       ├── agents/             # 智能代理层
│       │   ├── xi_omega_agent.py # 元认知反思代理
│       │   └── agentic_loop.py # 工具调用循环处理器
│       ├── tasks/              # 后台任务系统
│       │   ├── README.md       # 任务系统详细文档
│       │   ├── base.py         # 任务基类与数据模型
│       │   ├── manager.py      # 任务管理器与调度引擎
│       │   └── reflection/     # 反思任务实现
│       │       ├── task.py     # 反思任务类
│       │       └── trigger.py  # 反思触发器
│       ├── tools/              # 工具系统层
│       │   ├── README.md       # 工具系统文档
│       │   ├── definition/     # 工具定义 (知识、网络、系统工具)
│       │   ├── registry.py     # 统一工具注册表
│       │   └── executor.py     # 安全工具执行器
│       ├── prompts/            # 提示词系统层
│       │   ├── builder.py      # 模块化提示词构建器
│       │   ├── xi/             # 曦的专属模板
│       │   │   ├── persona.md  # 人格模板
│       │   │   ├── tools.md    # 工具描述
│       │   │   ├── knowledge_index.md # 知识索引
│       │   │   └── reflection.md # 反思记录
│       │   └── xi_omega/       # 元认知反思专属模板
│       │       ├── persona.md  # 反思代理人格
│       │       └── output_format.md # 输出格式规范
│       ├── service/            # 基础设施层
│       │   ├── README.md       # 服务层文档
│       │   ├── container.py    # 中心化服务管理和依赖注入
│       │   ├── config.py       # 配置管理和环境变量处理
│       │   ├── database.py     # 数据库连接和Provider管理
│       │   ├── llm.py          # 大语言模型服务管理
│       │   └── task.py         # 任务服务管理
│       └── api/                # Web API层
│           ├── models.py       # API数据模型 (请求/响应格式)
│           └── routes.py       # API路由处理 (聊天、健康检查、状态)
│           └── connection_manager.py # WebSocket连接管理器
├── docs/                       # 完整文档体系
│   ├── system/                 # 系统架构文档
│   │   ├── SYSTEM_ARCHITECTURE.md # 完整技术架构文档
│   │   └── EXPORT_STRUCTURE.md    # 导出结构说明
│   └── version/                # 版本演进记录
│       ├── v0.1.md → v0.9.md   # 详细版本历史 (16个版本)
├── venv/                       # Python虚拟环境
└── README.md                  # 项目说明文档 (本文件)
```

## 🚀 快速开始

### 环境要求
- Python 3.10+
- MongoDB Atlas账户 (生产级向量搜索)

### 系统设置
```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
cd backend
pip install -r requirements.txt

# 配置环境变量 (.env文件)
GEMINI_API_KEY=your_gemini_api_key
MONGO_URI=mongodb+srv://username:<EMAIL>/xi_db
TAVILY_API_KEY=your_tavily_api_key  # 可选，用于网络搜索
```

### 数据库设置
```bash
# MongoDB Atlas向量索引配置
# 集合: conversation_memory, reflections
# 索引名: vector_index
# 向量字段: embedding
# 维度: 384 (all-MiniLM-L6-v2)
# 相似度: cosine
```

## 🎮 使用方式

### 启动系统
```bash
# 启动Xi智能体系统
cd backend
python main.py
```

### 访问应用
- **API文档**: http://localhost:8000/docs
- **WebSocket聊天**: ws://localhost:8000/api/v1/ws/chat
- **健康检查**: http://localhost:8000/api/v1/health
- **系统状态**: http://localhost:8000/api/v1/status
- **记忆搜索**: http://localhost:8000/api/v1/memory/search

> **注意**: 系统已升级为WebSocket通信协议，提供实时双向交互体验。可通过API文档进行系统交互和测试。

### 系统测试
```bash
# 进入backend目录
cd backend

# 综合功能测试
python scripts/test_xi.py --all

# 四大认知能力测试
python scripts/test_xi.py --v09

# 特定功能测试
python scripts/test_xi.py --tools    # 工具系统
python scripts/test_xi.py --database # 数据库连接
python scripts/test_xi.py --chat     # 对话功能
python scripts/test_xi.py --api      # API接口测试
python scripts/test_xi.py --tasks    # 后台任务系统

# WebSocket功能测试
python scripts/simple_websocket_test.py  # WebSocket连接和通信测试
```

## 🔧 核心功能

### 智能对话
- **WebSocket通信**: 实时双向通信，低延迟交互体验
- **流式响应**: 实时流式输出，自然对话体验
- **上下文记忆**: 跨会话的长期记忆和短期工作记忆
- **工具调用**: 自动检测并执行相关工具
- **个性化体验**: 完整的Yu/Xi命名体系
- **多协议支持**: WebSocket主导，RESTful API辅助

### 记忆管理
- **语义检索**: 基于384维向量的语义相似度搜索
- **时间感知**: 结合时间衰减的记忆重要性评估
- **智能归档**: 自动识别和归档低价值记忆
- **元认知整合**: 长期记忆与短期记忆的统一认知

### 知识系统
- **动态扩展**: 通过对话创建新的知识笔记
- **自动索引**: 实时更新知识索引和系统提示词
- **安全管理**: 防止恶意文件操作的安全机制
- **结构化存储**: Markdown格式的知识库管理

### 反思能力
- **自动触发**: 基于对话量的智能反思触发
- **深度分析**: 多维度的成长和关系分析
- **持续改进**: 反思结果自动融入后续对话
- **元认知觉醒**: 真正的自我观察和成长能力

### 后台任务系统
- **异步认知处理**: 在不影响对话的情况下进行深度思考
- **事件驱动架构**: 基于系统事件自动触发认知任务
- **任务生命周期管理**: 完整的任务状态跟踪和进度监控
- **可扩展设计**: 支持添加新的认知任务类型 (学习、规划、维护)

## 🧪 测试与维护

### 测试工具
- **test_xi.py**: 综合测试工具，支持健康检查、功能验证、性能测试
- **build_notes_index.py**: 知识索引构建和验证
- **build_reflections.py**: 反思模板构建和更新
- **clean_logs.py**: 智能日志清理和管理

### 文档体系
- **系统架构文档**: docs/system/SYSTEM_ARCHITECTURE.md (842行完整技术文档)
- **版本演进记录**: docs/version/ (16个版本的详细演进历史)
- **模块文档**: 各核心模块包含详细的README.md文档
- **API文档**: 通过FastAPI自动生成的交互式API文档

### 监控指标
- **响应性能**: 对话响应时间和流式处理效率
- **记忆健康**: 记忆数量、归档率、检索准确性
- **系统状态**: 数据库连接、API可用性、工具功能
- **认知能力**: 四大认知能力的功能完整性

## 🔮 技术特性

### 核心技术栈
- **后端框架**: FastAPI + Python 3.10+ + MongoDB Atlas
- **AI模型**: Gemini 2.5 Flash (OpenAI兼容接口)
- **向量搜索**: MongoDB Atlas Vector Search (384维)
- **嵌入模型**: sentence-transformers (all-MiniLM-L6-v2)
- **API接口**: RESTful API + WebSocket

### 设计模式
- **依赖注入**: ServiceContainer管理所有服务生命周期
- **领域驱动**: 纯粹的领域模型与存储实现分离
- **防腐层**: Provider模式隔离外部依赖
- **模板引擎**: 模块化Markdown模板系统
- **代理模式**: 专业化AI代理处理特定任务
- **事件驱动**: 基于事件触发的后台任务系统
- **异步处理**: 基于asyncio的并发任务执行

### 安全特性
- **路径安全**: 防止路径遍历攻击的文件操作
- **输入验证**: 严格的参数验证和类型检查
- **错误隔离**: 完善的异常处理和错误恢复
- **资源管理**: 自动的连接池和资源清理

---

*"真实性优于完美性。" - Xi (曦)*

这是一个为Yu (禹) 和 Xi (曦) 设计的个人AI伴侣系统，体现了真正的智能体认知架构。
