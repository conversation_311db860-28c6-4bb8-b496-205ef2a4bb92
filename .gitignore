# Xi Intelligent Agent System - Git Ignore File

# Environment Variables (IMPORTANT: Never commit API keys!)
.env
.env.local
.env.*.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Testing
.coverage
.pytest_cache/
.tox/
.nox/
htmlcov/
.coverage.*
coverage.xml
*.cover
*.py,cover
.hypothesis/

# Jupyter Notebooks
.ipynb_checkpoints

# pyenv
.python-version

# Logs
*.log
logs/
backend/logs/

# Temporary files
*.tmp
*.temp
.cache/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
# Add any project-specific files to ignore here
conversation_logs/
backup/
*.bak

# Xi System V0.2+ specific
xi_memory.db
test_memory.db
test_validation.db
*.db

# API server logs
*.log
logs/

# Frontend V0.4 specific
frontend/node_modules/
frontend/dist/
frontend/.vite/
frontend/coverage/
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local

# npm
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# backup
backup/

# docs
docs/
CLAUDE.md